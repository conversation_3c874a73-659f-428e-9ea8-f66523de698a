# Chrome V3插件开发完整指南
## Vue3 + Ant Design Vue + Manifest V3

> 本指南专注于使用Vue3和Ant Design Vue开发Chrome扩展时的核心技术要求、常见问题解决方案和最佳实践。

---

## 🎯 核心架构概览

### 双环境执行模型
Chrome V3插件运行在两个独立的执行环境中，理解这一点是成功开发的关键：

- **MAIN World（页面主环境）**: 能拦截真实网络请求，获取认证信息
- **ISOLATED World（隔离环境）**: 能使用Chrome API，处理扩展逻辑

### 技术栈配置
```
Vue 3 + Ant Design Vue + TypeScript
├── Manifest V3 (Service Worker)
├── Content Scripts (双环境)
├── Popup/Options (Vue组件)
└── 构建工具 (Vite/Webpack)
```

---

## 🚨 关键限制与解决方案

### 1. 内容安全策略 (CSP) 限制

#### ❌ 禁用的功能
- 内联脚本: `<script>alert('hello')</script>`
- 内联事件: `<button onclick="fn()">` 
- 动态代码执行: `eval()`, `new Function()`
- 运行时样式插入: 动态`<style>`标签

#### ✅ 解决方案

**A. Vue模板预编译**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      input: {
        popup: 'src/popup/index.html',
        options: 'src/options/index.html',
        'content-script': 'src/content-script/main.ts',
        'service-worker': 'src/background/service-worker.ts'
      }
    }
  },
  plugins: [
    vue(), // 确保模板完全预编译
  ]
})
```

**B. Ant Design Vue 样式处理**
```typescript
// main.ts - 静态导入所有样式
import 'ant-design-vue/dist/reset.css'
import { createApp } from 'vue'
import Antd from 'ant-design-vue'

const app = createApp(App)
app.use(Antd)
```

**C. Manifest CSP配置**
```json
{
  "content_security_policy": {
    "extension_pages": "script-src 'self'; style-src 'self' 'unsafe-inline'; object-src 'self';",
    "sandbox": "sandbox allow-scripts allow-forms allow-popups allow-modals;"
  }
}
```

### 2. Service Worker (后台脚本)

#### 核心变化
- **非持久性**: 闲置时自动终止
- **无DOM访问**: 不能使用`window`, `document`
- **状态丢失**: 变量在重启后丢失

#### 最佳实践
```typescript
// service-worker.ts
class ServiceWorker {
  constructor() {
    this.setupEventListeners()
  }

  private setupEventListeners() {
    // ✅ 在构造函数中注册所有监听器
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this))
    chrome.action.onClicked.addListener(this.handleActionClick.bind(this))
  }

  private async handleMessage(request: any, sender: any, sendResponse: Function) {
    try {
      // ✅ 每次都从存储获取数据，不依赖内存变量
      const userData = await chrome.storage.local.get('userData')
      
      switch (request.action) {
        case 'GET_USER_DATA':
          sendResponse({ success: true, data: userData })
          break
        case 'SAVE_USER_DATA':
          await chrome.storage.local.set({ userData: request.data })
          sendResponse({ success: true })
          break
      }
    } catch (error) {
      sendResponse({ success: false, error: error.message })
    }
    return true // 保持异步响应通道
  }
}

new ServiceWorker()
```

---

## 🌍 双环境架构实现

### 理解执行环境差异

| 特性 | MAIN World | ISOLATED World |
|------|------------|----------------|
| Chrome API | ❌ 无法使用 | ✅ 完全访问 |
| 页面变量 | ✅ 可访问 | ❌ 无法访问 |
| 真实请求拦截 | ✅ 可拦截 | ❌ 只能拦截自身 |
| 认证信息获取 | ✅ 可获取 | ❌ 无法获取 |
| DOM操作 | ✅ 完全访问 | ✅ 完全访问 |

### 实际实现案例

#### 1. 项目结构
```
src/
├── lib/
│   └── page-interceptor.js          # MAIN world 脚本
├── content-script/
│   └── unified-content.ts           # ISOLATED world 脚本
├── background/
│   └── service-worker.ts            # Service Worker
├── popup/
│   ├── index.html
│   ├── main.ts
│   └── App.vue                      # Vue组件
└── manifest.config.ts
```

#### 2. Manifest 配置
```typescript
// manifest.config.ts
export default defineManifest({
  manifest_version: 3,
  
  content_scripts: [
    {
      // MAIN world: 页面环境拦截器
      matches: ["*://example.com/*"],
      js: ["src/lib/page-interceptor.js"],
      run_at: "document_start",
      world: "MAIN"  // 🔑 关键配置
    },
    {
      // ISOLATED world: Content Script
      matches: ["*://example.com/*"],
      js: ["src/content-script/unified-content.ts"],
      run_at: "document_start"
      // 默认在ISOLATED world运行
    }
  ],

  web_accessible_resources: [
    {
      resources: ["src/lib/page-interceptor.js"],
      matches: ["<all_urls>"]
    }
  ]
})
```

#### 3. MAIN World 实现 (认证信息拦截)
```javascript
// page-interceptor.js
(function() {
  if (window.hasInterceptorInjected) return
  window.hasInterceptorInjected = true

  console.log('🚀 页面拦截器已注入')

  // 配置
  const CONFIG = {
    CACHE_PREFIX: 'ext_cache_',
    EXPIRE_TIME: 30 * 60 * 1000, // 30分钟
    TARGET_HEADERS: ['authorization', 'x-csrf-token', 'anti-content']
  }

  // 缓存管理
  const cache = {
    set(key, value, ttl = CONFIG.EXPIRE_TIME) {
      const expiry = Date.now() + ttl
      const cacheKey = CONFIG.CACHE_PREFIX + key
      
      localStorage.setItem(cacheKey, value)
      localStorage.setItem(cacheKey + '_exp', expiry.toString())
      
      console.log(`✅ 缓存已保存: ${key}`)
    },

    get(key) {
      const cacheKey = CONFIG.CACHE_PREFIX + key
      const expiry = localStorage.getItem(cacheKey + '_exp')
      
      if (expiry && Date.now() > parseInt(expiry)) {
        // 过期清理
        localStorage.removeItem(cacheKey)
        localStorage.removeItem(cacheKey + '_exp')
        return null
      }
      
      return localStorage.getItem(cacheKey)
    }
  }

  // 拦截 fetch 请求
  const originalFetch = window.fetch
  window.fetch = function(...args) {
    const [url, options] = args
    const urlString = typeof url === 'string' ? url : url.toString()

    // 检查是否为目标请求
    if (urlString.includes('api.example.com')) {
      if (options?.headers) {
        const headers = new Headers(options.headers)
        
        // 提取重要认证信息
        CONFIG.TARGET_HEADERS.forEach(headerName => {
          const value = headers.get(headerName)
          if (value) {
            cache.set(headerName.replace('-', '_'), value)
          }
        })
      }
    }

    return originalFetch.apply(this, args)
  }

  // 拦截 XMLHttpRequest
  const originalXHRSend = XMLHttpRequest.prototype.send
  XMLHttpRequest.prototype.send = function(body) {
    // 监听请求完成
    this.addEventListener('readystatechange', function() {
      if (this.readyState === 4) {
        const authHeader = this.getResponseHeader('Authorization')
        if (authHeader) {
          cache.set('auth_token', authHeader)
        }
      }
    })

    return originalXHRSend.call(this, body)
  }

  // 暴露调试接口
  window.pageInterceptor = {
    getCache: () => ({
      auth: cache.get('authorization'),
      csrf: cache.get('x_csrf_token'),
      antiContent: cache.get('anti_content')
    }),
    clearCache: () => {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(CONFIG.CACHE_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
    }
  }

  console.log('✅ 页面拦截器设置完成')
})()
```

#### 4. ISOLATED World 实现 (扩展逻辑)
```typescript
// unified-content.ts
interface AuthState {
  token: string | null
  csrfToken: string | null
  antiContent: string | null
}

class UnifiedContentScript {
  private logPrefix = '[Content Script]'
  private authState: AuthState = {
    token: null,
    csrfToken: null,
    antiContent: null
  }

  constructor() {
    console.log(`${this.logPrefix} 🚀 Content Script 启动`)
    this.init()
  }

  private async init() {
    this.loadAuthFromCache()
    this.setupMessageListener()
    this.setupPeriodicSync()
    this.exposeDebugInterface()
    
    console.log(`${this.logPrefix} ✅ 初始化完成`)
  }

  // 从localStorage加载MAIN world提取的认证信息
  private loadAuthFromCache() {
    const cacheKeys = {
      token: 'ext_cache_authorization',
      csrfToken: 'ext_cache_x_csrf_token', 
      antiContent: 'ext_cache_anti_content'
    }

    Object.entries(cacheKeys).forEach(([key, cacheKey]) => {
      const value = localStorage.getItem(cacheKey)
      if (value) {
        this.authState[key as keyof AuthState] = value
        console.log(`${this.logPrefix} 加载认证信息: ${key}`)
      }
    })
  }

  // 构建认证请求头
  private buildAuthHeaders(extraHeaders: Record<string, string> = {}) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...extraHeaders
    }

    if (this.authState.token) {
      headers['Authorization'] = this.authState.token
    }
    if (this.authState.csrfToken) {
      headers['X-CSRF-Token'] = this.authState.csrfToken
    }
    if (this.authState.antiContent) {
      headers['anti-content'] = this.authState.antiContent
    }

    return headers
  }

  // 处理认证请求
  private async makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    const headers = this.buildAuthHeaders(options.headers as Record<string, string>)
    
    try {
      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`${this.logPrefix} 请求失败:`, error)
      throw error
    }
  }

  // 消息监听器
  private setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.action) {
        case 'MAKE_AUTH_REQUEST':
          this.handleAuthRequest(request, sendResponse)
          return true // 异步响应

        case 'GET_AUTH_STATE':
          sendResponse({ success: true, data: this.authState })
          break

        case 'REFRESH_AUTH':
          this.loadAuthFromCache()
          sendResponse({ success: true, data: this.authState })
          break

        default:
          sendResponse({ success: false, error: 'Unknown action' })
      }
    })
  }

  private async handleAuthRequest(request: any, sendResponse: Function) {
    try {
      const result = await this.makeAuthenticatedRequest(
        request.url, 
        request.options
      )
      sendResponse({ success: true, data: result })
    } catch (error) {
      sendResponse({ 
        success: false, 
        error: error instanceof Error ? error.message : '请求失败' 
      })
    }
  }

  // 定期同步认证信息
  private setupPeriodicSync() {
    setInterval(() => {
      this.loadAuthFromCache()
    }, 5000) // 每5秒检查一次
  }

  // 调试接口
  private exposeDebugInterface() {
    ;(window as any).contentScript = {
      getAuthState: () => this.authState,
      testAuth: async () => {
        try {
          const result = await this.makeAuthenticatedRequest('/api/test')
          console.log('认证测试结果:', result)
        } catch (error) {
          console.error('认证测试失败:', error)
        }
      },
      refreshCache: () => this.loadAuthFromCache()
    }
  }
}

// 启动Content Script
new UnifiedContentScript()
```

### 5. Vue Popup 组件
```vue
<!-- App.vue -->
<template>
  <div class="extension-popup">
    <a-card title="扩展控制面板" :bordered="false">
      <a-space direction="vertical" style="width: 100%">
        
        <!-- 认证状态 -->
        <a-alert 
          :type="authStatus.type" 
          :message="authStatus.message" 
          show-icon 
        />

        <!-- 功能按钮 -->
        <a-space>
          <a-button 
            type="primary" 
            :loading="loading.refresh"
            @click="refreshAuth"
          >
            刷新认证
          </a-button>
          
          <a-button 
            :loading="loading.test"
            @click="testAPI"
          >
            测试API
          </a-button>
        </a-space>

        <!-- 结果展示 -->
        <a-card v-if="apiResult" size="small" title="API结果">
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </a-card>

      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'

interface AuthState {
  token: string | null
  csrfToken: string | null
  antiContent: string | null
}

const authState = ref<AuthState>({
  token: null,
  csrfToken: null,
  antiContent: null
})

const loading = ref({
  refresh: false,
  test: false
})

const apiResult = ref(null)

// 计算认证状态
const authStatus = computed(() => {
  const hasAuth = authState.value.token || authState.value.antiContent
  return {
    type: hasAuth ? 'success' : 'warning',
    message: hasAuth ? '认证信息已获取' : '等待认证信息...'
  }
})

// 获取当前标签页
const getCurrentTab = async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
  return tab
}

// 刷新认证信息
const refreshAuth = async () => {
  loading.value.refresh = true
  
  try {
    const tab = await getCurrentTab()
    const response = await chrome.tabs.sendMessage(tab.id!, {
      action: 'REFRESH_AUTH'
    })
    
    if (response.success) {
      authState.value = response.data
      message.success('认证信息已刷新')
    } else {
      message.error('刷新失败: ' + response.error)
    }
  } catch (error) {
    message.error('无法连接到页面')
  } finally {
    loading.value.refresh = false
  }
}

// 测试API调用
const testAPI = async () => {
  loading.value.test = true
  
  try {
    const tab = await getCurrentTab()
    const response = await chrome.tabs.sendMessage(tab.id!, {
      action: 'MAKE_AUTH_REQUEST',
      url: 'https://api.example.com/test',
      options: { method: 'GET' }
    })
    
    if (response.success) {
      apiResult.value = response.data
      message.success('API调用成功')
    } else {
      message.error('API调用失败: ' + response.error)
    }
  } catch (error) {
    message.error('请求失败')
  } finally {
    loading.value.test = false
  }
}

onMounted(() => {
  refreshAuth()
})
</script>

<style scoped>
.extension-popup {
  width: 380px;
  padding: 16px;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
```

---

## 🔧 构建配置优化

### Vite配置示例
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  build: {
    rollupOptions: {
      input: {
        popup: resolve(__dirname, 'src/popup/index.html'),
        options: resolve(__dirname, 'src/options/index.html'),
        'content-script': resolve(__dirname, 'src/content-script/main.ts'),
        'service-worker': resolve(__dirname, 'src/background/service-worker.ts'),
        // MAIN world脚本需要特殊处理
        'page-interceptor': resolve(__dirname, 'src/lib/page-interceptor.js')
      },
      output: {
        entryFileNames: (chunkInfo) => {
          // MAIN world脚本保持原样，不进行模块化处理
          if (chunkInfo.name === 'page-interceptor') {
            return '[name].js'
          }
          return 'assets/[name]-[hash].js'
        }
      }
    },
    
    // 确保所有依赖都被打包
    commonjsOptions: {
      include: [/node_modules/]
    }
  },

  // 开发时的特殊配置
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development')
  }
})
```

---

## 🐛 调试与测试

### 调试环境对应表
| 调试目标 | 控制台位置 | 访问方式 |
|----------|------------|----------|
| MAIN World | 页面控制台 | F12 → Console |
| Content Script | 扩展控制台 | chrome://extensions → 检查视图 |
| Service Worker | 扩展后台 | chrome://extensions → Service Worker |
| Popup | Popup控制台 | 右键Popup → 检查 |

### 调试命令
```javascript
// 页面控制台 (MAIN World)
window.pageInterceptor.getCache()
window.pageInterceptor.clearCache()

// 扩展控制台 (Content Script)  
window.contentScript.getAuthState()
window.contentScript.testAuth()
window.contentScript.refreshCache()
```

### 常见错误解决

#### 1. "Illegal invocation" 错误
```typescript
// ❌ 错误写法
const response = await fetch(url, options)

// ✅ 正确写法
const response = await window.fetch.bind(window)(url, options)
```

#### 2. 样式不生效
```typescript
// 确保在manifest中正确声明CSS
{
  "content_scripts": [{
    "css": ["styles/content.css"],
    "js": ["content-script.js"]
  }]
}
```

#### 3. MAIN World脚本不工作
```bash
# 必须构建后测试，HMR不支持
npm run build
# 然后加载dist目录测试
```

---

## 📋 部署检查清单

### 构建前检查
- [ ] 所有Vue模板使用预编译
- [ ] Ant Design样式静态导入
- [ ] CSP配置正确
- [ ] MAIN world脚本独立打包

### 功能测试
- [ ] Service Worker正常启动
- [ ] 认证信息正确拦截
- [ ] Content Script通信正常
- [ ] Popup界面显示正确
- [ ] API调用成功

### 性能优化
- [ ] 打包体积合理 (<2MB)
- [ ] 内存使用正常 (<50MB)
- [ ] 页面性能无影响
- [ ] 电池消耗最小

### 兼容性测试
- [ ] Chrome 最新版本
- [ ] Edge 兼容
- [ ] 不同分辨率适配
- [ ] 主题切换正常

---

## 🎯 最佳实践总结

### 架构设计
1. **职责分离**: MAIN world专注数据拦截，ISOLATED world处理扩展逻辑
2. **数据流**: 页面请求 → MAIN拦截 → 缓存存储 → ISOLATED读取 → 扩展使用
3. **错误处理**: 每个环节都要有完整的异常处理机制

### 开发流程
1. **设计阶段**: 明确功能边界，选择合适的执行环境
2. **开发阶段**: UI功能用HMR，核心功能需构建测试
3. **测试阶段**: 多环境验证，确保数据传递正确
4. **优化阶段**: 性能监控，内存管理，用户体验优化

### 代码质量
1. **类型安全**: 使用TypeScript，定义完整接口
2. **错误边界**: 添加try-catch，优雅降级
3. **调试支持**: 提供丰富的调试接口和日志
4. **文档完整**: 每个模块都有清晰的说明

通过遵循这些原则和实践，可以构建出稳定、高效、用户友好的Chrome V3扩展程序。