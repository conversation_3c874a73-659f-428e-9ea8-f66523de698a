/**
 * ProductListService 测试文件
 * 验证重构后的服务是否正常工作
 */

import { ProductListService } from '../productListService'

describe('ProductListService', () => {
  let service: ProductListService

  beforeEach(() => {
    service = new ProductListService()
  })

  describe('基础功能', () => {
    test('应该能够创建服务实例', () => {
      expect(service).toBeInstanceOf(ProductListService)
    })

    test('应该有所有必需的方法', () => {
      expect(typeof service.getProductList).toBe('function')
      expect(typeof service.getPriceConfirmationProducts).toBe('function')
      expect(typeof service.setMallId).toBe('function')
      expect(typeof service.getTodoCount).toBe('function')
      expect(typeof service.formatStatusTabs).toBe('function')
      expect(typeof service.formatProductData).toBe('function')
    })
  })

  describe('数据格式化', () => {
    test('formatProductData 应该处理空数据', () => {
      const emptyData = { result: { dataList: [] } }
      const result = service.formatProductData(emptyData)
      expect(result).toEqual([])
    })

    test('formatStatusTabs 应该处理空数据', () => {
      const emptyData = { result: null }
      const result = service.formatStatusTabs(emptyData)
      expect(result).toEqual([{ key: 'all', label: '全部', count: 0 }])
    })
  })

  describe('工具方法', () => {
    test('getStatusText 应该正确映射状态', () => {
      // 使用反射访问私有方法进行测试
      const getStatusText = (service as any).getStatusText.bind(service)
      
      expect(getStatusText(0)).toBe('待选择')
      expect(getStatusText(1)).toBe('已选择')
      expect(getStatusText(2)).toBe('已下架')
      expect(getStatusText(3)).toBe('已删除')
      expect(getStatusText(999)).toBe('未知状态')
    })

    test('calculatePriceRange 应该正确计算价格范围', () => {
      const calculatePriceRange = (service as any).calculatePriceRange.bind(service)
      
      // 测试空数据
      const emptyResult = calculatePriceRange([])
      expect(emptyResult.range).toBe('¥0.00')
      
      // 测试单价格
      const singlePriceSkus = [{
        siteSupplierPriceList: [{ supplierPriceValue: 1000 }] // 10.00元
      }]
      const singleResult = calculatePriceRange(singlePriceSkus)
      expect(singleResult.range).toBe('¥10.00')
      
      // 测试价格范围
      const multiPriceSkus = [
        { siteSupplierPriceList: [{ supplierPriceValue: 1000 }] }, // 10.00元
        { siteSupplierPriceList: [{ supplierPriceValue: 2000 }] }  // 20.00元
      ]
      const multiResult = calculatePriceRange(multiPriceSkus)
      expect(multiResult.range).toBe('¥10.00 - ¥20.00')
    })
  })

  describe('兼容性', () => {
    test('setMallId 应该正常工作', () => {
      // 这个方法主要是日志记录，不应该抛出错误
      expect(() => service.setMallId('test-mall-id')).not.toThrow()
    })
  })
})

// 集成测试（需要mock API调用）
describe('ProductListService 集成测试', () => {
  let service: ProductListService

  beforeEach(() => {
    service = new ProductListService()
    
    // Mock temuApiService
    jest.mock('../temu-api', () => ({
      temuApiService: {
        getProductList: jest.fn(),
        getTodoCount: jest.fn(),
        getOfficialPrices: jest.fn()
      }
    }))
  })

  test('getProductList 应该返回正确的格式', async () => {
    // 这里可以添加更详细的集成测试
    // 但需要先设置好mock数据
  })
})
