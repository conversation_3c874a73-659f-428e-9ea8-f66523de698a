import { i18n } from "src/utils/i18n"
import { pinia } from "src/utils/pinia"
import { appRouter } from "src/utils/router"
import { createApp } from "vue"
import App from "./app.vue"

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import "./index.css"

appRouter.addRoute({
  path: "/",
  redirect: "/side-panel",
})

// 添加默认重定向到首页
appRouter.addRoute({
  path: "/side-panel",
  redirect: "/side-panel/dashboard",
})

const app = createApp(App).use(i18n).use(Antd).use(pinia).use(appRouter)

app.mount("#app")

// 监听storage变化来处理导航
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.side_panel_navigation) {
    const navigationData = changes.side_panel_navigation.newValue
    if (navigationData && navigationData.route) {
      console.log('[SidePanel] 收到导航请求:', navigationData.route)

      // 使用Vue Router进行导航
      appRouter.push(navigationData.route).then(() => {
        console.log('[SidePanel] 导航成功:', navigationData.route)
        // 清除导航数据
        chrome.storage.local.remove('side_panel_navigation')
      }).catch(error => {
        console.error('[SidePanel] 导航失败:', error)
      })
    }
  }
})

// 检查是否有待处理的导航请求
chrome.storage.local.get('side_panel_navigation').then(result => {
  if (result.side_panel_navigation && result.side_panel_navigation.route) {
    console.log('[SidePanel] 发现待处理的导航请求:', result.side_panel_navigation.route)
    appRouter.push(result.side_panel_navigation.route).then(() => {
      console.log('[SidePanel] 初始导航成功:', result.side_panel_navigation.route)
      // 清除导航数据
      chrome.storage.local.remove('side_panel_navigation')
    }).catch(error => {
      console.error('[SidePanel] 初始导航失败:', error)
    })
  }
})

// 移除可能导致aria-hidden问题的属性
const removeAriaHidden = () => {
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.removeAttribute('aria-hidden')
    appElement.removeAttribute('data-aria-hidden')

    // 监听DOM变化，持续移除aria-hidden
    const observer = new MutationObserver(() => {
      if (appElement.hasAttribute('aria-hidden')) {
        appElement.removeAttribute('aria-hidden')
      }
      if (appElement.hasAttribute('data-aria-hidden')) {
        appElement.removeAttribute('data-aria-hidden')
      }
    })

    observer.observe(appElement, {
      attributes: true,
      attributeFilter: ['aria-hidden', 'data-aria-hidden']
    })
  }
}

// 在下一个tick执行，确保DOM已经渲染
setTimeout(removeAriaHidden, 0)

export default app

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}
