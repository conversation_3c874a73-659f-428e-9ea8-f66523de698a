/**
 * 自动核价功能组合式函数
 */

import { ref, computed } from 'vue'
import { priceReviewService, type PriceReviewItem } from '../services/priceReviewService'
import { amazonPriceService, type AmazonPriceInfo } from '../services/amazon/amazonPriceService'
import productListService from '../services/productListService'

// 自动核价配置接口
export interface AutoPricingConfig {
  shop: string
  site: string
  repeatCount: number
  pageSize: number
  skcFilter: string
  stockThreshold: number
  profitMargin: number
  maxPricingAttempts: number
  priceReduction: number
  abandonPricing: number
}

// 核价状态枚举
export enum PricingStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// 核价结果类型
export interface PricingResult {
  spu: string
  skc: string
  sku: string
  originalPrice: number
  suggestedPrice: number
  reviewTimes: number
  finalPrice: number
  extCode: string
  stock: number
  costPrice: number
  profitRate: number
  status: 'approved' | 'repriced' | 'rejected'
  remark: string
}

// 核价统计
export interface PricingStats {
  total: number
  processed: number
  approved: number
  repriced: number
  rejected: number
  successRate: number
  approvalRate: number
  firstTimeApprovalRate: number
}

export function useAutoPricing() {
  // 基础状态
  const status = ref<PricingStatus>(PricingStatus.IDLE)
  const progress = ref(0)
  const currentIndex = ref(0)
  const total = ref(0)
  const errorMessage = ref('')
  const isInitializing = ref(false)

  // 核价配置
  const config = ref<AutoPricingConfig | null>(null)
  const currentItem = ref<any>(null)

  // 核价数据
  const priceReviewItems = ref<PriceReviewItem[]>([])
  const results = ref<PricingResult[]>([])
  const stats = ref<PricingStats>({
    total: 0,
    processed: 0,
    approved: 0,
    repriced: 0,
    rejected: 0,
    successRate: 0,
    approvalRate: 0,
    firstTimeApprovalRate: 0
  })

  // 计算属性
  const isRunning = computed(() => status.value === PricingStatus.RUNNING)
  const isPaused = computed(() => status.value === PricingStatus.PAUSED)
  const isCompleted = computed(() => status.value === PricingStatus.COMPLETED)
  const canStart = computed(() => status.value === PricingStatus.IDLE || status.value === PricingStatus.PAUSED)
  const canPause = computed(() => status.value === PricingStatus.RUNNING)

  // 分组结果
  const approvedResults = computed(() => results.value.filter(r => r.status === 'approved'))
  const repricedResults = computed(() => results.value.filter(r => r.status === 'repriced'))
  const rejectedResults = computed(() => results.value.filter(r => r.status === 'rejected'))

  /**
   * 获取待核价商品列表
   */
  const fetchPendingItems = async (): Promise<PriceReviewItem[]> => {
    try {
      console.info('[useAutoPricing] 获取待核价商品列表...')

      // 获取价格待确认的商品列表
      const params = {
        pageSize: 1000, // 获取大量数据
        pageNum: 1,
        priceReviewStatusList: [0, 1, 2, 3],
        secondarySelectStatusList: [7],
        supplierTodoTypeList: [1]
      }

      const productList = await productListService.getProductList(params)

      if (!productList?.success || !productList?.data) {
        throw new Error('未找到待核价商品')
      }

      // 从格式化的商品数据中提取价格订单ID
      const orderIds: number[] = []
      productList.data.forEach((product: any) => {
        if (product.priceReview?.priceOrderId) {
          orderIds.push(product.priceReview.priceOrderId)
        }
      })

      if (orderIds.length === 0) {
        throw new Error('未找到待核价订单')
      }

      console.info('[useAutoPricing] 找到', orderIds.length, '个待核价订单')

      // 获取核价详细信息
      const priceReviewResult = await priceReviewService.getPriceReviewInfo(orderIds)
      
      return priceReviewResult.priceReviewItemList || []
    } catch (error) {
      console.error('[useAutoPricing] 获取待核价商品失败:', error)
      throw error
    }
  }

  /**
   * 获取Amazon成本价格
   */
  const getAmazonCost = async (extCode: string): Promise<number> => {
    try {
      const priceInfo = await amazonPriceService.getProductPrice(extCode, false)
      if (priceInfo) {
        // 美元转人民币，汇率7.2
        return parseFloat(priceInfo.p.toString()) * 7.2
      }
      return 0
    } catch (error) {
      console.warn('[useAutoPricing] 获取Amazon成本失败:', extCode, error)
      return 0
    }
  }

  /**
   * 处理单个商品核价
   */
  const processSingleItem = async (item: PriceReviewItem, pricingConfig: AutoPricingConfig): Promise<PricingResult> => {
    try {
      console.info('[useAutoPricing] 处理商品:', item.productName)

      // 更新当前处理项目
      currentItem.value = {
        productName: item.productName,
        extCode: item.skuInfoList[0]?.productSkuExtCode || '',
        originalPrice: priceReviewService.formatPrice(item.priceBeforeExchange),
        suggestedPrice: priceReviewService.formatPrice(item.suggestSupplyPrice)
      }

      const sku = item.skuInfoList[0]
      if (!sku) {
        throw new Error('商品SKU信息不完整')
      }

      // 获取Amazon成本
      const costPrice = await getAmazonCost(sku.productSkuExtCode)

      // 计算利润率
      const suggestedPriceYuan = priceReviewService.formatPrice(item.suggestSupplyPrice)
      const profitRate = priceReviewService.calculateProfitRate(suggestedPriceYuan, costPrice)

      // 决策逻辑
      let finalPrice = suggestedPriceYuan
      let status: 'approved' | 'repriced' | 'rejected' = 'approved'
      let remark = '已通过核价'

      // 根据配置进行决策
      if (profitRate < pricingConfig.profitMargin) {
        // 检查核价次数
        if (item.reviewTimes >= pricingConfig.maxPricingAttempts) {
          // 超过最大核价次数，根据配置决定是否放弃
          if (pricingConfig.abandonPricing === 1) {
            status = 'rejected'
            remark = '拒绝，放弃上新'
            await priceReviewService.singleRejectPricing(item.id, sku.productSkuId)
          } else {
            // 不处理，保持原状
            status = 'rejected'
            remark = '不处理'
          }
        } else {
          // 重新报价
          const targetProfitRate = pricingConfig.profitMargin / 100
          const targetPrice = costPrice / (1 - targetProfitRate)

          status = 'repriced'
          finalPrice = targetPrice
          remark = '已重新报价'

          const newPriceCents = priceReviewService.formatPriceToCents(targetPrice)
          await priceReviewService.singleRepricing(item.id, sku.productSkuId, newPriceCents)
        }
      } else {
        // 利润率满足要求，同意申报价
        await priceReviewService.singleAcceptPrice(item.id, sku.productSkuId, priceReviewService.formatPriceToCents(suggestedPriceYuan))
      }

      return {
        spu: item.productId.toString(),
        skc: item.skcId.toString(),
        sku: sku.productSkuId.toString(),
        originalPrice: priceReviewService.formatPrice(item.priceBeforeExchange),
        suggestedPrice: suggestedPriceYuan,
        reviewTimes: item.reviewTimes,
        finalPrice: finalPrice,
        extCode: sku.productSkuExtCode,
        stock: 10, // 默认库存
        costPrice: costPrice,
        profitRate: status === 'repriced' 
          ? priceReviewService.calculateProfitRate(finalPrice, costPrice)
          : profitRate,
        status: status,
        remark: remark
      }
    } catch (error) {
      console.error('[useAutoPricing] 处理商品失败:', error)
      
      // 返回错误结果
      const sku = item.skuInfoList[0]
      return {
        spu: item.productId.toString(),
        skc: item.skcId.toString(),
        sku: sku?.productSkuId.toString() || '',
        originalPrice: priceReviewService.formatPrice(item.priceBeforeExchange),
        suggestedPrice: priceReviewService.formatPrice(item.suggestSupplyPrice),
        reviewTimes: item.reviewTimes,
        finalPrice: 0,
        extCode: sku?.productSkuExtCode || '',
        stock: 0,
        costPrice: 0,
        profitRate: 0,
        status: 'rejected',
        remark: '处理失败: ' + (error instanceof Error ? error.message : '未知错误')
      }
    }
  }

  /**
   * 更新统计信息
   */
  const updateStats = () => {
    const processed = results.value.length
    const approved = approvedResults.value.length
    const repriced = repricedResults.value.length
    const rejected = rejectedResults.value.length

    // 计算首次申报通过率（假设reviewTimes=1为首次）
    const firstTimeItems = results.value.filter(r => r.reviewTimes === 1)
    const firstTimeApproved = firstTimeItems.filter(r => r.status === 'approved').length
    const firstTimeApprovalRate = firstTimeItems.length > 0
      ? (firstTimeApproved / firstTimeItems.length) * 100
      : 0

    // 计算成功率（非拒绝的比例）
    const successRate = processed > 0 ? ((approved + repriced) / processed) * 100 : 0

    // 计算通过率（同意申报价的比例）
    const approvalRate = processed > 0 ? (approved / processed) * 100 : 0

    stats.value = {
      total: total.value,
      processed,
      approved,
      repriced,
      rejected,
      successRate,
      approvalRate,
      firstTimeApprovalRate
    }
  }

  /**
   * 开始自动核价
   */
  const startPricing = async (pricingConfig: AutoPricingConfig) => {
    try {
      console.info('[useAutoPricing] 开始自动核价...', pricingConfig)

      isInitializing.value = true
      config.value = pricingConfig

      // 获取待核价商品
      const items = await fetchPendingItems()
      priceReviewItems.value = items
      total.value = items.length
      currentIndex.value = 0
      results.value = []

      isInitializing.value = false
      status.value = PricingStatus.RUNNING
      errorMessage.value = ''

      // 处理商品
      for (let i = currentIndex.value; i < priceReviewItems.value.length; i++) {
        // 检查是否暂停
        if (status.value === PricingStatus.PAUSED) {
          break
        }

        currentIndex.value = i
        progress.value = ((i + 1) / total.value) * 100

        const item = priceReviewItems.value[i]
        const result = await processSingleItem(item, pricingConfig)
        results.value.push(result)

        updateStats()

        // 添加延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      // 完成或暂停
      if (status.value === PricingStatus.RUNNING) {
        status.value = PricingStatus.COMPLETED
        progress.value = 100
        console.info('[useAutoPricing] 自动核价完成')
      }

    } catch (error) {
      console.error('[useAutoPricing] 自动核价失败:', error)
      status.value = PricingStatus.ERROR
      errorMessage.value = error instanceof Error ? error.message : '未知错误'
      isInitializing.value = false
    }
  }

  /**
   * 暂停自动核价
   */
  const pausePricing = () => {
    console.info('[useAutoPricing] 暂停自动核价')
    status.value = PricingStatus.PAUSED
  }

  /**
   * 恢复自动核价
   */
  const resumePricing = async () => {
    if (!config.value) return

    console.info('[useAutoPricing] 恢复自动核价')
    status.value = PricingStatus.RUNNING

    // 继续处理剩余商品
    for (let i = currentIndex.value; i < priceReviewItems.value.length; i++) {
      if (status.value === PricingStatus.PAUSED) {
        break
      }

      currentIndex.value = i
      progress.value = ((i + 1) / total.value) * 100

      const item = priceReviewItems.value[i]
      const result = await processSingleItem(item, config.value)
      results.value.push(result)

      updateStats()

      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    if (status.value === PricingStatus.RUNNING) {
      status.value = PricingStatus.COMPLETED
      progress.value = 100
      console.info('[useAutoPricing] 自动核价完成')
    }
  }

  /**
   * 停止自动核价
   */
  const stopPricing = () => {
    console.info('[useAutoPricing] 停止自动核价')
    status.value = PricingStatus.IDLE
    currentItem.value = null
  }

  /**
   * 暂停自动核价
   */
  const pauseAutoPricing = () => {
    console.info('[useAutoPricing] 暂停自动核价')
    status.value = PricingStatus.PAUSED
  }

  /**
   * 重置状态
   */
  const resetAutoPricing = () => {
    console.info('[useAutoPricing] 重置自动核价状态')
    status.value = PricingStatus.IDLE
    progress.value = 0
    currentIndex.value = 0
    total.value = 0
    errorMessage.value = ''
    priceReviewItems.value = []
    results.value = []
    stats.value = {
      total: 0,
      approved: 0,
      repriced: 0,
      rejected: 0,
      firstTimePassRate: 0,
      currentPassRate: 0
    }
  }

  return {
    // 状态
    status,
    progress,
    currentIndex,
    total,
    errorMessage,
    isInitializing,
    currentItem,

    // 数据
    priceReviewItems,
    results,
    stats,

    // 计算属性
    isRunning,
    isPaused,
    isCompleted,
    canStart,
    canPause,
    approvedResults,
    repricedResults,
    rejectedResults,

    // 方法
    startPricing,
    pausePricing,
    resumePricing,
    stopPricing,
    fetchPendingItems
  }
}
