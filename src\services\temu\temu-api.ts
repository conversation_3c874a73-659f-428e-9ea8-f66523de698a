/**
 * Temu API服务
 * 
 * 功能：
 * - 提供统一的Temu API调用接口
 * - 处理认证头部构建和请求发送
 * - 通过content script代理所有API请求
 * - 支持批量操作和数据格式化
 * 
 * 依赖关系：
 * - 依赖：temu-config.ts (API端点和配置)
 * - 依赖：temu-auth.ts (认证头部管理)
 * - 被依赖：temuDataService.ts, dashboard组件
 * 
 * 核心方法：
 * - makeRequest(): 通用API请求方法
 * - getUserInfo(): 获取用户信息
 * - getProductList(): 获取商品列表
 * - getOfficialPrices(): 批量获取官方申报价
 */

import { TEMU_CONFIG, type ApiResponse } from '../../config/temu-config'
import { temuAuthManager, type AuthHeaders } from './temu-auth'

// 请求选项接口
interface RequestOptions {
  method?: string
  body?: any
  headers?: Record<string, string>
  timeout?: number
}

// 商品搜索参数
interface SearchParams {
  pageSize?: number
  pageNum?: number  // 修复：使用pageNum而不是currentPage
  currentPage?: number  // 保留兼容性
  supplierTodoTypeList?: number[]
  secondarySelectStatusList?: number[]
  priceReviewStatusList?: number[]
  keyword?: string
  [key: string]: any
}

class TemuApiService {
  private readonly logPrefix = TEMU_CONFIG.DEBUG.LOG_PREFIX + '[API]'

  /**
   * 通用API请求方法
   * 通过content script代理发送认证请求
   */
  async makeRequest<T = any>(url: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    try {
      console.info(`${this.logPrefix} 发起API请求:`, url, options)

      // 构建认证头部
      const authHeaders = temuAuthManager.buildAuthHeaders(options.headers)
      
      // 准备请求选项
      const requestOptions = {
        method: options.method || 'POST',
        headers: authHeaders,
        body: options.body ? JSON.stringify(options.body) : undefined,
        timeout: options.timeout || 30000
      }

      // 通过content script发送请求
      const response = await this.sendRequestViaContentScript(url, requestOptions)
      
      console.info(`${this.logPrefix} API请求成功:`, url, response)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      console.error(`${this.logPrefix} API请求失败:`, url, error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败',
        errorCode: 500
      }
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<ApiResponse> {
    return this.makeRequest(TEMU_CONFIG.ENDPOINTS.USER_INFO_URL, {
      method: 'POST',
      body: {}
    })
  }

  /**
   * 获取待办事项数量
   */
  async getTodoCount(): Promise<ApiResponse> {
    return this.makeRequest(TEMU_CONFIG.ENDPOINTS.TODO_COUNT_URL, {
      method: 'POST',
      body: {}
    })
  }

  /**
   * 获取商品列表
   */
  async getProductList(params: SearchParams = {}): Promise<ApiResponse> {
    // 构建默认参数
    const defaultParams = {
      pageSize: 20,
      pageNum: 1,  // 修复：使用pageNum
      supplierTodoTypeList: [],
      secondarySelectStatusList: [],
      priceReviewStatusList: [],
      keyword: ''
    }

    // 兼容性处理：如果传入currentPage，转换为pageNum
    if (params.currentPage && !params.pageNum) {
      params.pageNum = params.currentPage
    }

    const searchParams = { ...defaultParams, ...params }

    console.info(`${this.logPrefix} 商品列表请求参数:`, searchParams)

    return this.makeRequest(TEMU_CONFIG.ENDPOINTS.PRODUCT_LIST_URL, {
      method: 'POST',
      body: searchParams
    })
  }

  /**
   * 批量获取官方申报价
   */
  async getOfficialPrices(priceOrderIds: number[]): Promise<ApiResponse> {
    if (!priceOrderIds || priceOrderIds.length === 0) {
      console.warn(`${this.logPrefix} getOfficialPrices: priceOrderIds为空`)
      return {
        success: false,
        error: 'priceOrderIds 不能为空'
      }
    }

    console.info(`${this.logPrefix} 获取官方申报价，priceOrderIds:`, priceOrderIds)

    const requestBody = { orderIds: priceOrderIds }
    console.info(`${this.logPrefix} 官方申报价请求体:`, requestBody)

    return this.makeRequest(TEMU_CONFIG.ENDPOINTS.OFFICIAL_PRICE_URL, {
      method: 'POST',
      body: requestBody
    })
  }



  /**
   * 通过content script发送请求
   */
  private async sendRequestViaContentScript(url: string, options: any): Promise<any> {
    // 检查Chrome API可用性
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      throw new Error(TEMU_CONFIG.ERROR_MESSAGES.CHROME_API_UNAVAILABLE)
    }

    // 查找Temu标签页
    const tabs = await chrome.tabs.query({
      url: TEMU_CONFIG.TAB.URL_PATTERNS
    })

    if (tabs.length === 0) {
      throw new Error(TEMU_CONFIG.ERROR_MESSAGES.NO_TAB_FOUND)
    }

    // 发送消息到content script
    try {
      const response = await chrome.tabs.sendMessage(tabs[0].id!, {
        action: 'MAKE_AUTHENTICATED_REQUEST',
        url,
        options
      })

      if (!response) {
        throw new Error('Content script 无响应')
      }

      if (!response.success) {
        throw new Error(response.error || '请求失败')
      }

      return response.data
    } catch (error) {
      console.error(`${this.logPrefix} Content script 通信失败:`, error)
      throw new Error(`Content script 通信失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 检查登录状态
   */
  async checkLoginStatus(): Promise<ApiResponse> {
    try {
      const response = await this.getUserInfo()
      
      if (response.success && response.data?.success && response.data?.result) {
        const userInfo = this.parseUserInfo(response.data.result)
        const shopInfo = this.parseShopInfo(response.data.result)
        
        return {
          success: true,
          data: {
            isLoggedIn: true,
            userInfo,
            shopInfo,
            message: '已登录 Temu 商家后台'
          }
        }
      } else {
        return {
          success: true,
          data: {
            isLoggedIn: false,
            message: '未登录或登录已过期'
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '检测登录状态失败'
      }
    }
  }

  /**
   * 解析用户信息
   */
  private parseUserInfo(data: any): any {
    try {
      if (data.userId) {
        return {
          userId: String(data.userId),
          userName: data.userName || data.name,
          email: data.email
        }
      }
      return null
    } catch (error) {
      console.warn(`${this.logPrefix} 解析用户信息失败:`, error)
      return null
    }
  }

  /**
   * 解析店铺信息
   */
  private parseShopInfo(data: any): any {
    try {
      if (data.companyList && Array.isArray(data.companyList)) {
        for (const company of data.companyList) {
          if (company?.malInfoList && Array.isArray(company.malInfoList)) {
            for (const mallInfo of company.malInfoList) {
              if (mallInfo?.mallId && mallInfo?.mallName) {
                // 缓存mallId
                temuAuthManager.setMallId(String(mallInfo.mallId), 'userInfo')
                
                return {
                  mallId: String(mallInfo.mallId),
                  shopId: String(mallInfo.mallId),
                  mallName: String(mallInfo.mallName),
                  shopName: String(mallInfo.mallName)
                }
              }
            }
          }
        }
      }
      return null
    } catch (error) {
      console.warn(`${this.logPrefix} 解析店铺信息失败:`, error)
      return null
    }
  }

  /**
   * 获取API状态报告
   */
  getApiStatus(): any {
    return {
      endpoints: TEMU_CONFIG.ENDPOINTS,
      authStatus: {
        hasAntiContent: !!temuAuthManager.getAntiContent(),
        hasMallId: !!temuAuthManager.getMallId()
      },
      cacheStatus: temuAuthManager.getCacheStatus(),
      timestamp: new Date().toLocaleString()
    }
  }
}

// 创建单例实例
export const temuApiService = new TemuApiService()
export default temuApiService

// 导出类型
export type { RequestOptions, SearchParams }
