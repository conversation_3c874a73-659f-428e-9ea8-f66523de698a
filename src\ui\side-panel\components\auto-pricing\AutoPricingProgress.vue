<template>
  <a-card title="核价进度" size="small">
    <div class="progress-container">
      <!-- 状态显示 -->
      <div class="status-section">
        <a-tag :color="statusColor" class="status-tag">
          {{ statusText }}
        </a-tag>
        <span class="progress-text">{{ progressText }}</span>
      </div>

      <!-- 进度条 -->
      <div class="progress-bar-section">
        <a-progress 
          :percent="progress" 
          :status="progressStatus"
          :stroke-color="progressColor"
          :show-info="true"
        />
      </div>

      <!-- 当前处理项目 -->
      <div class="current-item-section" v-if="currentItem">
        <a-descriptions title="当前处理商品" :column="2" size="small" bordered>
          <a-descriptions-item label="商品名称">
            {{ currentItem.productName }}
          </a-descriptions-item>
          <a-descriptions-item label="货号">
            {{ currentItem.extCode }}
          </a-descriptions-item>
          <a-descriptions-item label="原申报价">
            ¥{{ currentItem.originalPrice }}
          </a-descriptions-item>
          <a-descriptions-item label="官方建议价">
            ¥{{ currentItem.suggestedPrice }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="总数量" 
              :value="stats.total" 
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="已处理" 
              :value="stats.processed" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="成功率" 
              :value="stats.successRate" 
              suffix="%" 
              :precision="1"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="通过率" 
              :value="stats.approvalRate" 
              suffix="%" 
              :precision="1"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
        </a-row>

        <a-divider />

        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic 
              title="核价成功" 
              :value="stats.approved" 
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic 
              title="重新报价" 
              :value="stats.repriced" 
              :value-style="{ color: '#faad14' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic 
              title="拒绝核价" 
              :value="stats.rejected" 
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 控制按钮 -->
      <div class="control-buttons">
        <a-space>
          <a-button 
            v-if="status === 'running'"
            type="default"
            @click="$emit('pause')"
          >
            暂停
          </a-button>
          <a-button 
            v-if="status === 'paused'"
            type="primary"
            @click="$emit('resume')"
          >
            继续
          </a-button>
          <a-button 
            v-if="status === 'running' || status === 'paused'"
            danger
            @click="$emit('stop')"
          >
            停止
          </a-button>
        </a-space>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
interface Props {
  status: 'idle' | 'running' | 'paused' | 'completed' | 'error'
  progress: number
  currentItem?: {
    productName: string
    extCode: string
    originalPrice: number
    suggestedPrice: number
  }
  stats: {
    total: number
    processed: number
    approved: number
    repriced: number
    rejected: number
    successRate: number
    approvalRate: number
  }
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  pause: []
  resume: []
  stop: []
}>()

// 计算属性
const statusColor = computed(() => {
  switch (props.status) {
    case 'running': return 'processing'
    case 'paused': return 'warning'
    case 'completed': return 'success'
    case 'error': return 'error'
    default: return 'default'
  }
})

const statusText = computed(() => {
  switch (props.status) {
    case 'running': return '运行中'
    case 'paused': return '已暂停'
    case 'completed': return '已完成'
    case 'error': return '出错'
    default: return '待开始'
  }
})

const progressText = computed(() => {
  if (props.stats.total === 0) return '准备中...'
  return `${props.stats.processed} / ${props.stats.total}`
})

const progressStatus = computed(() => {
  switch (props.status) {
    case 'completed': return 'success'
    case 'error': return 'exception'
    default: return 'normal'
  }
})

const progressColor = computed(() => {
  switch (props.status) {
    case 'running': return '#1890ff'
    case 'paused': return '#faad14'
    case 'completed': return '#52c41a'
    case 'error': return '#ff4d4f'
    default: return '#d9d9d9'
  }
})
</script>

<style>
.progress-container {
  padding: 16px 0;
}

.status-section {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.status-tag {
  margin-right: 12px;
}

.progress-text {
  font-size: 14px;
  color: #666;
}

.progress-bar-section {
  margin-bottom: 24px;
}

.current-item-section {
  margin-bottom: 24px;
}

.stats-section {
  margin-bottom: 24px;
}

.control-buttons {
  text-align: center;
}

:deep(.ant-descriptions-title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 18px;
}
</style>
