<script setup lang="ts">
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
</script>

<template>
  <a-layout class="min-h-screen">
    <a-layout-header style="padding: 0;">
      <AppHeader />
    </a-layout-header>

    <a-layout-content class="w-full p-4">
      <div class="prose dark:prose-invert">
        <RouterView />
      </div>
    </a-layout-content>

    <a-layout-footer style="padding: 0;">
      <AppFooter />
    </a-layout-footer>
  </a-layout>
</template>

<style lang="css"></style>
