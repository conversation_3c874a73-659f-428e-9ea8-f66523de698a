/**
 * Amazon采集流程控制器
 * 统一管理Amazon商品采集、处理和推送的完整流程
 */

import imageProcessingService from '../imageProcessingService'
import dataMapperService from '../dataMapperService'
import pushService from '../pushService'
import configStorageService from '../configStorageService'

export interface CollectionConfig {
  enableImageProcessing: boolean
  enableAutoPush: boolean
  maxConcurrentRequests: number
  retryAttempts: number
  imageProcessingOptions: {
    maxImages: number
    targetSize: number
    quality: number
  }
  pushOptions: {
    batchSize: number
    delayBetweenPush: number
  }
}

export interface CollectionResult {
  success: boolean
  productData?: {
    spuData: any
    skuDataList: any[]
    dianxiaomiData: any
    processedImages?: string[]
  }
  pushResult?: any
  error?: string
  warnings?: string[]
  metadata: {
    collectionTime: number
    imageProcessingTime?: number
    pushTime?: number
    totalTime: number
  }
}

export interface BatchCollectionResult {
  success: boolean
  results: CollectionResult[]
  summary: {
    total: number
    successful: number
    failed: number
    warnings: number
  }
  metadata: {
    startTime: string
    endTime: string
    totalTime: number
  }
}

export class AmazonCollectionController {
  private static instance: AmazonCollectionController
  private defaultConfig: CollectionConfig = {
    enableImageProcessing: true,
    enableAutoPush: false,
    maxConcurrentRequests: 3,
    retryAttempts: 2,
    imageProcessingOptions: {
      maxImages: 8,
      targetSize: 800,
      quality: 0.85
    },
    pushOptions: {
      batchSize: 5,
      delayBetweenPush: 2000
    }
  }

  static getInstance(): AmazonCollectionController {
    if (!AmazonCollectionController.instance) {
      AmazonCollectionController.instance = new AmazonCollectionController()
    }
    return AmazonCollectionController.instance
  }

  /**
   * 采集单个Amazon商品的完整流程
   */
  async collectSingleProduct(
    productUrl: string,
    config?: Partial<CollectionConfig>
  ): Promise<CollectionResult> {
    const startTime = Date.now()
    const finalConfig = { ...this.defaultConfig, ...config }
    const warnings: string[] = []

    try {
      console.info('[AmazonCollectionController] 开始单品采集流程:', productUrl)

      // 1. 验证配置
      const configValidation = await this.validateConfiguration()
      if (!configValidation.isValid) {
        throw new Error(`配置验证失败: ${configValidation.errors.join(', ')}`)
      }

      // 2. 获取基础配置
      const basicConfig = await configStorageService.getBasicConfig()

      // 3. 采集Amazon数据
      console.info('[AmazonCollectionController] 步骤1: 采集Amazon数据...')
      const collectionStartTime = Date.now()
      
      const amazonResult = await chrome.runtime.sendMessage({
        action: 'EXTRACT_AND_ASSEMBLE_AMAZON_DATA',
        productUrl: productUrl,
        config: basicConfig
      })

      if (!amazonResult.success) {
        throw new Error(amazonResult.error || 'Amazon数据采集失败')
      }

      const collectionTime = Date.now() - collectionStartTime

      // 4. 处理图片（可选）
      let processedImages: string[] = []
      let imageProcessingTime = 0

      if (finalConfig.enableImageProcessing) {
        console.info('[AmazonCollectionController] 步骤2: 处理商品图片...')
        const imageStartTime = Date.now()

        try {
          const imageUrls = JSON.parse(amazonResult.spuData.imageUrls || '[]')
          const limitedImageUrls = imageUrls.slice(0, finalConfig.imageProcessingOptions.maxImages)

          if (limitedImageUrls.length > 0) {
            const imageResults = await imageProcessingService.batchProcessImages(limitedImageUrls)
            processedImages = imageResults
              .filter(result => result.success && result.processedUrl)
              .map(result => result.processedUrl!)

            if (processedImages.length < limitedImageUrls.length) {
              warnings.push(`图片处理部分失败: ${processedImages.length}/${limitedImageUrls.length} 成功`)
            }
          }
        } catch (imageError) {
          warnings.push(`图片处理失败: ${imageError instanceof Error ? imageError.message : '未知错误'}`)
          // 使用原始图片
          processedImages = JSON.parse(amazonResult.spuData.imageUrls || '[]')
        }

        imageProcessingTime = Date.now() - imageStartTime
      } else {
        // 不处理图片，使用原始图片
        processedImages = JSON.parse(amazonResult.spuData.imageUrls || '[]')
      }

      // 5. 重新映射数据（使用处理后的图片）
      console.info('[AmazonCollectionController] 步骤3: 重新映射数据...')
      const mappingConfig = this.buildMappingConfig(basicConfig)
      
      const enhancedDianxiaomiData = await dataMapperService.mapAmazonToDianxiaomi(
        amazonResult.spuData,
        amazonResult.skuDataList,
        mappingConfig,
        processedImages
      )

      // 6. 推送到店小秘（可选）
      let pushResult: any = null
      let pushTime = 0

      if (finalConfig.enableAutoPush) {
        console.info('[AmazonCollectionController] 步骤4: 推送到店小秘...')
        const pushStartTime = Date.now()

        try {
          pushResult = await pushService.pushSingleProduct(enhancedDianxiaomiData)
          if (!pushResult.success) {
            warnings.push(`推送失败: ${pushResult.error}`)
          }
        } catch (pushError) {
          warnings.push(`推送异常: ${pushError instanceof Error ? pushError.message : '未知错误'}`)
        }

        pushTime = Date.now() - pushStartTime
      }

      const totalTime = Date.now() - startTime

      console.info('[AmazonCollectionController] 单品采集流程完成:', {
        success: true,
        warnings: warnings.length,
        totalTime: totalTime
      })

      return {
        success: true,
        productData: {
          spuData: amazonResult.spuData,
          skuDataList: amazonResult.skuDataList,
          dianxiaomiData: enhancedDianxiaomiData,
          processedImages: processedImages
        },
        pushResult: pushResult,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          collectionTime,
          imageProcessingTime: finalConfig.enableImageProcessing ? imageProcessingTime : undefined,
          pushTime: finalConfig.enableAutoPush ? pushTime : undefined,
          totalTime
        }
      }

    } catch (error) {
      const totalTime = Date.now() - startTime
      console.error('[AmazonCollectionController] 单品采集流程失败:', error)

      return {
        success: false,
        error: error instanceof Error ? error.message : '采集失败',
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          collectionTime: 0,
          totalTime
        }
      }
    }
  }

  /**
   * 批量采集Amazon商品
   */
  async batchCollectProducts(
    productUrls: string[],
    config?: Partial<CollectionConfig>,
    onProgress?: (current: number, total: number, result?: CollectionResult) => void
  ): Promise<BatchCollectionResult> {
    const startTime = new Date()
    const finalConfig = { ...this.defaultConfig, ...config }

    console.info('[AmazonCollectionController] 开始批量采集:', productUrls.length, '个商品')

    const results: CollectionResult[] = []
    let successful = 0
    let failed = 0
    let warnings = 0

    // 分批处理，控制并发
    const batchSize = Math.min(finalConfig.maxConcurrentRequests, productUrls.length)

    for (let i = 0; i < productUrls.length; i += batchSize) {
      const batch = productUrls.slice(i, i + batchSize)

      // 并发处理当前批次
      const batchPromises = batch.map(async (productUrl, batchIndex) => {
        const globalIndex = i + batchIndex

        try {
          onProgress?.(globalIndex, productUrls.length)

          const result = await this.collectSingleProduct(productUrl, {
            ...finalConfig,
            enableAutoPush: false // 批量模式下先不自动推送
          })

          onProgress?.(globalIndex + 1, productUrls.length, result)

          return result
        } catch (error) {
          const errorResult: CollectionResult = {
            success: false,
            error: error instanceof Error ? error.message : '采集失败',
            metadata: {
              collectionTime: 0,
              totalTime: 0
            }
          }

          onProgress?.(globalIndex + 1, productUrls.length, errorResult)
          return errorResult
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.allSettled(batchPromises)

      // 统计结果
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value)
          if (result.value.success) {
            successful++
            if (result.value.warnings && result.value.warnings.length > 0) {
              warnings++
            }
          } else {
            failed++
          }
        } else {
          failed++
          results.push({
            success: false,
            error: '处理异常',
            metadata: {
              collectionTime: 0,
              totalTime: 0
            }
          })
        }
      })

      // 批次间延迟
      if (i + batchSize < productUrls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    const endTime = new Date()
    const totalTime = endTime.getTime() - startTime.getTime()

    console.info('[AmazonCollectionController] 批量采集完成:', {
      total: productUrls.length,
      successful,
      failed,
      warnings,
      totalTime
    })

    return {
      success: successful > 0,
      results,
      summary: {
        total: productUrls.length,
        successful,
        failed,
        warnings
      },
      metadata: {
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        totalTime
      }
    }
  }

  /**
   * 验证配置
   */
  private async validateConfiguration(): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      // 检查基础配置
      const configCheck = await configStorageService.isConfigComplete()
      if (!configCheck.isComplete) {
        errors.push(...configCheck.missingFields.map(field => `缺少配置: ${field}`))
      }

      // 检查店小秘登录状态
      // 这里可以添加更多验证逻辑

    } catch (error) {
      errors.push('配置验证异常')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 构建映射配置
   */
  private buildMappingConfig(basicConfig: any): any {
    return {
      basic: {
        categoryId: basicConfig.categoryId || "9938",
        shopId: basicConfig.shopAccount || "6959965",
        businessSite: basicConfig.businessSite || "**************",
        warehouse: basicConfig.warehouse || "WH-*****************",
        freightTemplate: basicConfig.freightTemplate || "HFT-14821249525104782972",
        shippingTime: basicConfig.shippingTime || "172800",
        publishStatus: basicConfig.publishStatus || "2",
        venue: basicConfig.venue || "CN_GD",
        productCategory: basicConfig.productCategory || "",
        productAttributes: basicConfig.productAttributes || {}
      },
      product: {
        titlePrefix: "",
        titleSuffix: "",
        priceMultiplier: 4,
        defaultSize: {
          length: 127,
          width: 23,
          height: 19,
          weight: 2000
        },
        fixedStock: 200,
        minStock: 12,
        collectDetails: ['title', 'img'],
        collectSku: true
      }
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): CollectionConfig {
    return { ...this.defaultConfig }
  }

  /**
   * 更新默认配置
   */
  updateDefaultConfig(config: Partial<CollectionConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config }
  }
}

export default AmazonCollectionController.getInstance()
