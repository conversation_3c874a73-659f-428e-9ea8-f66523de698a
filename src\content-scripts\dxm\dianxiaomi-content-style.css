/* dianxiaomi-content-style.css */

.dxm-btn {
  position: fixed !important;
  bottom: 120px !important;
  right: 30px !important;
  z-index: 2147483647 !important;
  padding: 12px 16px !important;
  color: white !important;
  text-align: center !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border: none !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  cursor: pointer !important;
  transition: background-color 0.3s, transform 0.1s;
}

.dxm-btn:active {
    transform: scale(0.98);
}

.dxm-btn-primary, .dxm-btn-idle {
  background-color: #1890ff !important;
}
.dxm-btn-loading {
  background-color: #a0a0a0 !important;
  cursor: wait !important;
}
.dxm-btn-success {
  background-color: #52c41a !important;
}
.dxm-btn-error {
  background-color: #ff4d4f !important;
}

.dxm-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 2147483647;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.dxm-notification-success {
  background-color: #52c41a;
}
.dxm-notification-error {
  background-color: #ff4d4f;
}
.dxm-notification-info {
  background-color: #1890ff;
}
