/**
 * IndexedDB管理工具
 * 用于检查、管理和调试IndexedDB中的Amazon价格数据
 */

export class IndexedDBDebugManager {
  private dbName = 'hjdmjDB'
  private storeName = 'dmjObjectStore'

  /**
   * 获取IndexedDB统计信息
   */
  async getStats(): Promise<any> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_INDEXEDDB_STATS'
      })

      if (response && response.success) {
        return response.data
      } else {
        throw new Error(response?.error || '获取统计失败')
      }
    } catch (error) {
      console.error('[IndexedDBDebugManager] 获取统计失败:', error)
      throw error
    }
  }

  /**
   * 清理过期数据
   */
  async cleanExpiredData(): Promise<number> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'CLEAN_INDEXEDDB_EXPIRED'
      })

      if (response && response.success) {
        return response.data.deletedCount
      } else {
        throw new Error(response?.error || '清理失败')
      }
    } catch (error) {
      console.error('[IndexedDBDebugManager] 清理过期数据失败:', error)
      throw error
    }
  }

  /**
   * 直接访问IndexedDB获取所有数据
   */
  async getAllDataDirect(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName)
      
      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        if (!db.objectStoreNames.contains(this.storeName)) {
          resolve([])
          return
        }
        
        const transaction = db.transaction([this.storeName], 'readonly')
        const store = transaction.objectStore(this.storeName)
        const getAllRequest = store.getAll()
        
        getAllRequest.onsuccess = () => {
          resolve(getAllRequest.result)
        }
        
        getAllRequest.onerror = () => {
          reject(getAllRequest.error)
        }
      }
      
      request.onerror = () => {
        reject(request.error)
      }
    })
  }

  /**
   * 分析数据分布
   */
  async analyzeData(): Promise<any> {
    const allData = await this.getAllDataDirect()
    
    const analysis = {
      totalCount: allData.length,
      dataTypes: {},
      timeRange: {
        oldest: null as Date | null,
        newest: null as Date | null
      },
      keyPatterns: {},
      sampleData: allData.slice(0, 10)
    }

    // 分析数据类型和时间范围
    allData.forEach(item => {
      // 分析键值模式
      const keyType = this.analyzeKeyPattern(item.id || item.key || 'unknown')
      analysis.keyPatterns[keyType] = (analysis.keyPatterns[keyType] || 0) + 1

      // 分析时间范围
      const timestamp = item.t || item.timestamp || 0
      if (timestamp) {
        const date = new Date(timestamp)
        if (!analysis.timeRange.oldest || date < analysis.timeRange.oldest) {
          analysis.timeRange.oldest = date
        }
        if (!analysis.timeRange.newest || date > analysis.timeRange.newest) {
          analysis.timeRange.newest = date
        }
      }

      // 分析数据结构类型
      const dataType = this.analyzeDataStructure(item)
      analysis.dataTypes[dataType] = (analysis.dataTypes[dataType] || 0) + 1
    })

    return analysis
  }

  /**
   * 分析键值模式 - 简化版（只处理Amazon数据）
   */
  private analyzeKeyPattern(key: string): string {
    if (key.match(/^[A-Z0-9]{10}\[am\]/)) {
      return 'amazon_extcode'
    } else {
      return 'other'
    }
  }

  /**
   * 分析数据结构 - 简化版（只处理Amazon数据）
   */
  private analyzeDataStructure(item: Record<string, any>): string {
    if (item.d && typeof item.d === 'string') {
      try {
        const parsed = JSON.parse(item.d)
        if (parsed.p !== undefined && parsed.s !== undefined) {
          return 'amazon_price_data'
        }
      } catch {
        // 解析失败，忽略
      }
      return 'json_string_data'
    } else {
      return 'unknown_structure'
    }
  }

  /**
   * 打印详细报告
   */
  async printDetailedReport(): Promise<void> {
    console.info('📊 IndexedDB 详细分析报告')

    try {
      const stats = await this.getStats()
      const analysis = await this.analyzeData()

      console.info('🔢 基本统计:', {
        '总记录数': stats.count,
        '实际数据条数': analysis.totalCount,
        '数据一致性': stats.count === analysis.totalCount ? '✅ 一致' : '❌ 不一致'
      })

      console.info('📅 时间范围:')
      if (analysis.timeRange.oldest && analysis.timeRange.newest) {
        console.info({
          '最早记录': analysis.timeRange.oldest.toLocaleString(),
          '最新记录': analysis.timeRange.newest.toLocaleString(),
          '时间跨度': `${Math.round((analysis.timeRange.newest.getTime() - analysis.timeRange.oldest.getTime()) / (1000 * 60 * 60 * 24))} 天`
        })
      }

      console.info('🔑 键值模式分布:', analysis.keyPatterns)
      console.info('📋 数据结构类型:', analysis.dataTypes)

      console.info('📝 样本数据 (前10条):',
        analysis.sampleData.map((item: Record<string, unknown>) => ({
          ID: item.id || item.key || 'N/A',
          时间戳: item.t ? new Date(item.t as number).toLocaleString() : 'N/A',
          数据类型: this.analyzeDataStructure(item),
          键值模式: this.analyzeKeyPattern((item.id || item.key || 'unknown') as string)
        }))
      )

      // 检查是否有重复或冲突的数据
      const duplicates = this.findDuplicates(analysis.sampleData)
      if (duplicates.length > 0) {
        console.warn('⚠️ 发现重复数据:', duplicates)
      }

    } catch (error) {
      console.error('❌ 生成报告失败:', error)
    }

    console.info('📊 IndexedDB 分析报告结束')
  }

  /**
   * 查找重复数据
   */
  private findDuplicates(data: Record<string, unknown>[]): Record<string, unknown>[] {
    const seen = new Set()
    const duplicates: Record<string, unknown>[] = []

    for (const item of data) {
      const key = item.id || item.key
      if (key && seen.has(key)) {
        duplicates.push(item)
      } else if (key) {
        seen.add(key)
      }
    }

    return duplicates
  }
}

// 创建全局实例供控制台使用
if (typeof window !== 'undefined') {
  (window as any).indexedDBDebugManager = new IndexedDBDebugManager()
  console.info('🔧 IndexedDB调试工具已加载，使用 window.indexedDBDebugManager 访问')
}

export default IndexedDBDebugManager
