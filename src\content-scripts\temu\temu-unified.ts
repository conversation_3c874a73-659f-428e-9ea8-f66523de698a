/**
 * Temu统一Content Script
 *
 * 功能：
 * - 统一处理所有来自扩展的消息请求
 * - 代理API请求，确保认证头部正确
 * - 从localStorage读取认证信息（由page-interceptor.js提供）
 * - 提供认证信息查询接口
 *
 * 依赖关系：
 * - 依赖：temu-config.ts (配置和常量)
 * - 依赖：page-interceptor.js (在MAIN world中提取认证信息)
 * - 被依赖：temu-api.ts (通过消息通信)
 *
 * 运行环境：
 * - 注入到Temu商家后台页面的content script环境(ISOLATED world)
 * - 在document_start阶段运行
 *
 * 核心功能：
 * - 消息处理和API请求代理
 * - 从localStorage读取认证信息
 * - 认证头部构建和请求发送
 */

import { TEMU_CONFIG } from '../../config/temu-config'


console.log('[Temu Main] Content-Script 加载。开始动态注入拦截器到页面主环境...');

// 动态注入 page-interceptor.js 到 MAIN world
async function injectPageInterceptor() {
  try {
    // 获取 page-interceptor.js 的 URL
    const scriptUrl = chrome.runtime.getURL('src/lib/page-interceptor.js');

    // 创建 script 标签并注入到页面
    const script = document.createElement('script');
    script.src = scriptUrl;
    script.type = 'text/javascript';

    // 注入到页面头部，确保尽早执行
    (document.head || document.documentElement).appendChild(script);

    console.log('[Temu Main] 拦截器脚本已成功注入到页面主环境');

    // 注入完成后移除 script 标签（可选）
    script.onload = () => {
      script.remove();
    };

  } catch (error) {
    console.error('[Temu Main] 注入拦截器脚本失败:', error);
  }
}

// 立即执行注入
injectPageInterceptor();


// 全局状态管理
interface GlobalState {
  antiContent: string | null
  mallId: string | null
  authHeaders: Record<string, string>
}

class TemuUnifiedScript {
  private readonly logPrefix = TEMU_CONFIG.DEBUG.LOG_PREFIX + '[Unified]'
  private state: GlobalState = {
    antiContent: null,
    mallId: null,
    authHeaders: {}
  }

  constructor() {
    console.info(`${this.logPrefix} 🚀 Temu统一Content Script启动`)
    this.init()
  }

  /**
   * 初始化
   */
  private init(): void {
    // 加载现有缓存
    this.loadExistingCache()

    // 设置消息监听
    this.setupMessageListener()

    // 暴露全局接口
    this.exposeGlobalInterface()

    console.info(`${this.logPrefix} ✅ 初始化完成`)
  }

  /**
   * 加载现有缓存
   */
  private loadExistingCache(): void {
    try {
      // 加载anti-content
      const antiContentSources = [
        TEMU_CONFIG.CACHE.KEYS.ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_ANTI_CONTENT,
        'temu_cs_anti_content'
      ]

      for (const key of antiContentSources) {
        const cached = localStorage.getItem(key)
        if (cached && cached.length > 10) {
          this.state.antiContent = cached
          console.info(`${this.logPrefix} 从缓存加载 anti-content:`, key)
          break
        }
      }

      // 加载mallId
      const mallIdSources = [
        TEMU_CONFIG.CACHE.KEYS.MALL_ID,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_MALL_ID,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_MALL_ID,
        'temu_cs_mall_id'
      ]

      for (const key of mallIdSources) {
        const cached = localStorage.getItem(key)
        if (cached) {
          this.state.mallId = cached
          console.info(`${this.logPrefix} 从缓存加载 mallId:`, key, cached)
          break
        }
      }
    } catch (error) {
      console.warn(`${this.logPrefix} 加载缓存失败:`, error)
    }
  }



  /**
   * 设置消息监听
   */
  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.info(`${this.logPrefix} 收到消息:`, request.action)

      switch (request.action) {
        case 'MAKE_AUTHENTICATED_REQUEST':
          this.handleAuthenticatedRequest(request, sendResponse)
          return true

        case 'GET_AUTH_INFO':
          this.handleGetAuthInfo(sendResponse)
          return true

        case 'GET_ANTI_CONTENT':
          sendResponse({ 
            success: true, 
            antiContent: this.state.antiContent || this.extractAntiContentFromPage() 
          })
          return true

        case 'GET_MALL_ID':
          sendResponse({ 
            success: true, 
            mallId: this.state.mallId 
          })
          return true

        case 'CHECK_LOGIN_STATUS':
          this.handleCheckLoginStatus(sendResponse)
          return true

        default:
          sendResponse({ success: false, error: '未知的action: ' + request.action })
          return true
      }
    })

    console.info(`${this.logPrefix} ✅ 消息监听器已设置`)
  }

  /**
   * 处理认证请求
   */
  private async handleAuthenticatedRequest(request: any, sendResponse: Function): Promise<void> {
    try {
      console.info(`${this.logPrefix} 处理认证请求:`, request.url)
      console.info(`${this.logPrefix} 请求选项:`, {
        method: request.options?.method,
        hasBody: !!request.options?.body,
        bodyType: typeof request.options?.body,
        bodyLength: request.options?.body?.length || 0,
        bodyPreview: request.options?.body?.substring(0, 200) || 'N/A'
      })

      // 构建认证头部
      const headers = this.buildAuthHeaders(request.options?.headers || {})
      console.info(`${this.logPrefix} 构建的认证头部:`, Object.keys(headers))

      // 使用绑定的fetch方法避免 "Illegal invocation" 错误
      const boundFetch = window.fetch.bind(window)

      // 发送请求
      const response = await boundFetch(request.url, {
        method: request.options?.method || 'POST',
        headers,
        credentials: 'include',
        body: request.options?.body
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info(`${this.logPrefix} 认证请求成功:`, request.url)

      sendResponse({ success: true, data })
    } catch (error) {
      console.error(`${this.logPrefix} 认证请求失败:`, error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      })
    }
  }

  /**
   * 处理获取认证信息
   */
  private handleGetAuthInfo(sendResponse: Function): void {
    const authInfo = {
      antiContent: this.state.antiContent || this.extractAntiContentFromPage(),
      mallId: this.state.mallId,
      authHeaders: this.state.authHeaders
    }

    sendResponse({ success: true, data: authInfo })
  }

  /**
   * 处理登录状态检查
   */
  private async handleCheckLoginStatus(sendResponse: Function): Promise<void> {
    try {
      const headers = this.buildAuthHeaders()

      // 使用绑定的fetch方法避免 "Illegal invocation" 错误
      const boundFetch = window.fetch.bind(window)

      const response = await boundFetch(TEMU_CONFIG.ENDPOINTS.USER_INFO_URL, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify({})
      })

      if (response.ok) {
        const data = await response.json()

        if (data.success && data.result) {
          sendResponse({
            success: true,
            data: {
              isLoggedIn: true,
              userInfo: data.result,
              message: '已登录 Temu 商家后台'
            }
          })
        } else {
          sendResponse({
            success: true,
            data: {
              isLoggedIn: false,
              message: '未登录或登录已过期'
            }
          })
        }
      } else {
        sendResponse({
          success: false,
          error: `登录检查失败: ${response.status}`
        })
      }
    } catch (error) {
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '登录检查异常'
      })
    }
  }

  /**
   * 构建认证头部
   */
  private buildAuthHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
    const headers = {
      ...TEMU_CONFIG.DEFAULT_HEADERS,
      ...customHeaders
    }

    // 添加anti-content
    const antiContent = this.state.antiContent || this.extractAntiContentFromPage()
    if (antiContent) {
      headers['anti-content'] = antiContent
    }

    // 添加mallId
    if (this.state.mallId) {
      headers['mallid'] = this.state.mallId
    }

    // 添加其他认证头部
    Object.assign(headers, this.state.authHeaders)

    return headers
  }



  /**
   * 从页面提取anti-content
   */
  private extractAntiContentFromPage(): string | null {
    try {
      // 1. 从全局变量获取
      const win = window as any
      if (win.antiContent) return win.antiContent
      if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

      // 2. 从meta标签获取
      const metaTag = document.querySelector('meta[name="anti-content"]')
      if (metaTag) {
        const content = metaTag.getAttribute('content')
        if (content) return content
      }

      // 3. 从脚本中搜索
      const scripts = document.querySelectorAll('script')
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML
        const patterns = [
          /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
          /antiContent\s*:\s*["']([^"']+)["']/i,
          /"anti-content":\s*"([^"]+)"/i
        ]

        for (const pattern of patterns) {
          const match = content.match(pattern)
          if (match) return match[1]
        }
      }

      return null
    } catch (error) {
      console.error(`${this.logPrefix} 从页面提取 anti-content 失败:`, error)
      return null
    }
  }

  /**
   * 暴露全局接口
   */
  private exposeGlobalInterface(): void {
    ;(window as any).temuUnified = {
      getState: () => this.state,
      getAntiContent: () => this.state.antiContent || this.extractAntiContentFromPage(),
      getMallId: () => this.state.mallId,
      clearCache: () => {
        this.state.antiContent = null
        this.state.mallId = null
        this.state.authHeaders = {}

        // 清除localStorage
        Object.values(TEMU_CONFIG.CACHE.KEYS).forEach(key => {
          localStorage.removeItem(key)
        })

        console.info(`${this.logPrefix} 🗑️ 缓存已清除`)
      },
      report: () => {
        console.log(`${this.logPrefix} 📊 状态报告:`, {
          timestamp: new Date().toLocaleString(),
          antiContent: this.state.antiContent ? this.state.antiContent.substring(0, 50) + '...' : null,
          mallId: this.state.mallId,
          authHeaders: Object.keys(this.state.authHeaders)
        })
        return this.state
      }
    }

    console.info(`${this.logPrefix} ✅ 全局接口已暴露: window.temuUnified`)
  }
}

// 立即初始化
const temuUnified = new TemuUnifiedScript()

// 导出实例（用于内部引用）
export default temuUnified
