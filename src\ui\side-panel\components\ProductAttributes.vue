<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import type { ProductAttribute } from '../../../services/configStorageService'

// Props
const props = defineProps<{
  attributes: ProductAttribute[]
}>()

// Emits
const emit = defineEmits<{
  'update:attributes': [value: ProductAttribute[]]
  'add-attribute': [attribute: Omit<ProductAttribute, 'id'>]
  'remove-attribute': [attributeId: string]
  'update-attribute': [attributeId: string, updates: Partial<ProductAttribute>]
}>()

// 新增属性表单
const newAttributeForm = ref({
  name: '',
  value: '',
  type: 'text' as 'text' | 'select' | 'number' | 'boolean',
  options: [] as string[],
  required: false
})

// 是否显示新增表单
const showAddForm = ref(false)

// 新增选项输入
const newOption = ref('')

// 属性类型选项
const attributeTypeOptions = [
  { value: 'text', label: '文本' },
  { value: 'select', label: '下拉选择' },
  { value: 'number', label: '数字' },
  { value: 'boolean', label: '布尔值' }
]

// 添加选项
const addOption = () => {
  if (newOption.value.trim() && !newAttributeForm.value.options.includes(newOption.value.trim())) {
    newAttributeForm.value.options.push(newOption.value.trim())
    newOption.value = ''
  }
}

// 删除选项
const removeOption = (index: number) => {
  newAttributeForm.value.options.splice(index, 1)
}

// 重置新增表单
const resetForm = () => {
  newAttributeForm.value = {
    name: '',
    value: '',
    type: 'text',
    options: [],
    required: false
  }
  newOption.value = ''
  showAddForm.value = false
}

// 添加属性
const handleAddAttribute = () => {
  if (!newAttributeForm.value.name.trim()) {
    return
  }

  const attribute: Omit<ProductAttribute, 'id'> = {
    name: newAttributeForm.value.name.trim(),
    value: newAttributeForm.value.value.trim(),
    type: newAttributeForm.value.type,
    options: newAttributeForm.value.type === 'select' ? [...newAttributeForm.value.options] : undefined,
    required: newAttributeForm.value.required
  }

  emit('add-attribute', attribute)
  resetForm()
}

// 删除属性
const handleRemoveAttribute = (attributeId: string) => {
  emit('remove-attribute', attributeId)
}

// 更新属性值
const handleUpdateAttributeValue = (attributeId: string, value: string) => {
  emit('update-attribute', attributeId, { value })
}

// 更新属性必填状态
const handleUpdateAttributeRequired = (attributeId: string, required: boolean) => {
  emit('update-attribute', attributeId, { required })
}

// 预设属性模板
const presetAttributes = [
  { name: '电池类型', type: 'select', options: ['不含电池', '无线充电', '锂电池', '干电池'] },
  { name: '供电方式', type: 'select', options: ['USB供电', '电池供电', '插电使用', '太阳能'] },
  { name: '接线类型', type: 'select', options: ['有线', '无线', '蓝牙', 'WiFi'] },
  { name: '材质', type: 'text', options: [] },
  { name: '颜色', type: 'text', options: [] },
  { name: '尺寸', type: 'text', options: [] },
  { name: '重量', type: 'number', options: [] }
]

// 添加预设属性
const addPresetAttribute = (preset: any) => {
  const attribute: Omit<ProductAttribute, 'id'> = {
    name: preset.name,
    value: '',
    type: preset.type,
    options: preset.options.length > 0 ? [...preset.options] : undefined,
    required: false
  }
  emit('add-attribute', attribute)
}
</script>

<template>
  <div class="product-attributes-container">
    <a-card size="small" title="🏷️ 产品属性配置" class="mb-4">
      <!-- 现有属性列表 -->
      <div v-if="attributes.length > 0" class="mb-4">
        <!-- 店小秘同步的属性 -->
        <div v-if="attributes.some(attr => attr.originalData)" class="mb-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div class="flex items-center justify-between mb-3">
              <span class="text-blue-700 font-medium text-sm">📋 产品属性</span>
              <a-tag color="blue" size="small">店小秘同步</a-tag>
            </div>

            <div class="space-y-2">
              <div
                v-for="attribute in attributes.filter(attr => attr.originalData)"
                :key="attribute.id"
                class="text-sm"
              >
                <span class="text-gray-700 font-medium">{{ attribute.name }}:</span>
                <span class="text-gray-900 ml-2">{{ attribute.value || '未设置' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 自定义属性 -->
        <div v-if="attributes.some(attr => !attr.originalData)" class="space-y-3">
          <div class="text-sm font-medium text-gray-700 mb-2">🔧 自定义属性</div>
          <div
            v-for="attribute in attributes.filter(attr => !attr.originalData)"
            :key="attribute.id"
            class="border rounded-lg p-3 bg-gray-50"
          >
            <div class="flex items-start justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="font-medium text-sm">{{ attribute.name }}</span>
                <a-tag :color="attribute.type === 'select' ? 'green' : 'default'" size="small">
                  {{ attributeTypeOptions.find(opt => opt.value === attribute.type)?.label }}
                </a-tag>
                <a-tag v-if="attribute.required" color="red" size="small">必填</a-tag>
              </div>
              <a-button
                type="text"
                danger
                size="small"
                @click="handleRemoveAttribute(attribute.id)"
              >
                删除
              </a-button>
            </div>

            <div class="space-y-2">
              <!-- 文本/数字输入 -->
              <div v-if="attribute.type === 'text' || attribute.type === 'number'">
                <a-input
                  :value="attribute.value"
                  @change="(e) => handleUpdateAttributeValue(attribute.id, e.target.value)"
                  :placeholder="`请输入${attribute.name}`"
                  :type="attribute.type === 'number' ? 'number' : 'text'"
                />
              </div>

              <!-- 下拉选择 -->
              <div v-else-if="attribute.type === 'select'">
                <a-select
                  :value="attribute.value"
                  @change="(value) => handleUpdateAttributeValue(attribute.id, value)"
                  :placeholder="`请选择${attribute.name}`"
                  style="width: 100%"
                  allow-clear
                >
                  <a-select-option
                    v-for="option in attribute.options"
                    :key="option"
                    :value="option"
                  >
                    {{ option }}
                  </a-select-option>
                </a-select>
              </div>

              <!-- 布尔值 -->
              <div v-else-if="attribute.type === 'boolean'">
                <a-switch
                  :checked="attribute.value === 'true'"
                  @change="(checked) => handleUpdateAttributeValue(attribute.id, checked.toString())"
                  :checked-children="attribute.name"
                  :un-checked-children="`非${attribute.name}`"
                />
              </div>

              <!-- 必填设置 -->
              <div class="flex items-center space-x-2">
                <a-checkbox
                  :checked="attribute.required"
                  @change="(e) => handleUpdateAttributeRequired(attribute.id, e.target.checked)"
                >
                  必填项
                </a-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-8 text-gray-500">
        <div class="text-lg mb-2">📝</div>
        <div class="text-sm">暂无产品属性</div>
        <div class="text-xs">点击下方按钮添加属性</div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between items-center pt-4 border-t">
        <div class="space-x-2">
          <a-button
            type="primary"
            size="small"
            @click="showAddForm = true"
          >
            ➕ 自定义属性
          </a-button>
          
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item
                  v-for="preset in presetAttributes"
                  :key="preset.name"
                  @click="addPresetAttribute(preset)"
                >
                  {{ preset.name }}
                </a-menu-item>
              </a-menu>
            </template>
            <a-button size="small">
              📋 预设属性 <DownOutlined />
            </a-button>
          </a-dropdown>
        </div>

        <a-tag color="blue" size="small">
          共 {{ attributes.length }} 个属性
        </a-tag>
      </div>
    </a-card>

    <!-- 新增属性表单 -->
    <a-modal
      v-model:open="showAddForm"
      title="添加产品属性"
      @ok="handleAddAttribute"
      @cancel="resetForm"
      :ok-button-props="{ disabled: !newAttributeForm.name.trim() }"
    >
      <a-form layout="vertical" class="space-y-4">
        <a-form-item label="属性名称" required>
          <a-input
            v-model:value="newAttributeForm.name"
            placeholder="请输入属性名称，如：电池类型"
          />
        </a-form-item>

        <a-form-item label="属性类型" required>
          <a-select
            v-model:value="newAttributeForm.type"
            placeholder="请选择属性类型"
          >
            <a-select-option
              v-for="option in attributeTypeOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 下拉选择选项配置 -->
        <a-form-item v-if="newAttributeForm.type === 'select'" label="选项配置">
          <div class="space-y-2">
            <div class="flex space-x-2">
              <a-input
                v-model:value="newOption"
                placeholder="输入选项内容"
                @press-enter="addOption"
              />
              <a-button @click="addOption" :disabled="!newOption.trim()">
                添加
              </a-button>
            </div>
            
            <div v-if="newAttributeForm.options.length > 0" class="space-y-1">
              <div
                v-for="(option, index) in newAttributeForm.options"
                :key="index"
                class="flex items-center justify-between bg-gray-50 px-2 py-1 rounded"
              >
                <span class="text-sm">{{ option }}</span>
                <a-button
                  type="text"
                  size="small"
                  @click="removeOption(index)"
                >
                  删除
                </a-button>
              </div>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="默认值">
          <a-input
            v-model:value="newAttributeForm.value"
            placeholder="请输入默认值（可选）"
          />
        </a-form-item>

        <a-form-item>
          <a-checkbox v-model:checked="newAttributeForm.required">
            设为必填项
          </a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.product-attributes-container {
  width: 100%;
}

.product-attributes-container :deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}

.product-attributes-container :deep(.ant-card-body) {
  padding: 16px;
}
</style>
