/* 胡建大卖家扩展 - 设计规范 */
/* 基于 Ant Design Vue + Tailwind CSS 的设计系统 */

:root {
  /* Ant Design Vue 主色调 */
  --ant-primary-color: #1677ff;
  --ant-primary-color-hover: #4096ff;
  --ant-primary-color-active: #0958d9;

  /* Ant Design Vue 功能色 */
  --ant-success-color: #52c41a;
  --ant-warning-color: #faad14;
  --ant-error-color: #ff4d4f;
  --ant-info-color: #1677ff;

  /* 文本颜色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;

  /* 边框色 */
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  --border-light: #e6f4ff;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* 圆角 - 与 Ant Design Vue 保持一致 */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
}

/* 与 Ant Design Vue 配合的工具类 */
.card-custom {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-lg);
  transition: all 0.2s ease-in-out;
}

.card-custom:hover {
  box-shadow: var(--shadow-md);
}

/* 文本工具类 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

/* 状态颜色 */
.status-success {
  color: var(--ant-success-color);
}

.status-warning {
  color: var(--ant-warning-color);
}

.status-error {
  color: var(--ant-error-color);
}

.status-info {
  color: var(--ant-info-color);
}

/* 简单的工具类 */
.transition-all {
  transition: all 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
