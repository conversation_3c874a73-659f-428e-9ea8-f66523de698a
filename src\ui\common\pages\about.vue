<template>
  <div class="p-6 max-w-4xl mx-auto">
    <RouterLinkUp />

    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">关于胡建大卖家</h1>
      <p class="text-lg text-gray-600">专业的Temu店铺管理Chrome扩展</p>
    </div>

    <div class="grid md:grid-cols-2 gap-8 mb-8">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4 text-blue-600">产品介绍</h2>
        <p class="text-gray-700 leading-relaxed">
          胡建大卖家是一款专为Temu平台卖家设计的Chrome浏览器扩展程序。
          我们致力于为跨境电商卖家提供高效、便捷的店铺管理工具，
          帮助您轻松管理商品、优化运营、提升销售业绩。
        </p>
      </div>

      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4 text-green-600">核心功能</h2>
        <ul class="text-gray-700 space-y-2">
          <li>• 商品批量采集与上传</li>
          <li>• 智能价格监控与调整</li>
          <li>• 多店铺统一管理</li>
          <li>• 数据分析与报表</li>
          <li>• 自动化运营工具</li>
        </ul>
      </div>
    </div>

    <div class="bg-gray-50 rounded-lg p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4 text-purple-600">我们的使命</h2>
      <p class="text-gray-700 leading-relaxed">
        让每一位跨境电商卖家都能享受到专业、高效的店铺管理体验。
        通过技术创新，简化复杂的运营流程，让您专注于业务发展和客户服务。
      </p>
    </div>

    <div class="flex flex-wrap gap-4 justify-center">
      <RouterLink
        to="/common/change-log"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        更新日志
      </RouterLink>

      <RouterLink
        to="/common/help"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
      >
        使用帮助
      </RouterLink>

      <RouterLink
        to="/common/privacy-policy"
        class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
      >
        隐私政策
      </RouterLink>

      <RouterLink
        to="/common/terms-of-service"
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
      >
        服务条款
      </RouterLink>
    </div>
  </div>
</template>
