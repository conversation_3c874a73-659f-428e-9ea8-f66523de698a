<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  defineProps,
  defineEmits,
  onMounted,
  onUnmounted,
} from "vue"
import type {
  ShopAccount,
  FreightTemplate,
  ProductCategory,
} from "../../../services/dxm/dianxiaomiDetectionService"
import CategoryCascader from "./CategoryCascader.vue"
import ProductAttributes from "./ProductAttributes.vue"
import { getSiteOptions, getSiteById } from "../../../config/temuSites"
import configStorageService from "../../../services/configStorageService"
import type { ProductAttribute } from "../../../services/configStorageService"
import productConfigService from "../../../services/productConfigService"
import { message } from "ant-design-vue"
import {
  dianxiaomiTokenService,
  checkDianxiaomiToken,
  getDianxiaomiTokenStatus,
  clearDianxiaomiTokenCache,
  getDianxiaomiCookieInfo,
  inferLoginStatusFromSuccess,
  type TokenStatus,
} from "../../../services/dxm/dianxiaomiTokenService"
import { saveTokenToCache } from "../../../services/dxm/dianxiaomiTokenCache"
import { CookieHelper } from "../../../utils/cookieHelper"

// Props
const props = defineProps<{
  basicForm: {
    erpPlatform: string
    publishSite: string
    shopAccount: string
    publishStatus: string
    businessSite: string
    warehouse: string | string[]
    freightTemplate: string
    shippingTime: string
    venue: string
    productCategory: string
    productAttributes: any[]
    mainVideo?: string
    publishStatusOptions?: any[]
    shippingTimeOptions?: any[]
    venueOptions?: any[]
  }
  dianxiaomiLoginStatus: {
    isLoggedIn: boolean
    message: string
    loading: boolean
  }
  shopAccounts: ShopAccount[]
  warehouses: Record<string, Record<string, Record<string, string>>>
  freightTemplates: FreightTemplate[]
  productCategories: ProductCategory[]
  loadingStates: {
    shopAccounts: boolean
    warehouses: boolean
    freightTemplates: boolean
    productCategories: boolean
  }
  currentTemuShop: string
}>()

// Emits
const emit = defineEmits<{
  "update:basicForm": [value: typeof props.basicForm]
  "check-login": []
  "open-erp": []
  "load-shop-accounts": []
  "load-warehouses": []
  "load-freight-templates": []
  "load-product-categories": []
  "shop-account-change": [shopId: string]
  "category-change": [category: ProductCategory | null]
  "load-categories-by-parent": [parentId?: number]
  "save-settings": []
}>()

// Token状态管理
const tokenStatus = getDianxiaomiTokenStatus()
const tokenCheckLoading = ref(false)
const tokenAutoCheckInterval = ref<number | null>(null)

// 站点选项
const siteOptions = computed(() => getSiteOptions())

// 计算属性
const getWarehouseOptions = computed(() => {
  const options: Array<{
    value: string
    label: string
    shopId: string
    site: string
  }> = []

  Object.entries(props.warehouses).forEach(([shopId, shopWarehouses]) => {
    Object.entries(shopWarehouses).forEach(([site, siteWarehouses]) => {
      Object.entries(siteWarehouses).forEach(([warehouseId, warehouseName]) => {
        options.push({
          value: warehouseId,
          label: `${warehouseName} (店铺: ${shopId}, 站点: ${site})`,
          shopId,
          site,
        })
      })
    })
  })

  return options
})

// 检查授权是否即将过期（30天内）
const isExpiringSoon = (expireTime: string): boolean => {
  if (!expireTime) return false
  try {
    const expireDate = new Date(expireTime)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  } catch {
    return false
  }
}

// 更新表单数据
const updateForm = (key: string, value: any) => {
  const newForm = { ...props.basicForm, [key]: value }
  emit("update:basicForm", newForm)
}

// 事件处理
const handleShopAccountChange = (shopId: string) => {
  updateForm("shopAccount", shopId)
  emit("shop-account-change", shopId)
}

// 级联选择器状态
const selectedCategoryPath = ref<number[]>([])
const selectedCategoryLabels = ref<string[]>([])

const handleCategoryChange = (category: ProductCategory | null) => {
  emit("category-change", category)
}

const handleLoadCategoriesByParent = async (
  parentId?: number,
): Promise<ProductCategory[]> => {
  emit("load-categories-by-parent", parentId)
  return []
}

// 级联选择器变化处理
interface CascaderOption {
  value: number
  label: string
  isLeaf?: boolean
  children?: CascaderOption[]
}

const handleCascaderChange = (
  value: number[],
  selectedOptions: CascaderOption[],
) => {
  console.info("[BasicSettings] 分类选择变化:", { value, selectedOptions })

  selectedCategoryPath.value = value
  selectedCategoryLabels.value = selectedOptions.map((option) => option.label)

  // 更新表单中的分类名称（使用最后一级的名称）
  const categoryName =
    selectedOptions.length > 0
      ? selectedOptions[selectedOptions.length - 1].label
      : ""

  console.info("[BasicSettings] 更新表单分类字段:", categoryName)
  updateForm("productCategory", categoryName)

  // 强制触发表单验证
  if (categoryName) {
    // 确保表单字段被正确设置
    console.info("[BasicSettings] 分类选择完成，表单字段已更新")
  }

  // 如果选择了完整的分类路径，显示商品配置按钮
  if (value.length > 0 && selectedOptions.length > 0) {
    const lastCategory = selectedOptions[selectedOptions.length - 1]
    if (lastCategory.isLeaf) {
      // 选择了叶子节点，可以进行商品配置
      console.info("[BasicSettings] 选择了完整分类路径，可以进行商品配置:", {
        categoryId: lastCategory.value,
        categoryName: lastCategory.label,
        fullPath: selectedCategoryLabels.value.join(" / "),
      })
    }
  }

  // 创建一个模拟的 ProductCategory 对象用于向上传递
  if (selectedOptions.length > 0) {
    const lastOption = selectedOptions[selectedOptions.length - 1]
    const mockCategory: ProductCategory = {
      id: lastOption.value,
      catId: lastOption.value,
      catName: lastOption.label,
      parentCatId:
        selectedOptions.length > 1
          ? selectedOptions[selectedOptions.length - 2].value
          : 0,
      catLevel: selectedOptions.length,
      isLeaf: lastOption.isLeaf || false,
      catType: 0,
      isHidden: false,
      hiddenType: 0,
      classType: -1,
      enabledModelType: -1,
      enabledModel: false,
      needGuideFile: false,
      hasUpdate: 1,
      deleted: 0,
      createTime: Date.now(),
      updateTime: Date.now(),
      siteId: null,
      nodePathId: null,
      nodePath: null,
      classId: null,
      parentClassId: null,
      relatedClassIds: null,
    }
    emit("category-change", mockCategory)
  } else {
    emit("category-change", null)
  }
}

const handleSave = () => {
  // 保存基础设置时，同时保存当前的token状态到跨页面缓存
  if (tokenStatus.value.isValid && tokenStatus.value.isLoggedIn) {
    saveTokenToCache(tokenStatus.value)
    console.info("[BasicSettings] 保存基础设置时，token状态已保存到跨页面缓存")
    message.success("基础设置和Token状态已保存")
  }
  emit("save-settings")
}

// 智能推断登录状态（当其他API成功时）
const handleAPISuccess = (apiName: string) => {
  console.info(`[BasicSettings] 检测到${apiName}API成功，推断登录状态`)

  // 如果当前token状态无效，但有API成功，说明实际是登录的
  if (!tokenStatus.value.isValid) {
    const inferredStatus = inferLoginStatusFromSuccess(apiName)
    // 保存推断的token状态到跨页面缓存
    saveTokenToCache(inferredStatus)
    console.info("[BasicSettings] 推断的token状态已保存到跨页面缓存")
    message.success(`基于${apiName}成功推断店小秘已登录`)
  }
}

// 监听props变化，检测API成功情况
watch(
  () => props.dianxiaomiLoginStatus,
  (newStatus, oldStatus) => {
    if (newStatus.isLoggedIn && !oldStatus?.isLoggedIn) {
      console.info("[BasicSettings] 检测到登录状态变化，推断token有效")
      handleAPISuccess("登录检测")
    }
  },
  { deep: true },
)

// 监听运费模板变化，如果有数据说明API调用成功
watch(
  () => props.freightTemplates,
  (newTemplates, oldTemplates) => {
    if (
      newTemplates &&
      newTemplates.length > 0 &&
      (!oldTemplates || oldTemplates.length === 0)
    ) {
      console.info("[BasicSettings] 检测到运费模板数据，推断token有效")
      handleAPISuccess("运费模板")
    }
  },
  { deep: true },
)

// 监听店铺账号变化，如果有数据说明API调用成功
watch(
  () => props.shopAccounts,
  (newAccounts, oldAccounts) => {
    if (
      newAccounts &&
      newAccounts.length > 0 &&
      (!oldAccounts || oldAccounts.length === 0)
    ) {
      console.info("[BasicSettings] 检测到店铺账号数据，推断token有效")
      handleAPISuccess("店铺账号")
    }
  },
  { deep: true },
)

// 生命周期钩子
onMounted(async () => {
  console.info("[BasicSettings] 组件已挂载，开始初始化token检测")

  // 如果已经有数据，说明之前的API调用成功，推断登录状态
  if (props.freightTemplates && props.freightTemplates.length > 0) {
    console.info("[BasicSettings] 发现现有运费模板数据，推断token有效")
    handleAPISuccess("运费模板（初始化）")
  } else if (props.shopAccounts && props.shopAccounts.length > 0) {
    console.info("[BasicSettings] 发现现有店铺账号数据，推断token有效")
    handleAPISuccess("店铺账号（初始化）")
  } else {
    // 没有现有数据，进行正常检测
    await checkTokenStatus(false)
  }

  // 启动自动检测
  startAutoCheck()

  // 确保当前有效的token状态被保存到跨页面缓存
  if (tokenStatus.value.isValid && tokenStatus.value.isLoggedIn) {
    saveTokenToCache(tokenStatus.value)
    console.info("[BasicSettings] 初始化时，有效的token状态已保存到跨页面缓存")
  }

  // 获取店小秘配置状态
  await getDxmConfigStatus()

  // 加载保存的分类和属性信息
  await loadSavedCategoryAndAttributes()
})

onUnmounted(() => {
  console.info("[BasicSettings] 组件即将卸载，清理资源")
  // 停止自动检测
  stopAutoCheck()
})

// 测试配置
const testConfiguration = async () => {
  try {
    message.info("开始测试配置...")

    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()

    if (configCheck.isComplete) {
      // 获取完整配置
      const fullConfig = await configStorageService.getFullConfig()

      console.info("[BasicSettings] 配置测试结果:", {
        isComplete: true,
        basicConfig: fullConfig.basic,
        productConfig: fullConfig.product,
      })

      message.success("配置测试通过！所有必填项都已配置完成。")
    } else {
      console.warn("[BasicSettings] 配置不完整:", configCheck.missingFields)
      message.warning(
        `配置不完整，缺少以下项目：${configCheck.missingFields.join(", ")}`,
      )
    }
  } catch (error) {
    console.error("[BasicSettings] 配置测试失败:", error)
    message.error(
      "配置测试失败: " + (error instanceof Error ? error.message : "未知错误"),
    )
  }
}

// 跳转到店小秘商品配置页面
const openProductConfig = () => {
  if (!props.basicForm.shopAccount) {
    message.error("请先选择店铺账号")
    return
  }

  if (selectedCategoryPath.value.length === 0) {
    message.error("请先选择商品分类")
    return
  }

  const categoryId =
    selectedCategoryPath.value[selectedCategoryPath.value.length - 1]
  const shopId = props.basicForm.shopAccount

  // 构建店小秘商品配置页面URL
  const configUrl = `https://www.dianxiaomi.com/userTemplate/popTemuAdd.htm?shopId=${shopId}&from=dmo&pushtype=popTemuProduct&categoryId=${categoryId}`

  console.info("[BasicSettings] 打开商品配置页面:", {
    url: configUrl,
    shopId,
    categoryId,
    categoryPath: selectedCategoryLabels.value.join(" / "),
  })

  // 打开新标签页
  window.open(configUrl, "_blank")

  message.info("已打开商品配置页面，请在新页面中完成配置并保存")
}

// 计算属性：是否可以进行商品配置
const canConfigProduct = computed(() => {
  return (
    props.basicForm.shopAccount &&
    selectedCategoryPath.value.length > 0 &&
    (tokenStatus.value.isValid || props.dianxiaomiLoginStatus.isLoggedIn)
  )
})

// 商品配置状态
const productConfigStatus = ref<{
  hasConfig: boolean
  config?: any
  loading: boolean
}>({
  hasConfig: false,
  config: null,
  loading: false,
})

// 检查商品配置状态
const checkProductConfigStatus = async () => {
  if (!props.basicForm.shopAccount || selectedCategoryPath.value.length === 0) {
    productConfigStatus.value = {
      hasConfig: false,
      config: null,
      loading: false,
    }
    return
  }

  productConfigStatus.value.loading = true
  try {
    const categoryId =
      selectedCategoryPath.value[selectedCategoryPath.value.length - 1]
    const config = await productConfigService.getProductConfig(
      props.basicForm.shopAccount,
      categoryId,
    )

    productConfigStatus.value = {
      hasConfig: config !== null,
      config: config,
      loading: false,
    }

    console.info(
      "[BasicSettings] 商品配置状态检查完成:",
      productConfigStatus.value,
    )
  } catch (error) {
    console.error("[BasicSettings] 检查商品配置状态失败:", error)
    productConfigStatus.value = {
      hasConfig: false,
      config: null,
      loading: false,
    }
  }
}

// 监听分类选择变化，检查配置状态
watch(
  [() => props.basicForm.shopAccount, selectedCategoryPath],
  () => {
    checkProductConfigStatus()
  },
  { immediate: true },
)

// 测试分类API - 直接调用
const testCategoryAPI = async () => {
  try {
    if (!props.basicForm.shopAccount) {
      message.error("请先选择店铺账号")
      return
    }

    message.info("开始测试分类API...")
    console.info(
      "[BasicSettings] 开始直接测试分类API，店铺ID:",
      props.basicForm.shopAccount,
    )

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append("shopId", props.basicForm.shopAccount)
    formData.append("categoryParentId", "0")

    console.info("[BasicSettings] 请求参数:", {
      shopId: props.basicForm.shopAccount,
      categoryParentId: 0,
    })

    // 直接调用店小秘API
    const response = await fetch(
      "https://www.dianxiaomi.com/api/popTemuCategory/list.json",
      {
        method: "POST",
        credentials: "include",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/x-www-form-urlencoded",
          "X-Requested-With": "XMLHttpRequest",
        },
        body: formData.toString(),
      },
    )

    console.info("[BasicSettings] API响应状态:", response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info("[BasicSettings] API响应数据:", data)

    if (data.code === 0) {
      message.success(
        `分类API测试成功！获取到 ${data.data?.length || 0} 个根分类`,
      )
      console.info("[BasicSettings] 根分类数据:", data.data)
    } else {
      message.error(`分类API测试失败: ${data.msg || "未知错误"}`)
    }
  } catch (error) {
    console.error("[BasicSettings] 分类API测试失败:", error)
    message.error(
      "分类API测试失败: " +
        (error instanceof Error ? error.message : "未知错误"),
    )
  }
}

// Token检测相关方法
const checkTokenStatus = async (forceRefresh = false) => {
  try {
    tokenCheckLoading.value = true
    console.info("[BasicSettings] 开始检查店小秘token状态...")

    if (forceRefresh) {
      message.info("正在刷新token状态...")
    } else {
      message.info("正在检查token状态...")
    }

    const status = await checkDianxiaomiToken(forceRefresh)
    console.info("[BasicSettings] Token状态检查结果:", status)

    if (status.isValid && status.isLoggedIn) {
      message.success(status.message)
      // 保存token到跨页面缓存
      saveTokenToCache(status)
      console.info("[BasicSettings] Token状态已保存到跨页面缓存")
      // 触发登录状态更新事件
      emit("check-login")
    } else {
      message.error(status.message)
    }

    return status
  } catch (error) {
    console.error("[BasicSettings] Token状态检查失败:", error)
    message.error(
      "Token状态检查失败: " +
        (error instanceof Error ? error.message : "未知错误"),
    )
    return null
  } finally {
    tokenCheckLoading.value = false
  }
}

// 显示Cookie详情
const showCookieDetails = () => {
  try {
    console.info("[BasicSettings] 开始获取Cookie详情...")

    // 使用新的Cookie辅助工具
    const cookieInfo = CookieHelper.getDetailedCookieInfo()
    const validation = CookieHelper.validateDianxiaomiCookies()

    console.info("[BasicSettings] Cookie详情:", cookieInfo)
    console.info("[BasicSettings] Cookie验证:", validation)

    if (cookieInfo.cookieStats.hasValidCookies) {
      const cookieList = Object.entries(cookieInfo.dxmCookies)
        .map(
          ([key, value]) =>
            `${key}: ${value.substring(0, 20)}${value.length > 20 ? "..." : ""}`,
        )
        .join("\n")

      message.info(
        `Cookie检测结果:\n域名: ${cookieInfo.domain}\n总Cookie数: ${cookieInfo.cookieStats.totalCount}\n店小秘Cookie数: ${cookieInfo.cookieStats.dxmCount}\n验证分数: ${validation.score}%\n\n店小秘Cookie:\n${cookieList}`,
      )
    } else {
      message.warning(
        `Cookie检测结果:\n域名: ${cookieInfo.domain}\n总Cookie数: ${cookieInfo.cookieStats.totalCount}\n店小秘Cookie数: 0\n\n未发现店小秘相关Cookie，请确保已在店小秘网站登录`,
      )
    }
  } catch (error) {
    console.error("[BasicSettings] 获取Cookie详情失败:", error)
    message.error(
      "获取Cookie详情失败: " +
        (error instanceof Error ? error.message : "未知错误"),
    )
  }
}

// 清除Token缓存
const clearTokenCache = () => {
  try {
    clearDianxiaomiTokenCache()
    message.success("Token缓存已清除")
    console.info("[BasicSettings] Token缓存已清除")
  } catch (error) {
    console.error("[BasicSettings] 清除Token缓存失败:", error)
    message.error("清除Token缓存失败")
  }
}

// 手动保存Token到跨页面缓存
const saveTokenToCrossPageCache = () => {
  try {
    if (tokenStatus.value.isValid && tokenStatus.value.isLoggedIn) {
      saveTokenToCache(tokenStatus.value)
      message.success("Token状态已保存到跨页面缓存，现在可以在测试页面使用")
      console.info(
        "[BasicSettings] 手动保存token到跨页面缓存:",
        tokenStatus.value,
      )

      // 验证保存结果
      setTimeout(() => {
        const saved = localStorage.getItem("dianxiaomi_token_cache")
        if (saved) {
          const data = JSON.parse(saved)
          console.info(
            "[BasicSettings] 保存验证 - Cookie数量:",
            Object.keys(data.cookies).length,
          )
          console.info("[BasicSettings] 保存验证 - 缓存数据:", data)
        }
      }, 100)
    } else {
      message.warning("当前Token状态无效，请先检测Token状态")
    }
  } catch (error) {
    console.error("[BasicSettings] 手动保存Token失败:", error)
    message.error("保存Token失败")
  }
}

// 查看跨页面缓存详情
const showCrossPageCacheDetails = () => {
  try {
    const cached = localStorage.getItem("dianxiaomi_token_cache")
    if (cached) {
      const data = JSON.parse(cached)
      const cookieCount = Object.keys(data.cookies).length
      const dxmCookies = Object.keys(data.cookies).filter(
        (key) =>
          key.includes("dxm_") ||
          key.includes("JSESSIONID") ||
          key.includes("MYJ_") ||
          key.includes("dianxiaomi"),
      )

      message.info(
        `跨页面缓存详情:\n总Cookie数: ${cookieCount}\n店小秘Cookie数: ${dxmCookies.length}\n版本: ${data.version}\n时间: ${new Date(data.timestamp).toLocaleString()}`,
      )
      console.info("[BasicSettings] 跨页面缓存详情:", data)
    } else {
      message.warning("未找到跨页面缓存")
    }
  } catch (error) {
    console.error("[BasicSettings] 查看缓存详情失败:", error)
    message.error("查看缓存详情失败")
  }
}

// 启动自动检测
const startAutoCheck = () => {
  if (tokenAutoCheckInterval.value) {
    clearInterval(tokenAutoCheckInterval.value)
  }

  // 每30秒自动检查一次token状态
  tokenAutoCheckInterval.value = window.setInterval(() => {
    checkTokenStatus(false)
  }, 30000)

  console.info("[BasicSettings] 已启动token自动检测")
}

// 停止自动检测
const stopAutoCheck = () => {
  if (tokenAutoCheckInterval.value) {
    clearInterval(tokenAutoCheckInterval.value)
    tokenAutoCheckInterval.value = null
    console.info("[BasicSettings] 已停止token自动检测")
  }
}

// 兼容原有的检查登录状态方法
const checkLoginStatus = () => checkTokenStatus(true)

// 店小秘配置状态
const dxmConfigStatus = ref({
  hasConfig: false,
  lastUpdated: '',
  categoryName: '',
  shopId: '',
  attributesCount: 0
})

// 获取店小秘配置状态
const getDxmConfigStatus = async () => {
  try {
    const status = await configStorageService.getDxmConfigStatus()
    dxmConfigStatus.value = {
      hasConfig: status.hasConfig,
      lastUpdated: status.lastUpdated || '',
      categoryName: status.categoryName || '',
      shopId: status.shopId || '',
      attributesCount: status.attributesCount || 0
    }
    console.info('[BasicSettings] 店小秘配置状态:', status)
  } catch (error) {
    console.error('[BasicSettings] 获取店小秘配置状态失败:', error)
  }
}

// 同步店小秘配置
const syncDxmConfig = async () => {
  try {
    message.loading('正在同步店小秘配置...', 0)

    await configStorageService.syncDxmConfigToBasic()

    // 触发保存事件，让父组件重新加载配置
    emit('save-settings')
    await getDxmConfigStatus()

    message.destroy()
    message.success('店小秘配置同步成功')
  } catch (error) {
    message.destroy()
    console.error('[BasicSettings] 同步店小秘配置失败:', error)
    message.error('同步店小秘配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 清除店小秘配置
const clearDxmConfig = async () => {
  try {
    await chrome.storage.local.remove('dxm-config-data')
    await getDxmConfigStatus()
    message.success('店小秘配置已清除')
  } catch (error) {
    console.error('[BasicSettings] 清除店小秘配置失败:', error)
    message.error('清除店小秘配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 产品属性处理方法
const handleAddProductAttribute = async (attribute: Omit<ProductAttribute, 'id'>) => {
  try {
    await configStorageService.addProductAttribute(attribute)
    // 重新加载配置以更新界面
    const config = await configStorageService.getBasicConfig()
    updateForm('productAttributes', config.productAttributes)
    message.success(`产品属性 "${attribute.name}" 添加成功`)
  } catch (error) {
    console.error('[BasicSettings] 添加产品属性失败:', error)
    message.error('添加产品属性失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

const handleRemoveProductAttribute = async (attributeId: string) => {
  try {
    await configStorageService.removeProductAttribute(attributeId)
    // 重新加载配置以更新界面
    const config = await configStorageService.getBasicConfig()
    updateForm('productAttributes', config.productAttributes)
    message.success('产品属性删除成功')
  } catch (error) {
    console.error('[BasicSettings] 删除产品属性失败:', error)
    message.error('删除产品属性失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

const handleUpdateProductAttribute = async (attributeId: string, updates: Partial<ProductAttribute>) => {
  try {
    await configStorageService.updateProductAttribute(attributeId, updates)
    // 重新加载配置以更新界面
    const config = await configStorageService.getBasicConfig()
    updateForm('productAttributes', config.productAttributes)
    console.info('[BasicSettings] 产品属性更新成功:', { attributeId, updates })
  } catch (error) {
    console.error('[BasicSettings] 更新产品属性失败:', error)
    message.error('更新产品属性失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 加载保存的分类和属性信息
const loadSavedCategoryAndAttributes = async () => {
  try {
    console.info('[BasicSettings] 开始加载保存的分类和属性信息...')

    // 从 temu-extension-basic-config 中读取数据
    const result = await chrome.storage.local.get(['temu-extension-basic-config'])
    const basicConfig = result['temu-extension-basic-config']

    if (basicConfig) {
      console.info('[BasicSettings] 找到保存的配置:', basicConfig)

      // 加载分类信息
      if (basicConfig.categoryInfo) {
        const categoryInfo = basicConfig.categoryInfo
        console.info('[BasicSettings] 加载分类信息:', categoryInfo)

        // 设置分类路径
        if (categoryInfo.categoryPath && Array.isArray(categoryInfo.categoryPath)) {
          selectedCategoryPath.value = categoryInfo.categoryPath.map((item: any) => parseInt(item.id))
          selectedCategoryLabels.value = categoryInfo.categoryPath.map((item: any) => item.name)

          // 更新表单中的分类名称
          updateForm('productCategory', categoryInfo.categoryName)

          console.info('[BasicSettings] 分类路径已设置:', {
            path: selectedCategoryPath.value,
            labels: selectedCategoryLabels.value,
            categoryName: categoryInfo.categoryName
          })
        }
      }

      // 加载产品属性
      if (basicConfig.productAttributes) {
        const attributes = basicConfig.productAttributes
        console.info('[BasicSettings] 加载产品属性:', attributes)

        // 转换为组件需要的格式
        const convertedAttributes = Object.keys(attributes).map(key => {
          const attr = attributes[key]
          return {
            id: attr.id || key,
            name: attr.name || attr.propName,
            value: attr.value || attr.propValue || '',
            type: 'select' as const,
            options: attr.options ? Object.values(attr.options) : undefined,
            required: attr.required || false,
            // 保留原始数据用于后续处理
            originalData: attr
          }
        })

        // 更新表单中的产品属性
        updateForm('productAttributes', convertedAttributes)

        console.info('[BasicSettings] 产品属性已设置:', convertedAttributes)
      }

      message.success('已加载保存的分类和属性配置')
    } else {
      console.info('[BasicSettings] 未找到保存的配置')
    }
  } catch (error) {
    console.error('[BasicSettings] 加载保存的分类和属性信息失败:', error)
    message.error('加载配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}
</script>

<template>
  <div class="basic-settings-container">
    <a-form
      :model="basicForm"
      layout="horizontal"
      :label-col="{ style: 'width: 120px' }"
      :wrapper-col="{ span: 18 }"
      @finish="handleSave"
      class="space-y-4"
    >
      <!-- ERP平台 -->
      <a-form-item
        label="ERP平台"
        name="erpPlatform"
        :rules="[{ required: true, message: '请选择ERP平台' }]"
      >
        <a-select
          :value="basicForm.erpPlatform"
          @change="(value) => updateForm('erpPlatform', value)"
          placeholder="请选择ERP平台"
        >
          <a-select-option value="店小秘">店小秘</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 发布站点 -->
      <a-form-item label="发布站点">
        <a-input
          :value="currentTemuShop"
          readonly
          class="bg-gray-50"
        >
          <template #addonBefore>
            <a-tag
              :color="currentTemuShop.includes('半托') ? 'blue' : 'green'"
              size="small"
            >
              {{ currentTemuShop.includes("半托") ? "半托" : "全托" }}
            </a-tag>
          </template>
        </a-input>
      </a-form-item>

      <!-- 店小秘Token状态检测 - 增强版 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          class="mb-4"
        >
          <template #title>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium">🔐 店小秘Token状态</span>
              <a-tag
                :color="tokenStatus.isValid ? 'success' : 'error'"
                size="small"
              >
                {{ tokenStatus.isValid ? "有效" : "无效" }}
              </a-tag>
            </div>
          </template>

          <template #extra>
            <a-space size="small">
              <a-button
                size="small"
                :loading="tokenCheckLoading"
                @click="checkTokenStatus(true)"
              >
                {{ tokenCheckLoading ? "检测中..." : "🔄 刷新" }}
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      key="cookie"
                      @click="showCookieDetails"
                    >
                      🍪 查看Cookie详情
                    </a-menu-item>
                    <a-menu-item
                      key="save-token"
                      @click="saveTokenToCrossPageCache"
                    >
                      💾 保存Token到缓存
                    </a-menu-item>
                    <a-menu-item
                      key="view-cache"
                      @click="showCrossPageCacheDetails"
                    >
                      📋 查看缓存详情
                    </a-menu-item>
                    <a-menu-item
                      key="clear"
                      @click="clearTokenCache"
                    >
                      🗑️ 清除缓存
                    </a-menu-item>
                    <a-menu-item
                      key="open-erp"
                      @click="emit('open-erp')"
                    >
                      🌐 打开店小秘ERP
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">⚙️ 更多</a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-card>
      </a-form-item>

      <!-- 兼容原有的登录状态检测（当token无效时显示） -->
      <a-form-item
        v-if="!tokenStatus.isValid && !dianxiaomiLoginStatus.isLoggedIn"
        :wrapper-col="{ span: 24, offset: 0 }"
      >
        <a-alert
          type="warning"
          show-icon
          class="mb-4"
        >
          <template #message>
            <div class="flex items-center justify-between">
              <span>{{ dianxiaomiLoginStatus.message }}</span>
              <a-space>
                <a-button
                  type="primary"
                  size="small"
                  @click="emit('open-erp')"
                >
                  打开店小秘ERP
                </a-button>
                <a-button
                  size="small"
                  :loading="dianxiaomiLoginStatus.loading"
                  @click="emit('check-login')"
                >
                  {{ dianxiaomiLoginStatus.loading ? "检测中..." : "再次重试" }}
                </a-button>
              </a-space>
            </div>
          </template>
        </a-alert>
      </a-form-item>

      <!-- 店铺账号 -->
      <a-form-item
        v-if="tokenStatus.isValid || dianxiaomiLoginStatus.isLoggedIn"
        name="shopAccount"
        :rules="[{ required: true, message: '请选择店铺账号' }]"
      >
        <template #label>
          <div class="flex items-center space-x-2">
            <span>店铺账号</span>
            <a-tag
              v-if="shopAccounts.length > 0"
              color="blue"
              size="small"
            >
              共 {{ shopAccounts.length }} 个
            </a-tag>
          </div>
        </template>

        <a-input-group compact>
          <a-select
            :value="basicForm.shopAccount"
            @change="handleShopAccountChange"
            placeholder="请选择店铺账号"
            :loading="loadingStates.shopAccounts"
            style="width: calc(100% - 80px)"
          >
            <a-select-option
              v-for="shop in shopAccounts"
              :key="shop.shopId"
              :value="shop.shopId"
            >
              {{ shop.shopName }} (ID: {{ shop.shopId }}) - {{ shop.currency }}
            </a-select-option>
          </a-select>
          <a-button
            @click="emit('load-shop-accounts')"
            :loading="loadingStates.shopAccounts"
            style="width: 80px"
          >
            {{ loadingStates.shopAccounts ? "同步中" : "同步" }}
          </a-button>
        </a-input-group>

        <!-- 显示选中店铺的详细信息 -->
        <div
          v-if="basicForm.shopAccount"
          class="mt-3"
        >
          <div
            v-for="shop in shopAccounts"
            :key="shop.shopId"
          >
            <a-card
              v-if="shop.shopId === basicForm.shopAccount"
              size="small"
              class="bg-blue-50 border-blue-200"
            >
              <template #title>
                <div class="flex items-center justify-between">
                  <span class="text-blue-900 text-sm">{{ shop.shopName }}</span>
                  <a-tag
                    color="success"
                    size="small"
                  >
                    已绑定
                  </a-tag>
                </div>
              </template>

              <a-descriptions
                :column="3"
                size="small"
              >
                <a-descriptions-item label="店铺ID">
                  <a-typography-text code>{{ shop.shopId }}</a-typography-text>
                </a-descriptions-item>
                <a-descriptions-item label="币种">
                  <a-tag color="blue">{{ shop.currency }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="shop.authTime"
                  label="授权时间"
                >
                  <span class="text-xs">{{ shop.authTime }}</span>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="shop.expireTime"
                  label="过期时间"
                  :span="3"
                >
                  <span
                    :class="
                      isExpiringSoon(shop.expireTime)
                        ? 'text-red-600 font-medium text-xs'
                        : 'text-xs'
                    "
                  >
                    {{ shop.expireTime }}
                  </span>
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </div>
        </div>
      </a-form-item>

      <!-- 发布状态 -->
      <a-form-item
        label="发布状态"
        name="publishStatus"
      >
        <a-radio-group
          :value="basicForm.publishStatus"
          @change="(e) => updateForm('publishStatus', e.target.value)"
        >
          <a-radio value="2">直接发布</a-radio>
          <a-radio value="3">移入DXM待发布</a-radio>
          <a-radio value="1">放置DXM草稿箱</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 经营站点 -->
      <a-form-item
        label="经营站点"
        name="businessSite"
        :rules="[{ required: true, message: '请选择经营站点' }]"
      >
        <a-select
          :value="basicForm.businessSite"
          @change="(value) => updateForm('businessSite', value)"
          placeholder="请选择经营站点"
        >
          <a-select-option
            v-for="site in siteOptions"
            :key="site.value"
            :value="site.value"
          >
            {{ site.label }} ({{ site.code }})
          </a-select-option>
        </a-select>

        <!-- 显示选中站点的详细信息 -->
        <div
          v-if="basicForm.businessSite"
          class="mt-2"
        >
          <div
            v-if="getSiteById(Number(basicForm.businessSite))"
            class="text-sm"
          >
            <a-space>
              <a-tag
                color="blue"
                size="small"
              >
                {{ getSiteById(Number(basicForm.businessSite))?.region }}
              </a-tag>
              <span class="text-gray-500 text-xs">
                货币：{{
                  getSiteById(Number(basicForm.businessSite))?.currency
                }}
              </span>
              <span class="text-gray-500 text-xs">
                语言：{{
                  getSiteById(Number(basicForm.businessSite))?.language
                }}
              </span>
            </a-space>
          </div>
        </div>
      </a-form-item>

      <!-- 发货仓库 -->
      <a-form-item
        v-if="tokenStatus.isValid || dianxiaomiLoginStatus.isLoggedIn"
        label="发货仓库"
        name="warehouse"
        :rules="[{ required: true, message: '请选择发货仓库' }]"
      >
        <div class="flex items-center space-x-2">
          <div class="flex-1">
            <a-select
              :value="Array.isArray(basicForm.warehouse) ? basicForm.warehouse : [basicForm.warehouse].filter(Boolean)"
              @change="(value) => updateForm('warehouse', value)"
              placeholder="请选择发货仓库"
              :loading="loadingStates.warehouses"
              mode="multiple"
              style="width: 100%"
            >
              <a-select-option
                v-for="warehouse in getWarehouseOptions"
                :key="warehouse.value"
                :value="warehouse.value"
              >
                {{ warehouse.label }}
              </a-select-option>
            </a-select>
          </div>
          <a-button
            @click="emit('load-warehouses')"
            :loading="loadingStates.warehouses"
            size="small"
          >
            {{ loadingStates.warehouses ? "同步中" : "同步" }}
          </a-button>
        </div>
      </a-form-item>

      <!-- 运费模板 -->
      <a-form-item
        v-if="tokenStatus.isValid || dianxiaomiLoginStatus.isLoggedIn"
        label="运费模板"
        name="freightTemplate"
        :rules="[{ required: true, message: '请选择运费模板' }]"
      >
        <a-input-group compact>
          <a-select
            :value="basicForm.freightTemplate"
            @change="(value) => updateForm('freightTemplate', value)"
            placeholder="请选择运费模板"
            :loading="loadingStates.freightTemplates"
            style="width: calc(100% - 80px)"
          >
            <a-select-option
              v-for="template in freightTemplates"
              :key="template.id"
              :value="template.freightTemplateId"
            >
              {{ template.templateName }} (店铺: {{ template.shopId }}, 站点:
              {{ template.site }})
            </a-select-option>
          </a-select>
          <a-button
            @click="emit('load-freight-templates')"
            :loading="loadingStates.freightTemplates"
            style="width: 80px"
            size="small"
          >
            {{ loadingStates.freightTemplates ? "同步中" : "同步" }}
          </a-button>
        </a-input-group>
      </a-form-item>

      <!-- 发货时效 -->
      <a-form-item
        label="发货时效"
        name="shippingTime"
        :rules="[{ required: true, message: '请选择发货时效' }]"
      >
        <a-radio-group
          :value="basicForm.shippingTime"
          @change="(e) => updateForm('shippingTime', e.target.value)"
        >
          <a-radio value="86400">1个工作日内发货</a-radio>
          <a-radio value="172800">2个工作日内发货</a-radio>
          <a-radio value="777600">9个工作日内发货(Y2)</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 场地 -->
      <a-form-item
        label="场地"
        name="venue"
        :rules="[{ required: true, message: '请选择场地' }]"
      >
        <a-cascader
          :value="basicForm.venue ? basicForm.venue.split('/') : []"
          @change="(value) => updateForm('venue', value ? value.join('/') : '')"
          :options="[
            {
              value: '中国',
              label: '中国',
              children: [
                { value: '广东省', label: '广东省' },
                { value: '浙江省', label: '浙江省' },
                { value: '江苏省', label: '江苏省' },
                { value: '上海市', label: '上海市' }
              ]
            },
            {
              value: '美国',
              label: '美国',
              children: [
                { value: '加利福尼亚州', label: '加利福尼亚州' },
                { value: '纽约州', label: '纽约州' },
                { value: '德克萨斯州', label: '德克萨斯州' }
              ]
            }
          ]"
          placeholder="请选择场地"
          style="width: 50vw"
          :show-search="false"
        />
      </a-form-item>

      <!-- 商品分类 -->
      <a-form-item
        v-if="tokenStatus.isValid || dianxiaomiLoginStatus.isLoggedIn"
        label="商品分类"
        name="productCategory"
        :rules="[{ required: true, message: '请选择商品分类' }]"
      >
        <div class="space-y-3">
          <!-- 显示已保存的分类信息 -->
          <div v-if="selectedCategoryPath.length > 0" class="bg-green-50 border border-green-200 rounded-lg p-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-green-700 font-medium text-sm">✅ 已配置分类</span>
              <a-tag color="green" size="small">已选择</a-tag>
            </div>

            <div class="space-y-2">
              <div class="text-sm">
                <span class="text-gray-600">分类路径:</span>
                <div class="mt-1">
                  <a-breadcrumb separator=">">
                    <a-breadcrumb-item
                      v-for="(label, index) in selectedCategoryLabels"
                      :key="index"
                      class="text-blue-600"
                    >
                      {{ label }}
                    </a-breadcrumb-item>
                  </a-breadcrumb>
                </div>
              </div>

              <div class="text-sm">
                <span class="text-gray-600">分类ID:</span>
                <a-tag color="blue" size="small" class="ml-1">
                  {{ selectedCategoryPath[selectedCategoryPath.length - 1] }}
                </a-tag>
              </div>
            </div>
          </div>

          <CategoryCascader
            :value="selectedCategoryPath"
            :shop-id="basicForm.shopAccount"
            @change="handleCascaderChange"
            placeholder="请选择商品分类"
          />

          <!-- 调试信息 -->
          <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            <div><strong>调试信息:</strong></div>
            <div>
              Token状态: {{ tokenStatus.isValid ? "✅ 有效" : "❌ 无效" }}
            </div>
            <div>
              登录状态: {{ tokenStatus.isLoggedIn ? "✅ 已登录" : "❌ 未登录" }}
            </div>
            <div>店铺ID: {{ basicForm.shopAccount || "未选择" }}</div>
            <div>
              选中路径:
              {{
                selectedCategoryPath.length > 0
                  ? selectedCategoryPath.join(" → ")
                  : "未选择"
              }}
            </div>
            <div>
              分类标签:
              {{
                selectedCategoryLabels.length > 0
                  ? selectedCategoryLabels.join(" / ")
                  : "未选择"
              }}
            </div>
            <div>表单字段: {{ basicForm.productCategory || "未设置" }}</div>
            <div>可配置商品: {{ canConfigProduct ? "✅ 是" : "❌ 否" }}</div>
          </div>

          <!-- 测试按钮 -->
          <div class="flex flex-wrap gap-2">
            <a-button
              size="small"
              @click="testCategoryAPI"
            >
              🧪 测试分类API
            </a-button>
            <a-button
              size="small"
              :loading="tokenCheckLoading"
              @click="checkTokenStatus(true)"
            >
              🔐 检查Token状态
            </a-button>
            <a-button
              size="small"
              @click="checkLoginStatus"
            >
              👤 检查登录状态
            </a-button>
            <a-button
              size="small"
              @click="showCookieDetails"
            >
              🍪 查看Cookie
            </a-button>
          </div>

          <!-- 显示选中的分类路径 -->
          <div v-if="selectedCategoryPath.length > 0">
            <a-tag
              color="blue"
              size="small"
            >
              {{ selectedCategoryLabels.join(" / ") }}
            </a-tag>
          </div>
        </div>
      </a-form-item>

      <!-- 商品配置 -->
      <a-form-item label="商品配置">
        <div class="space-y-3">
          <!-- 配置状态显示 -->
          <div
            v-if="productConfigStatus.loading"
            class="flex items-center space-x-2"
          >
            <a-spin size="small" />
            <span class="text-sm text-gray-500">检查配置状态中...</span>
          </div>

          <div
            v-else-if="productConfigStatus.hasConfig"
            class="bg-green-50 p-3 rounded border-l-4 border-green-400"
          >
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-green-800 mb-1">
                  ✅ 已有商品配置
                </div>
                <div class="text-xs text-green-600">
                  配置时间:
                  {{
                    productConfigStatus.config?.timestamp
                      ? new Date(
                          productConfigStatus.config.timestamp,
                        ).toLocaleString()
                      : "未知"
                  }}
                </div>
                <div class="text-xs text-green-600">
                  分类路径:
                  {{ productConfigStatus.config?.categoryPath || "未知" }}
                </div>
              </div>
              <a-button
                type="primary"
                size="small"
                @click="openProductConfig"
                :disabled="!canConfigProduct"
              >
                🔧 重新配置
              </a-button>
            </div>
          </div>

          <!-- 配置按钮 -->
          <div v-else>
            <a-button
              type="primary"
              @click="openProductConfig"
              :disabled="!canConfigProduct"
              size="default"
              class="mr-2"
            >
              🔧 配置商品属性
            </a-button>
            <a-tooltip
              v-if="!canConfigProduct"
              title="请先选择店铺账号和商品分类"
            >
              <a-button
                disabled
                size="small"
              >
                ❓ 需要完成前置配置
              </a-button>
            </a-tooltip>
          </div>

          <!-- 配置说明 -->
          <div
            class="text-sm text-gray-600 bg-blue-50 p-3 rounded border-l-4 border-blue-400"
          >
            <div class="font-medium text-blue-800 mb-1">📋 配置说明：</div>
            <ul class="list-disc list-inside space-y-1 text-xs">
              <li>选择商品分类后，点击"配置商品属性"按钮</li>
              <li>系统将打开店小秘商品配置页面</li>
              <li>在配置页面中设置商品属性、规格等信息</li>
              <li>点击"保存设置"后，配置将自动保存到插件本地缓存</li>
            </ul>
          </div>
        </div>
      </a-form-item>

      <!-- 产品属性 -->
      <a-form-item
        label="产品属性"
        name="productAttributes"
      >
        <div class="space-y-2">
          <div v-if="basicForm.productAttributes && basicForm.productAttributes.length > 0">
            <div
              v-for="(attr, index) in basicForm.productAttributes"
              :key="index"
              class="flex items-center space-x-2 text-sm"
            >
              <span class="text-gray-600 font-medium">{{ attr.name }}:</span>
              <div class="text-gray-800">{{ attr.value || '基础清洁' }}</div>
            </div>
          </div>
          <div v-else class="text-gray-500 text-sm">
            暂无产品属性配置
          </div>
        </div>
      </a-form-item>

      <!-- 主图视频 -->
      <a-form-item
        label="主图视频(选填)"
        name="mainVideo"
      >
        <a-input
          :value="basicForm.mainVideo || ''"
          @change="(e) => updateForm('mainVideo', e.target.value)"
          placeholder="请填入TEMU认证过后的视频链接"
        />
      </a-form-item>

      <!-- 店小秘配置同步 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card size="small" title="🔄 店小秘配置同步" class="mb-4">
          <div v-if="dxmConfigStatus.hasConfig" class="space-y-3">
            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
              <div class="flex items-center justify-between mb-2">
                <span class="text-green-700 font-medium text-sm">✅ 已检测到店小秘配置</span>
                <a-tag color="green" size="small">已配置</a-tag>
              </div>

              <div class="space-y-2 text-sm">
                <div v-if="dxmConfigStatus.categoryName" class="flex justify-between">
                  <span class="text-gray-600">商品分类:</span>
                  <span class="text-gray-800">{{ dxmConfigStatus.categoryName }}</span>
                </div>
                <div v-if="dxmConfigStatus.shopId" class="flex justify-between">
                  <span class="text-gray-600">店铺ID:</span>
                  <span class="text-gray-800">{{ dxmConfigStatus.shopId }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">产品属性:</span>
                  <span class="text-gray-800">{{ dxmConfigStatus.attributesCount }} 个</span>
                </div>
                <div v-if="dxmConfigStatus.lastUpdated" class="flex justify-between">
                  <span class="text-gray-600">更新时间:</span>
                  <span class="text-gray-800 text-xs">{{ new Date(dxmConfigStatus.lastUpdated).toLocaleString() }}</span>
                </div>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <div class="space-x-2">
                <a-button type="primary" size="small" @click="syncDxmConfig">
                  🔄 同步到基础配置
                </a-button>
                <a-button size="small" @click="getDxmConfigStatus">
                  🔍 刷新状态
                </a-button>
              </div>
              <a-button danger size="small" @click="clearDxmConfig">
                🗑️ 清除配置
              </a-button>
            </div>
          </div>

          <div v-else class="text-center py-6">
            <div class="text-gray-400 mb-3">
              <div class="text-2xl mb-2">📋</div>
              <div class="text-sm">暂未检测到店小秘配置</div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div class="text-blue-700 text-sm space-y-1">
                <div class="font-medium">📝 如何获取配置：</div>
                <div>1. 在店小秘中打开商品配置页面</div>
                <div>2. 填写商品分类和属性信息</div>
                <div>3. 点击保存，配置将自动同步到此处</div>
              </div>
            </div>

            <div class="space-x-2">
              <a-button type="primary" size="small" @click="openProductConfig" :disabled="!canConfigProduct">
                🔗 打开商品配置页面
              </a-button>
              <a-button size="small" @click="getDxmConfigStatus">
                🔍 检查配置状态
              </a-button>
            </div>
          </div>
        </a-card>
      </a-form-item>

      <!-- 保存按钮 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <div style="padding-left: 120px;">
          <a-button
            type="primary"
            html-type="submit"
            size="default"
          >
            保存信息
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped>
/* ========================================
   基础设置组件样式 - 基于新设计系统
======================================== */

.basic-settings-container {
  width: 100%;
  min-height: auto; /* 自适应内容高度 */
  padding: 0; /* 父容器已设置padding */
  margin: 0 auto;
  background: transparent; /* 使用父容器背景 */
  border-radius: 0; /* 避免重复圆角 */
  overflow: visible; /* 让最外层容器处理滚动 */
}

/* 滚动条样式由父容器 step-panel 处理 */

.basic-settings-container :deep(.ant-form-item-label > label) {
  font-weight: 600;
  font-size: 14px;
}

.basic-settings-container :deep(.ant-form-item) {
  margin-bottom: var(--space-lg);
}

/* 表单布局优化 - 居中对齐 */
.basic-settings-container :deep(.ant-form) {
  max-width: 100%;
}

/* 卡片内容居中 */
.basic-settings-container :deep(.ant-card) {
  margin-bottom: var(--space-lg);
}

/* 表单项标签对齐 */
.basic-settings-container :deep(.ant-form-item-label) {
  text-align: left;
  padding-right: var(--space-md);
  width: 120px !important;
}

/* 自定义按钮圆角 */
.basic-settings-container :deep(.custom-btn-radius) {
  border-radius: 6px;
}

.basic-settings-container :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #6b7280;
  font-size: 12px;
}

.basic-settings-container :deep(.ant-descriptions-item-content) {
  font-size: 12px;
}

.basic-settings-container :deep(.ant-select) {
  font-size: 14px;
}

.basic-settings-container :deep(.ant-input) {
  font-size: 14px;
}

.basic-settings-container :deep(.ant-radio-wrapper) {
  font-size: 14px;
}

/* 水平布局优化 */
.basic-settings-container :deep(.ant-form-horizontal .ant-form-item-label) {
  padding-right: 12px;
}

.basic-settings-container :deep(.ant-form-horizontal .ant-form-item-control) {
  flex: 1;
}

/* 保存按钮区域样式 */
.basic-settings-container :deep(.flex.justify-center) {
  margin-top: var(--space-xl);
  margin-bottom: var(--space-xl);
  padding: var(--space-lg) 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-container);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 按钮样式优化 */
.basic-settings-container :deep(.ant-btn-primary) {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.basic-settings-container :deep(.ant-btn-primary:hover) {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.basic-settings-container :deep(.ant-btn-default) {
  border-color: var(--border-color);
  color: var(--text-secondary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.basic-settings-container :deep(.ant-btn-default:hover) {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 按钮组优化 */
.basic-settings-container :deep(.ant-input-group-addon) {
  padding: 0;
}
</style>
