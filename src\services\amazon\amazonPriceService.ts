/**
 * Amazon价格服务 - 简化版
 * 只处理Amazon价格数据，使用统一的简化格式
 */

import amazonDataService from './amazonDataService'
import type { AmazonPriceData } from './amazonDataService'

// Amazon价格信息接口
export interface AmazonPriceInfo {
  extCode: string
  amazonPrice?: AmazonPriceData
  dataSource: 'cache' | 'api'
  lastUpdated: number
  // 业务计算字段
  officialDeclaredPrice?: number  // 官方申报价
  profit?: number                 // 利润金额
  profitRate?: number            // 利润率（百分比）
}

// IndexedDB存储格式 - 统一简化格式
export interface AmazonCacheData {
  p: string      // 价格 "10.99"
  s: string      // 库存 "10"
  imageUrl: string // 图片URL
  t: number      // 时间戳
}

export class AmazonPriceService {
  private static instance: AmazonPriceService

  static getInstance(): AmazonPriceService {
    if (!AmazonPriceService.instance) {
      AmazonPriceService.instance = new AmazonPriceService()
    }
    return AmazonPriceService.instance
  }

  /**
   * 核心方法：获取Amazon价格信息（支持缓存）
   */
  async getAmazonPriceInfo(
    extCode: string,
    forceRefresh: boolean = false
  ): Promise<AmazonPriceInfo> {
    try {
      console.info('[AmazonPriceService] 获取Amazon价格信息:', extCode, { forceRefresh })

      const result: AmazonPriceInfo = {
        extCode,
        dataSource: 'api',
        lastUpdated: Date.now()
      }

      // 1. 获取Amazon价格（优先缓存）
      let amazonPriceData = null
      if (!forceRefresh) {
        // 先尝试从缓存获取
        amazonPriceData = await this.getCachedAmazonPrice(extCode)
        if (amazonPriceData) {
          console.info('[AmazonPriceService] 使用Amazon价格缓存:', extCode)
          result.dataSource = 'cache'
        }
      }

      // 如果缓存中没有或强制刷新，从amazonDataService获取
      if (!amazonPriceData) {
        console.info('[AmazonPriceService] 从amazonDataService获取Amazon价格:', extCode)
        amazonPriceData = await amazonDataService.getAmazonPrice(extCode)
        if (amazonPriceData) {
          // 缓存新获取的价格
          await this.cacheAmazonPrice(extCode, amazonPriceData)
          result.dataSource = 'api'
          console.info('[AmazonPriceService] Amazon价格获取并缓存成功:', extCode)
        }
      }

      if (amazonPriceData) {
        result.amazonPrice = amazonPriceData
      } else {
        console.warn('[AmazonPriceService] 未获取到Amazon价格:', extCode)
      }

      console.info('[AmazonPriceService] Amazon价格信息获取完成:', extCode, {
        hasAmazonPrice: !!result.amazonPrice,
        dataSource: result.dataSource
      })

      return result
    } catch (error) {
      console.error('[AmazonPriceService] 获取Amazon价格信息失败:', error)
      return {
        extCode,
        dataSource: 'api',
        lastUpdated: Date.now()
      }
    }
  }

  /**
   * 获取增强的Amazon价格信息（包含业务计算字段）
   */
  async getEnhancedAmazonPriceInfo(
    extCode: string,
    exchangeRate: number = 7.2,
    officialDeclaredPrice?: number,
    forceRefresh: boolean = false
  ): Promise<AmazonPriceInfo> {
    // 先获取基础的Amazon价格信息
    const basicInfo = await this.getAmazonPriceInfo(extCode, forceRefresh)

    // 计算业务字段
    let profit: number | undefined
    let profitRate: number | undefined

    if (basicInfo.amazonPrice && officialDeclaredPrice) {
      const usdPrice = parseFloat(basicInfo.amazonPrice.usdPrice)
      const costInCNY = usdPrice * exchangeRate

      // 计算利润和利润率
      profit = officialDeclaredPrice - costInCNY
      profitRate = officialDeclaredPrice > 0 ? (profit / officialDeclaredPrice) * 100 : 0
    }

    return {
      ...basicInfo,
      officialDeclaredPrice,
      profit,
      profitRate
    }
  }

  /**
   * 批量获取Amazon价格信息
   */
  async batchGetAmazonPriceInfo(
    extCodes: string[],
    forceRefresh: boolean = false,
    onProgress?: (completed: number, total: number) => void
  ): Promise<Map<string, AmazonPriceInfo>> {
    const result = new Map<string, AmazonPriceInfo>()
    const total = extCodes.length
    let completed = 0

    console.info('[AmazonPriceService] 批量获取Amazon价格信息:', total, '个商品')

    // 并发控制，最多同时处理3个请求
    const concurrency = 3
    const chunks = this.chunkArray(extCodes, concurrency)

    for (const chunk of chunks) {
      const promises = chunk.map(async (extCode) => {
        const priceInfo = await this.getAmazonPriceInfo(extCode, forceRefresh)
        result.set(extCode, priceInfo)
        completed++
        onProgress?.(completed, total)
      })

      await Promise.allSettled(promises)
    }

    console.info('[AmazonPriceService] 批量获取完成，成功获取', result.size, '个商品的Amazon价格信息')
    return result
  }

  /**
   * 从缓存获取Amazon价格（直接读取简化格式）
   */
  private async getCachedAmazonPrice(extCode: string): Promise<AmazonPriceData | null> {
    try {
      console.info('[AmazonPriceService] 尝试从缓存获取价格:', extCode)

      const response = await chrome.runtime.sendMessage({
        type: 'GET_PRICE_FROM_INDEXEDDB',
        data: { extCode }
      })

      if (response && response.success && response.data) {
        const cachedData: AmazonCacheData = response.data
        console.info('[AmazonPriceService] 缓存数据:', cachedData)

        // 检查缓存是否有效（24小时）
        const cacheAge = Date.now() - cachedData.t
        const CACHE_DURATION = 24 * 60 * 60 * 1000

        if (cacheAge < CACHE_DURATION) {
          console.info('[AmazonPriceService] 缓存有效，返回价格数据')

          // 直接转换为AmazonPriceData格式
          return {
            usdPrice: cachedData.p,
            stock: cachedData.s,
            imageUrl: cachedData.imageUrl || '',
            timestamp: cachedData.t
          }
        } else {
          console.info('[AmazonPriceService] 缓存已过期')
          // 清除过期缓存
          await this.clearExpiredCache(extCode)
        }
      } else {
        console.info('[AmazonPriceService] 缓存中无数据')
      }

      return null
    } catch (error) {
      console.warn('[AmazonPriceService] 获取Amazon价格缓存失败:', error)
      return null
    }
  }

  /**
   * 缓存Amazon价格（直接保存简化格式）
   */
  private async cacheAmazonPrice(extCode: string, priceData: AmazonPriceData): Promise<void> {
    try {
      console.info('[AmazonPriceService] 开始缓存Amazon价格:', { extCode, priceData })

      // 转换为简化格式
      const cacheData: AmazonCacheData = {
        p: priceData.usdPrice,
        s: priceData.stock,
        imageUrl: priceData.imageUrl || '',
        t: Date.now()
      }

      const response = await chrome.runtime.sendMessage({
        type: 'SAVE_PRICE_TO_INDEXEDDB',
        data: {
          id: extCode,
          t: Date.now(),
          d: JSON.stringify(cacheData)
        }
      })

      if (response && response.success) {
        console.info('[AmazonPriceService] Amazon价格缓存成功:', extCode)
      } else {
        console.warn('[AmazonPriceService] Amazon价格缓存失败:', response?.error)
      }
    } catch (error) {
      console.warn('[AmazonPriceService] Amazon价格缓存异常:', error)
    }
  }

  /**
   * 清除过期缓存
   */
  private async clearExpiredCache(extCode: string): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'DELETE_FROM_INDEXEDDB',
        data: { extCode }
      })

      if (response && response.success) {
        console.info('[AmazonPriceService] 过期缓存清除成功:', extCode)
      }
    } catch (error) {
      console.error('[AmazonPriceService] 清除过期缓存失败:', extCode, error)
    }
  }

  /**
   * 数组分块工具函数
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }
}

// 导出单例实例
export const amazonPriceService = AmazonPriceService.getInstance()
