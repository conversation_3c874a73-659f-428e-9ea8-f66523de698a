/**
 * Amazon数据组装服务
 * 处理Amazon商品数据的组装和格式化
 */

import AmazonDataService from './amazonDataService'
import { dianxiaomiImageUploadService } from '../dxm/imageUploadService'

/**
 * Amazon商品数据接口
 */
interface AmazonProductData {
  asin: string
  title: string
  brand?: string
  price?: number
  currency?: string
  rating?: number
  reviewCount?: number
  mainImageUrl?: string
  imageUrls: string[]
  bulletPoints?: string[]
  description?: string
  categoryPath?: string
  stockStatus?: string
  specifications?: Record<string, string>
  variations?: any[]
  sourceUrl: string
}

/**
 * 店小秘商品数据接口
 */
interface DianxiaomiProductData {
  title: string
  description: string
  price: number
  images: string[]
  category: string
  brand: string
  specifications: Record<string, string>
  sourceUrl: string
  [key: string]: any
}

export class AmazonDataAssemblyService {
  /**
   * 组装Amazon数据（带图片处理）
   */
  async assembleAmazonDataWithImageProcessing(
    html: string,
    options: {
      processImages?: boolean
      maxImages?: number
      compressImages?: boolean
    } = {}
  ): Promise<{ success: boolean; data?: AmazonProductData; error?: string }> {
    try {
      console.info('[AmazonDataAssemblyService] 开始组装Amazon数据（带图片处理）')

      const {
        processImages = true,
        maxImages = 10,
        compressImages = true
      } = options

      // 1. 提取基础数据
      const basicData = await this.extractBasicAmazonData(html)
      if (!basicData.success || !basicData.data) {
        return {
          success: false,
          error: basicData.error || '提取基础数据失败'
        }
      }

      const amazonData = basicData.data

      // 2. 处理图片（如果需要）
      if (processImages && amazonData.imageUrls.length > 0) {
        try {
          console.info('[AmazonDataAssemblyService] 开始处理图片，数量:', amazonData.imageUrls.length)

          // 限制图片数量
          const imagesToProcess = amazonData.imageUrls.slice(0, maxImages)

          // 处理图片
          const processedImages = await dianxiaomiImageUploadService.processImageUrls(
            imagesToProcess,
            3 // 最大并发数
          )

          if (processedImages.length > 0) {
            amazonData.imageUrls = processedImages
            amazonData.mainImageUrl = processedImages[0]
            console.info('[AmazonDataAssemblyService] 图片处理完成，成功处理:', processedImages.length, '张')
          } else {
            console.warn('[AmazonDataAssemblyService] 图片处理失败，使用原始图片URL')
          }
        } catch (imageError) {
          console.warn('[AmazonDataAssemblyService] 图片处理失败，使用原始图片URL:', imageError)
        }
      }

      console.info('[AmazonDataAssemblyService] Amazon数据组装完成:', amazonData)

      return {
        success: true,
        data: amazonData
      }

    } catch (error) {
      console.error('[AmazonDataAssemblyService] 组装Amazon数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '组装数据失败'
      }
    }
  }

  /**
   * 组装Amazon数据（新格式）
   */
  async assembleAmazonDataNewFormat(html: string): Promise<{ success: boolean; data?: DianxiaomiProductData; error?: string }> {
    try {
      console.info('[AmazonDataAssemblyService] 开始组装Amazon数据（新格式）')

      // 1. 提取Amazon数据
      const amazonResult = await this.extractBasicAmazonData(html)
      if (!amazonResult.success || !amazonResult.data) {
        return {
          success: false,
          error: amazonResult.error || '提取Amazon数据失败'
        }
      }

      const amazonData = amazonResult.data

      // 2. 转换为店小秘格式
      const dianxiaomiData: DianxiaomiProductData = {
        title: amazonData.title || '',
        description: this.formatDescription(amazonData),
        price: amazonData.price || 0,
        images: amazonData.imageUrls || [],
        category: amazonData.categoryPath || '',
        brand: amazonData.brand || '',
        specifications: amazonData.specifications || {},
        sourceUrl: amazonData.sourceUrl,
        
        // 额外字段
        asin: amazonData.asin,
        rating: amazonData.rating,
        reviewCount: amazonData.reviewCount,
        stockStatus: amazonData.stockStatus,
        currency: amazonData.currency || 'USD'
      }

      console.info('[AmazonDataAssemblyService] 新格式数据组装完成:', dianxiaomiData)

      return {
        success: true,
        data: dianxiaomiData
      }

    } catch (error) {
      console.error('[AmazonDataAssemblyService] 组装新格式数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '组装数据失败'
      }
    }
  }

  /**
   * 提取基础Amazon数据
   */
  private async extractBasicAmazonData(html: string): Promise<{ success: boolean; data?: AmazonProductData; error?: string }> {
    try {
      // 提取ASIN
      const asin = this.extractASIN(html)
      
      // 提取标题
      const title = this.extractTitle(html)
      
      // 提取品牌
      const brand = this.extractBrand(html)
      
      // 提取价格
      const price = this.extractPrice(html)
      
      // 提取评分
      const rating = this.extractRating(html)
      
      // 提取评论数
      const reviewCount = this.extractReviewCount(html)
      
      // 提取主图
      const mainImageUrl = AmazonDataService.extractMainImageFromHtml(html)
      
      // 提取图片列表
      const imageUrlsJson = AmazonDataService.extractImageUrlsFromHtml(html)
      const imageUrls = JSON.parse(imageUrlsJson)
      
      // 提取要点
      const bulletPoints = this.extractBulletPoints(html)
      
      // 提取规格
      const specifications = this.extractSpecifications(html)

      const amazonData: AmazonProductData = {
        asin,
        title,
        brand,
        price,
        currency: 'USD',
        rating,
        reviewCount,
        mainImageUrl: mainImageUrl || undefined,
        imageUrls,
        bulletPoints,
        description: bulletPoints.join(' '),
        categoryPath: '',
        stockStatus: 'In Stock',
        specifications,
        variations: [],
        sourceUrl: this.extractSourceUrl(html)
      }

      return {
        success: true,
        data: amazonData
      }

    } catch (error) {
      console.error('[AmazonDataAssemblyService] 提取基础数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '提取数据失败'
      }
    }
  }

  /**
   * 提取ASIN
   */
  private extractASIN(html: string): string {
    const patterns = [
      /\/dp\/([A-Z0-9]{10})/,
      /\/gp\/product\/([A-Z0-9]{10})/,
      /"asin":"([A-Z0-9]{10})"/,
      /data-asin="([A-Z0-9]{10})"/
    ]

    for (const pattern of patterns) {
      const match = html.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return 'UNKNOWN'
  }

  /**
   * 提取标题
   */
  private extractTitle(html: string): string {
    const patterns = [
      /<span[^>]*id="productTitle"[^>]*>([^<]+)<\/span>/,
      /<h1[^>]*id="title"[^>]*>([^<]+)<\/h1>/,
      /<title>([^<]+)<\/title>/
    ]

    for (const pattern of patterns) {
      const match = html.match(pattern)
      if (match) {
        return match[1].trim()
      }
    }

    return ''
  }

  /**
   * 提取品牌
   */
  private extractBrand(html: string): string | undefined {
    const patterns = [
      /<a[^>]*id="bylineInfo"[^>]*>([^<]+)<\/a>/,
      /<span[^>]*class="[^"]*brand[^"]*"[^>]*>([^<]+)<\/span>/,
      /Visit the ([^<]+) Store/
    ]

    for (const pattern of patterns) {
      const match = html.match(pattern)
      if (match) {
        return match[1].trim()
      }
    }

    return undefined
  }

  /**
   * 提取价格
   */
  private extractPrice(html: string): number | undefined {
    const patterns = [
      /<span[^>]*class="[^"]*a-price-current[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/,
      /<span[^>]*class="[^"]*a-price[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/
    ]

    for (const pattern of patterns) {
      const match = html.match(pattern)
      if (match) {
        const priceStr = match[1].replace(/,/g, '')
        const price = parseFloat(priceStr)
        if (!isNaN(price)) {
          return price
        }
      }
    }

    return undefined
  }

  /**
   * 提取评分
   */
  private extractRating(html: string): number | undefined {
    const patterns = [
      /<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([0-9.]+) out of 5 stars<\/span>/,
      /<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([0-9.]+)星<\/span>/
    ]

    for (const pattern of patterns) {
      const match = html.match(pattern)
      if (match) {
        const rating = parseFloat(match[1])
        if (!isNaN(rating)) {
          return rating
        }
      }
    }

    return undefined
  }

  /**
   * 提取评论数
   */
  private extractReviewCount(html: string): number | undefined {
    const patterns = [
      /<span[^>]*id="acrCustomerReviewText"[^>]*>([0-9,]+) ratings<\/span>/,
      /<span[^>]*id="acrCustomerReviewText"[^>]*>([0-9,]+)条评论<\/span>/
    ]

    for (const pattern of patterns) {
      const match = html.match(pattern)
      if (match) {
        const countStr = match[1].replace(/,/g, '')
        const count = parseInt(countStr)
        if (!isNaN(count)) {
          return count
        }
      }
    }

    return undefined
  }

  /**
   * 提取要点
   */
  private extractBulletPoints(html: string): string[] {
    const bulletPoints: string[] = []
    
    // 匹配要点列表
    const bulletPattern = /<div[^>]*id="feature-bullets"[^>]*>.*?<ul[^>]*>(.*?)<\/ul>/s
    const bulletMatch = html.match(bulletPattern)
    
    if (bulletMatch) {
      const listContent = bulletMatch[1]
      const itemPattern = /<li[^>]*>.*?<span[^>]*>([^<]+)<\/span>.*?<\/li>/g
      let itemMatch
      
      while ((itemMatch = itemPattern.exec(listContent)) !== null) {
        const text = itemMatch[1].trim()
        if (text && text.length > 10) {
          bulletPoints.push(text)
        }
      }
    }

    return bulletPoints.slice(0, 5) // 最多5个要点
  }

  /**
   * 提取规格参数
   */
  private extractSpecifications(html: string): Record<string, string> {
    const specs: Record<string, string> = {}
    
    // 匹配规格表格
    const specPattern = /<table[^>]*id="productDetails_techSpec_section_1"[^>]*>.*?<\/table>/s
    const specMatch = html.match(specPattern)
    
    if (specMatch) {
      const tableContent = specMatch[0]
      const rowPattern = /<tr[^>]*>.*?<td[^>]*>([^<]+)<\/td>.*?<td[^>]*>([^<]+)<\/td>.*?<\/tr>/g
      let rowMatch
      
      while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
        const key = rowMatch[1].trim()
        const value = rowMatch[2].trim()
        if (key && value) {
          specs[key] = value
        }
      }
    }

    return specs
  }

  /**
   * 提取源URL
   */
  private extractSourceUrl(html: string): string {
    // 从HTML中提取canonical URL或当前URL
    const canonicalMatch = html.match(/<link[^>]*rel="canonical"[^>]*href="([^"]+)"/)
    if (canonicalMatch) {
      return canonicalMatch[1]
    }

    // 如果没有找到canonical URL，返回默认值
    return 'https://www.amazon.com'
  }

  /**
   * 格式化描述
   */
  private formatDescription(amazonData: AmazonProductData): string {
    const parts: string[] = []

    if (amazonData.bulletPoints && amazonData.bulletPoints.length > 0) {
      parts.push('产品特点：')
      amazonData.bulletPoints.forEach((point, index) => {
        parts.push(`${index + 1}. ${point}`)
      })
    }

    if (amazonData.specifications && Object.keys(amazonData.specifications).length > 0) {
      parts.push('\n产品规格：')
      Object.entries(amazonData.specifications).forEach(([key, value]) => {
        parts.push(`${key}: ${value}`)
      })
    }

    return parts.join('\n')
  }
}

// 创建单例实例
export const amazonDataAssemblyService = new AmazonDataAssemblyService()
export default amazonDataAssemblyService
