/**
 * 商品列表业务服务
 * 
 * 职责：商品列表的完整业务逻辑，整合多个服务
 * - 整合 Temu API、Amazon 价格服务
 * - 处理商品数据格式化
 * - 提供完整的商品信息（包含价格、申报价等）
 * 
 * 依赖关系：
 * - temuApiService: Temu API 通信
 * - amazonPriceService: Amazon 价格获取
 * - indexedDBManager: 数据缓存
 * 
 * 被依赖：
 * - dashboard 组件
 * - useDashboard 组合式函数
 */

import { temuApiService } from './temu/temu-api'
import type { AmazonPriceService } from './amazon/amazonPriceService'

// 商品列表查询参数
interface ProductListParams {
  pageSize?: number
  pageNum?: number
  supplierTodoTypeList?: number[]
  secondarySelectStatusList?: number[]
  priceReviewStatusList?: number[]
  keyword?: string
  siteIds?: number[]
  productIds?: string[]
  skuCodes?: string[]
}

// 商品列表响应数据
interface ProductListResponse {
  success: boolean
  data: FormattedProduct[]
  total: number
  errorMsg?: string
}

// 格式化后的商品数据
interface FormattedProduct {
  id: number
  image: string
  title: string
  spu: string
  skc: string
  site: string
  currency: string
  category: string
  declaredPrice: string
  createTime: string
  
  // 价格信息
  priceInfo?: {
    supplierPriceValue: number
    supplierPriceText: string
    usdPrice: string
    exchangeRate: number
    reviewTimes: number
    priceReviewStatus: number
    officialDeclaredPrice?: number
    stock: number
    costInCNY?: number
    lastUpdated?: number
    dataSource?: 'cache' | 'api'
  }
  
  // 主要SKU信息
  sku: {
    image: string
    color: string
    itemNo: string
    status: string
    price: string
    selectStatus: number
    priceOrderId?: number
    productSkuId: number
    skuId: number
    officialDeclaredPrice?: number
    amazonPrice?: {
      usdPrice: string
      stock: string
      costInCNY: number
      lastUpdated: number
      dataSource: 'cache' | 'api'
    }
  }
  
  // 多SKU支持
  isMultiSku: boolean
  totalSkuCount: number
  skuList: Array<{
    skuId: number
    extCode: string
    properties: string
    price: string
    priceValue: number
    image: string
    priceReviewStatus: number
    selectStatus: number
    officialDeclaredPrice?: number
    amazonPrice?: {
      usdPrice: string
      stock: string
      costInCNY: number
      lastUpdated: number
      dataSource: 'cache' | 'api'
    }
  }>
  
  // 价格范围（多SKU）
  priceRange?: {
    min: number
    max: number
    minText: string
    maxText: string
    range: string
  }
  
  // 申报相关信息
  priceReview?: {
    priceOrderId: number | null
    times: number
    orderType: string | null
    hasReviewInfo: boolean
  }
}

export class ProductListService {
  private amazonPriceService: AmazonPriceService | null = null

  constructor() {
    console.info('[ProductListService] 初始化商品列表业务服务')
  }

  /**
   * 设置店铺ID（兼容性方法）
   */
  setMallId(mallId: string): void {
    console.info('[ProductListService] 设置店铺ID:', mallId)
    // 这里可以存储店铺ID，用于后续API调用
  }

  /**
   * 获取待办事项数量
   */
  async getTodoCount(): Promise<any> {
    console.info('[ProductListService] 开始获取待办事项数量...')

    try {
      const response = await temuApiService.getTodoCount()

      if (response.success && response.data) {
        return response.data
      } else {
        throw new Error(response.error || '获取待办事项失败')
      }
    } catch (error) {
      console.error('[ProductListService] 获取待办事项数量失败:', error)
      throw error
    }
  }

  /**
   * 格式化状态标签页（参考旧的temuDataService实现）
   */
  formatStatusTabs(productListData: any): any[] {
    console.info('[ProductListService] 开始格式化状态标签页...', productListData.result)

    if (!productListData.result) {
      console.warn('[ProductListService] 没有找到result数据')
      return [{ key: 'all', label: '全部', count: 0 }]
    }

    // 根据旧版本的状态映射关系
    const statusMap: Record<number, { key: string; label: string }> = {
      7: { key: 'price-confirming', label: '价格待确认' },
      10: { key: 'unpublished', label: '未发布到站点' },
      11: { key: 'unpublished', label: '未发布到站点' }, // 11也归类到未发布
      12: { key: 'published', label: '已发布站点' },
      13: { key: 'offline', label: '已下架' }
    }

    // 初始化标签页，保持界面显示的顺序
    const tabs = [
      { key: 'all', label: '全部', count: productListData.result.total || 0 },
      { key: 'price-confirming', label: '价格待确认', count: 0 },
      { key: 'unpublished', label: '未发布到站点', count: 0 },
      { key: 'published', label: '已发布站点', count: 0 },
      { key: 'offline', label: '已下架', count: 0 }
    ]

    const aggregation = productListData.result.productSkcStatusAggregation || []
    console.info('[ProductListService] 状态聚合数据:', aggregation)

    if (aggregation.length > 0) {
      // 创建一个映射来累计计数
      const countMap: Record<string, number> = {}

      aggregation.forEach((item: any) => {
        // 使用 selectStatus 字段（不是 secondarySelectStatus）
        const statusInfo = statusMap[item.selectStatus]
        if (statusInfo) {
          // 累计相同key的计数（比如状态10和11都归类到unpublished）
          if (!countMap[statusInfo.key]) {
            countMap[statusInfo.key] = 0
          }
          countMap[statusInfo.key] += item.count
          console.info(`[ProductListService] 状态${item.selectStatus}(${item.count}) -> ${statusInfo.key}`)
        } else {
          console.warn('[ProductListService] 忽略状态:', item.selectStatus, '计数:', item.count)
        }
      })

      // 更新标签页的计数
      tabs.forEach(tab => {
        if (countMap[tab.key]) {
          tab.count = countMap[tab.key]
        }
      })
    } else {
      console.warn('[ProductListService] 没有找到productSkcStatusAggregation数据')
    }

    console.info('[ProductListService] 格式化后的标签页:', tabs)
    return tabs
  }

  /**
   * 懒加载 Amazon 价格服务
   */
  private async getAmazonPriceService(): Promise<AmazonPriceService> {
    if (!this.amazonPriceService) {
      const { AmazonPriceService } = await import('./amazon/amazonPriceService')
      this.amazonPriceService = AmazonPriceService.getInstance()
    }
    return this.amazonPriceService
  }

  /**
   * 获取商品列表（基础版本，只包含 Temu 数据）
   */
  async getProductList(params: ProductListParams = {}): Promise<{
    success: boolean
    data: any[]
    total: number
    rawData?: any  // 添加原始数据，用于标签页格式化
    errorMsg?: string
  }> {
    try {
      console.info('[ProductListService] 获取商品列表:', params)

      // 调用 Temu API 获取原始数据
      const response = await temuApiService.getProductList({
        pageSize: params.pageSize || 50,
        pageNum: params.pageNum || 1,
        supplierTodoTypeList: params.supplierTodoTypeList || [],
        secondarySelectStatusList: params.secondarySelectStatusList || [],
        priceReviewStatusList: params.priceReviewStatusList || [],
        keyword: params.keyword || ''
      })

      if (!response.success || !response.data) {
        throw new Error(response.error || '获取商品列表失败')
      }

      const productListData = response.data

      if (!productListData.success || !productListData.result?.dataList) {
        return {
          success: false,
          data: [],
          total: 0,
          rawData: productListData,
          errorMsg: productListData.errorMsg || '获取商品列表失败'
        }
      }

      // 格式化商品数据
      const formattedData = this.formatProductData(productListData)

      return {
        success: true,
        data: formattedData,
        total: productListData.result.total,
        rawData: productListData  // 保留原始数据用于标签页格式化
      }
    } catch (error) {
      console.error('[ProductListService] 获取商品列表失败:', error)
      return {
        success: false,
        data: [],
        total: 0,
        errorMsg: error instanceof Error ? error.message : '获取失败'
      }
    }
  }

  /**
   * 获取价格确认商品列表（完整版本，包含所有价格信息）
   */
  async getPriceConfirmationProducts(params: ProductListParams = {}): Promise<ProductListResponse> {
    try {
      console.info('[ProductListService] 获取价格确认商品列表（完整业务逻辑）')

      // 1. 获取基础商品数据
      const productListParams = {
        ...params,
        secondarySelectStatusList: [7], // 价格待确认状态
        priceReviewStatusList: [0, 1, 2, 3]
      }

      const basicResult = await this.getProductList(productListParams)
      
      if (!basicResult.success) {
        return basicResult as ProductListResponse
      }

      const formattedData = basicResult.data

      // 2. 异步增强官方申报价（不阻塞界面）
      this.enhanceWithOfficialPrices(formattedData)

      // 3. 异步增强 Amazon 成本价（不阻塞界面）
      this.enhanceWithAmazonPrices(formattedData)

      return {
        success: true,
        data: formattedData,
        total: basicResult.total
      }
    } catch (error) {
      console.error('[ProductListService] 获取价格确认商品列表失败:', error)
      return {
        success: false,
        data: [],
        total: 0,
        errorMsg: error instanceof Error ? error.message : '获取失败'
      }
    }
  }

  /**
   * 格式化商品数据
   */
  formatProductData(productListData: any): FormattedProduct[] {
    console.info('[ProductListService] 开始格式化商品数据...')

    if (!productListData.result?.dataList) {
      console.warn('[ProductListService] 没有找到商品数据')
      return []
    }

    return productListData.result.dataList.map((product: any, index: number) => {
      console.info(`[ProductListService] 处理第${index + 1}个商品:`, product.productName)

      const firstSkc = product.skcList?.[0]
      if (!firstSkc) {
        console.warn(`[ProductListService] 商品${index + 1}没有SKC数据，跳过`)
        return null
      }

      // 提取申报信息
      const priceReviewInfo = firstSkc.supplierPriceReviewInfoList?.[0]
      const reviewTimes = priceReviewInfo?.times || 0
      const priceOrderId = priceReviewInfo?.priceOrderId || null

      // 处理SKU数据
      const skuList = firstSkc.skuList || []
      const firstSku = skuList[0]

      if (!firstSku) {
        console.warn(`[ProductListService] 商品${index + 1}没有SKU数据，跳过`)
        return null
      }

      // 价格信息
      const siteSupplierPrice = firstSku.siteSupplierPriceList?.[0]
      const supplierPriceValue = siteSupplierPrice?.supplierPriceValue || 0
      const supplierPriceText = siteSupplierPrice?.supplierPrice || firstSkc.supplierPrice || product.supplierPrice || ''

      // 构建多SKU信息
      const skuInfoList = skuList.map(sku => {
        const skuSitePrice = sku.siteSupplierPriceList?.[0]
        const skuProperties = sku.productPropertyList?.map(p => `${p.name}: ${p.value}`).join(', ') || ''

        return {
          skuId: sku.skuId,
          extCode: sku.extCode,
          properties: skuProperties,
          price: skuSitePrice?.supplierPrice || '',
          priceValue: skuSitePrice?.supplierPriceValue || 0,
          image: sku.skuPreviewImage,
          priceReviewStatus: sku.priceReviewStatus || 0,
          selectStatus: sku.selectStatus
        }
      })

      const formattedProduct: FormattedProduct = {
        id: product.productId,
        image: firstSku.skuPreviewImage || product.carouselImageUrlList?.[0] || '',
        title: product.productName || '',
        spu: product.productId?.toString() || '',
        skc: firstSkc.skcId?.toString() || '',
        site: product.siteInfoList?.[0]?.siteName || '',
        currency: product.supplierPriceCurrencyType || '',
        category: product.fullCategoryName?.join('>') || '',
        declaredPrice: supplierPriceText,
        createTime: product.productCreatedAt ? new Date(product.productCreatedAt).toLocaleString('zh-CN') : '',

        // 价格信息
        priceInfo: {
          supplierPriceValue: supplierPriceValue,
          supplierPriceText: supplierPriceText,
          usdPrice: (supplierPriceValue ? (supplierPriceValue / 100 / 7.2) : 0).toFixed(2),
          exchangeRate: 7.2,
          reviewTimes: reviewTimes,
          priceReviewStatus: firstSku.priceReviewStatus || 0,
          officialDeclaredPrice: supplierPriceValue, // 默认值，后续异步更新
          stock: 10
        },

        // 主要SKU信息
        sku: {
          image: firstSku.skuPreviewImage || firstSkc.previewImgUrlList?.[0] || '',
          color: firstSku.productPropertyList?.find(p => p.name === '颜色')?.value || '',
          itemNo: firstSku.extCode || firstSkc.extCode || '',
          status: this.getStatusText(firstSkc.selectStatus),
          price: supplierPriceText,
          selectStatus: firstSkc.selectStatus || 0,
          priceOrderId: priceOrderId,
          productSkuId: firstSku.skuId,
          skuId: firstSku.skuId,
          officialDeclaredPrice: supplierPriceValue
        },

        // 多SKU信息
        skuList: skuInfoList,
        isMultiSku: skuList.length > 1,
        totalSkuCount: skuList.length,
        priceRange: skuList.length > 1 ? this.calculatePriceRange(skuList) : undefined,

        // 申报相关信息
        priceReview: {
          priceOrderId: priceOrderId,
          times: reviewTimes,
          orderType: null,
          hasReviewInfo: !!priceReviewInfo
        }
      }

      return formattedProduct
    }).filter(Boolean) // 过滤掉null值
  }

  /**
   * 异步增强官方申报价数据
   */
  private async enhanceWithOfficialPrices(formattedData: FormattedProduct[]): Promise<void> {
    try {
      console.info('[ProductListService] 开始异步获取官方申报价...')

      // 收集所有 priceOrderId
      const priceOrderIds = formattedData
        .map(product => product.priceReview?.priceOrderId)
        .filter((id): id is number => id !== null && id !== undefined)

      if (priceOrderIds.length === 0) {
        console.info('[ProductListService] 没有需要获取官方申报价的商品')
        return
      }

      console.info('[ProductListService] 收集到priceOrderId:', priceOrderIds.length, '个')

      // 调用 Temu API 获取官方申报价
      const response = await temuApiService.getOfficialPrices(priceOrderIds)
      const officialPricesMap = this.parseOfficialPricesResponse(response)

      console.info('[ProductListService] 官方申报价获取完成，开始更新数据...')

      // 更新商品数据
      formattedData.forEach(product => {
        const priceOrderId = product.priceReview?.priceOrderId
        if (priceOrderId && officialPricesMap.has(priceOrderId)) {
          const officialPrice = officialPricesMap.get(priceOrderId)!

          // 更新各个位置的官方申报价
          if (product.priceInfo) {
            product.priceInfo.officialDeclaredPrice = officialPrice
          }
          if (product.sku) {
            product.sku.officialDeclaredPrice = officialPrice
          }
          // 更新所有SKU的官方申报价（一个SKC下的所有SKU共享同一个官方申报价）
          product.skuList.forEach(sku => {
            sku.officialDeclaredPrice = officialPrice
          })

          console.info('[ProductListService] 更新商品官方申报价:', {
            title: product.title,
            productId: product.id,
            priceOrderId,
            officialPrice
          })
        }
      })

      // 触发界面更新
      this.triggerDataUpdate('priceConfirmationDataUpdated', formattedData)

    } catch (error) {
      console.error('[ProductListService] 异步获取官方申报价失败:', error)
    }
  }

  /**
   * 异步增强 Amazon 价格数据
   */
  private async enhanceWithAmazonPrices(formattedData: FormattedProduct[]): Promise<void> {
    try {
      console.info('[ProductListService] 开始异步获取Amazon价格...')

      // 收集所有 Amazon extCode
      const amazonExtCodes: string[] = []

      formattedData.forEach(product => {
        // 收集所有SKU的Amazon extCode
        product.skuList.forEach(sku => {
          if (sku.extCode && sku.extCode.includes('[am]')) {
            amazonExtCodes.push(sku.extCode)
          }
        })

        // 收集主要SKU的Amazon extCode
        if (product.sku.itemNo && product.sku.itemNo.includes('[am]')) {
          amazonExtCodes.push(product.sku.itemNo)
        }
      })

      // 去重
      const uniqueExtCodes = [...new Set(amazonExtCodes)]

      if (uniqueExtCodes.length === 0) {
        console.info('[ProductListService] 没有找到Amazon商品')
        return
      }

      console.info('[ProductListService] 找到Amazon商品:', uniqueExtCodes.length, '个')

      // 通过 Amazon 价格服务批量获取价格
      const amazonPriceService = await this.getAmazonPriceService()
      const amazonPricesMap = await amazonPriceService.batchGetAmazonPriceInfo(uniqueExtCodes)

      console.info('[ProductListService] Amazon价格获取完成，开始更新数据...')

      // 更新商品数据
      formattedData.forEach(product => {
        let hasAmazonPrice = false

        // 更新所有SKU的Amazon价格
        product.skuList.forEach(sku => {
          if (sku.extCode && amazonPricesMap.has(sku.extCode)) {
            const amazonPriceInfo = amazonPricesMap.get(sku.extCode)!

            if (amazonPriceInfo.amazonPrice) {
              sku.amazonPrice = {
                usdPrice: amazonPriceInfo.amazonPrice.usdPrice,
                stock: amazonPriceInfo.amazonPrice.stock,
                costInCNY: parseFloat(amazonPriceInfo.amazonPrice.usdPrice) * 7.2,
                lastUpdated: amazonPriceInfo.lastUpdated,
                dataSource: amazonPriceInfo.dataSource
              }
              hasAmazonPrice = true
            }
          }
        })

        // 更新主要SKU的Amazon价格
        if (product.sku.itemNo && amazonPricesMap.has(product.sku.itemNo)) {
          const amazonPriceInfo = amazonPricesMap.get(product.sku.itemNo)!

          if (amazonPriceInfo.amazonPrice) {
            product.sku.amazonPrice = {
              usdPrice: amazonPriceInfo.amazonPrice.usdPrice,
              stock: amazonPriceInfo.amazonPrice.stock,
              costInCNY: parseFloat(amazonPriceInfo.amazonPrice.usdPrice) * 7.2,
              lastUpdated: amazonPriceInfo.lastUpdated,
              dataSource: amazonPriceInfo.dataSource
            }
            hasAmazonPrice = true
          }
        }

        // 更新产品级别的价格信息
        if (hasAmazonPrice && product.priceInfo) {
          const amazonSku = product.sku.amazonPrice ||
                           product.skuList.find(sku => sku.amazonPrice)?.amazonPrice

          if (amazonSku) {
            product.priceInfo.costInCNY = amazonSku.costInCNY
            product.priceInfo.lastUpdated = amazonSku.lastUpdated
            product.priceInfo.dataSource = amazonSku.dataSource
          }
        }
      })

      // 触发界面更新
      this.triggerDataUpdate('amazonCostDataUpdated', formattedData)

    } catch (error) {
      console.error('[ProductListService] 异步获取Amazon价格失败:', error)
    }
  }

  /**
   * 解析官方申报价API响应
   */
  private parseOfficialPricesResponse(response: any): Map<number, number> {
    const result = new Map<number, number>()

    if (!response.success || !response.data?.success || !response.data?.result) {
      return result
    }

    const apiResult = response.data.result

    // 处理新的API响应格式
    if (apiResult.priceReviewItemList && Array.isArray(apiResult.priceReviewItemList)) {
      apiResult.priceReviewItemList.forEach((item: any) => {
        if (item?.id && typeof item.suggestSupplyPrice === 'number') {
          const actualPrice = item.suggestSupplyPrice / 100
          result.set(item.id, actualPrice)
        }
      })
    }
    // 兼容旧格式
    else if (Array.isArray(apiResult)) {
      apiResult.forEach((item: any) => {
        if (item?.priceOrderId && typeof item.declaredPrice === 'number') {
          const actualPrice = item.declaredPrice / 100
          result.set(item.priceOrderId, actualPrice)
        }
      })
    }

    return result
  }

  /**
   * 触发数据更新事件
   */
  private triggerDataUpdate(eventName: string, data: any): void {
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent(eventName, {
        detail: { updatedData: data }
      }))
    }
  }

  /**
   * 获取状态文本
   */
  private getStatusText(selectStatus: number): string {
    const statusMap: Record<number, string> = {
      0: '待选择',
      1: '已选择',
      2: '已下架',
      3: '已删除'
    }
    return statusMap[selectStatus] || '未知状态'
  }

  /**
   * 计算价格范围
   */
  private calculatePriceRange(skuList: any[]): {
    min: number
    max: number
    minText: string
    maxText: string
    range: string
  } {
    const prices = skuList
      .map(sku => sku.siteSupplierPriceList?.[0]?.supplierPriceValue || 0)
      .filter(price => price > 0)

    if (prices.length === 0) {
      return {
        min: 0,
        max: 0,
        minText: '¥0.00',
        maxText: '¥0.00',
        range: '¥0.00'
      }
    }

    const min = Math.min(...prices) / 100
    const max = Math.max(...prices) / 100

    return {
      min,
      max,
      minText: `¥${min.toFixed(2)}`,
      maxText: `¥${max.toFixed(2)}`,
      range: min === max ? `¥${min.toFixed(2)}` : `¥${min.toFixed(2)} - ¥${max.toFixed(2)}`
    }
  }
}

// 导出单例
export const productListService = new ProductListService()
export default productListService
