// Content script for Temu extension - provides API communication functionality
// No UI injection - only background functionality

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}

console.info("hello world from content-script")

// 检测店小秘登录状态 - 通过API调用检测
async function checkDianxiaomiLoginStatus(): Promise<{ isLoggedIn: boolean; message: string; shopCount?: number }> {
  try {
    console.info('[Content Script] Starting DianXiaoMi login status check...')

    // 检查当前URL
    const currentUrl = window.location.href
    console.info('[Content Script] Current URL:', currentUrl)

    // 检查是否在店小秘域名下
    if (!currentUrl.includes('dianxiaomi.com')) {
      return {
        isLoggedIn: false,
        message: '当前不在店小秘网站'
      }
    }

    // 使用店铺列表API来检测登录状态
    console.info('[Content Script] 通过API检测店小秘登录状态...')

    const response = await fetch('https://www.dianxiaomi.com/shop/list/pddkj.htm', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'text/html, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': navigator.userAgent
      }
    })

    console.info('[Content Script] 登录检测API响应状态:', response.status)

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        return {
          isLoggedIn: false,
          message: '未登录店小秘，请先登录'
        }
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const htmlText = await response.text()
    console.info('[Content Script] 登录检测API响应长度:', htmlText.length)

    // 检查响应内容来判断登录状态
    if (htmlText.includes('login') && htmlText.includes('password')) {
      return {
        isLoggedIn: false,
        message: '检测到登录页面，请先登录店小秘'
      }
    }

    // 检查是否包含店铺数据
    const shopRows = htmlText.match(/data-id="\d+"/g)
    const shopCount = shopRows ? shopRows.length : 0

    console.info('[Content Script] 页面内容分析:')
    console.info('- 响应长度:', htmlText.length)
    console.info('- 包含data-id的匹配:', shopRows)
    console.info('- 检测到店铺数量:', shopCount)
    console.info('- 包含data-shopName:', htmlText.includes('data-shopName'))
    console.info('- 包含pddKJCurrency:', htmlText.includes('pddKJCurrency'))
    console.info('- 包含login关键词:', htmlText.includes('login'))
    console.info('- 包含password关键词:', htmlText.includes('password'))

    if (shopCount > 0) {
      console.info('[Content Script] 检测到店铺数量:', shopCount)
      return {
        isLoggedIn: true,
        message: `已登录店小秘，检测到 ${shopCount} 个Temu店铺`,
        shopCount: shopCount
      }
    }

    // 检查是否有其他登录标识
    if (htmlText.includes('data-shopName') || htmlText.includes('pddKJCurrency')) {
      console.info('[Content Script] 检测到登录标识，但没有店铺数据')
      return {
        isLoggedIn: true,
        message: '已登录店小秘，但可能没有Temu店铺数据',
        shopCount: 0
      }
    }

    // 如果响应正常但没有店铺数据，可能是权限问题
    if (htmlText.length > 1000) {
      // 输出页面内容的一部分用于调试
      const pagePreview = htmlText.substring(0, 500)
      console.info('[Content Script] 页面内容预览:', pagePreview)

      // 检查页面标题
      const titleMatch = htmlText.match(/<title[^>]*>([^<]+)<\/title>/i)
      const pageTitle = titleMatch ? titleMatch[1] : '未知'
      console.info('[Content Script] 页面标题:', pageTitle)

      return {
        isLoggedIn: true,
        message: `已登录店小秘，但可能没有Temu店铺权限。页面标题: ${pageTitle}`,
        shopCount: 0
      }
    }

    return {
      isLoggedIn: false,
      message: `无法确定登录状态，响应长度: ${htmlText.length}`
    }

  } catch (error) {
    console.error('[Content Script] 登录状态检测失败:', error)

    // 降级到URL检测
    const currentUrl = window.location.href
    if (currentUrl.includes('/index.htm')) {
      return {
        isLoggedIn: false,
        message: '未登录店小秘，请先登录'
      }
    }

    if (currentUrl.includes('/home.htm') || currentUrl.includes('/sys/')) {
      return {
        isLoggedIn: true,
        message: '已登录店小秘（基于URL判断）'
      }
    }

    return {
      isLoggedIn: false,
      message: `登录状态检测失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

// 监听来自扩展的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.info('[Content Script] 收到消息:', request)

  if (request.action === 'GET_ANTI_CONTENT') {
    // 获取页面中的 anti-content 值
    const antiContent = getAntiContentFromPage()
    console.info('[Content Script] 获取到 anti-content:', antiContent)
    sendResponse({ antiContent })
    return true
  }

  if (request.action === 'GET_TEMU_DATA') {
    // 通过页面上下文调用API
    getTemuDataFromPage(request.apiType, request.params, request.mallId)
      .then(data => {
        console.info('[Content Script] 获取到数据:', data)
        sendResponse({ success: true, data })
      })
      .catch(error => {
        console.error('[Content Script] 获取数据失败:', error)
        sendResponse({ success: false, error: error.message })
      })
    return true
  }

  if (request.action === 'CHECK_DIANXIAOMI_LOGIN') {
    // 检测店小秘登录状态
    console.info('[Content Script] 开始异步检测店小秘登录状态...')
    checkDianxiaomiLoginStatus()
      .then(loginStatus => {
        console.info('[Content Script] 店小秘登录状态:', loginStatus)
        sendResponse({
          success: true,
          isLoggedIn: loginStatus.isLoggedIn,
          message: loginStatus.message,
          shopCount: loginStatus.shopCount
        })
      })
      .catch(error => {
        console.error('[Content Script] 检测登录状态失败:', error)
        sendResponse({
          success: false,
          isLoggedIn: false,
          message: error instanceof Error ? error.message : '检测登录状态失败'
        })
      })
    return true
  }

  if (request.action === 'GET_SHOP_ACCOUNTS') {
    // 获取店铺账号列表
    console.info('[Content Script] 开始获取店铺账号列表...')
    getShopAccountsFromPage()
      .then(result => {
        console.info('[Content Script] 获取店铺账号结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Content Script] 获取店铺账号失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取店铺账号失败'
        })
      })
    return true
  }

  if (request.action === 'GET_WAREHOUSES') {
    // 获取发货仓库列表
    console.info('[Content Script] 开始获取发货仓库列表...')
    getWarehousesFromPage()
      .then(result => {
        console.info('[Content Script] 获取仓库列表结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Content Script] 获取仓库列表失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取仓库列表失败'
        })
      })
    return true
  }

  if (request.action === 'GET_FREIGHT_TEMPLATES') {
    // 获取运费模板列表
    console.info('[Content Script] 开始获取运费模板列表...')
    getFreightTemplatesFromPage()
      .then(result => {
        console.info('[Content Script] 获取运费模板结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Content Script] 获取运费模板失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取运费模板失败'
        })
      })
    return true
  }

  if (request.action === 'GET_PRODUCT_CATEGORIES') {
    // 获取商品分类列表
    console.info('[Content Script] 开始获取商品分类列表...', { parentId: request.parentId })
    getProductCategoriesFromPage(request.parentId)
      .then(result => {
        console.info('[Content Script] 获取商品分类结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Content Script] 获取商品分类失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取商品分类失败'
        })
      })
    return true
  }

  if (request.action === 'GET_TEMU_CATEGORIES') {
    // 获取Temu商品分类列表（指定店铺）
    console.info('[Content Script] 开始获取Temu商品分类列表...', {
      shopId: request.shopId,
      categoryParentId: request.categoryParentId
    })
    getTemuCategoriesFromPage(request.shopId, request.categoryParentId)
      .then(result => {
        console.info('[Content Script] 获取Temu商品分类结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Content Script] 获取Temu商品分类失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取Temu商品分类失败'
        })
      })
    return true
  }

  if (request.action === 'SAVE_PRODUCT_CONFIG') {
    // 保存商品配置
    console.info('[Content Script] 收到保存商品配置请求:', request.config)
    try {
      // 这里可以添加额外的处理逻辑
      sendResponse({ success: true, message: '配置已接收' })
    } catch (error) {
      console.error('[Content Script] 处理配置保存失败:', error)
      sendResponse({ success: false, error: error instanceof Error ? error.message : '处理失败' })
    }
    return true
  }

  if (request.action === 'MAKE_AUTHENTICATED_REQUEST') {
    // 执行认证请求
    console.info('[Content Script] 收到认证请求:', request.url, request.options)
    makeAuthenticatedRequest(request.url, request.options)
      .then(data => {
        console.info('[Content Script] 认证请求成功:', data)
        sendResponse({ success: true, data })
      })
      .catch(error => {
        console.error('[Content Script] 认证请求失败:', error)
        sendResponse({ success: false, error: error.message })
      })
    return true
  }
})

// 从拦截器缓存中获取 anti-content（优先使用）
function getAntiContentFromInterceptor(): string | null {
  try {
    const value = localStorage.getItem('temu_cs_anti_content')
    const expiryStr = localStorage.getItem('temu_cs_anti_content_expiry')

    if (value && expiryStr && Date.now() < parseInt(expiryStr, 10)) {
      console.info('[Content Script] 从拦截器缓存获取到 anti-content:', value.substring(0, 50) + '...')
      return value
    }

    // 如果过期，清除缓存
    if (value) {
      localStorage.removeItem('temu_cs_anti_content')
      localStorage.removeItem('temu_cs_anti_content_expiry')
      console.warn('[Content Script] 拦截器缓存的 anti-content 已过期，已清除')
    }

    return null
  } catch (error) {
    console.error('[Content Script] 从拦截器缓存获取 anti-content 失败:', error)
    return null
  }
}

// 从拦截器缓存中获取指定头部
function getCachedHeaderFromInterceptor(headerKey: string): string | null {
  try {
    const value = localStorage.getItem(`temu_cs_${headerKey}`)
    const expiryStr = localStorage.getItem(`temu_cs_${headerKey}_expiry`)

    if (value && expiryStr && Date.now() < parseInt(expiryStr, 10)) {
      return value
    }

    // 如果过期，清除缓存
    if (value) {
      localStorage.removeItem(`temu_cs_${headerKey}`)
      localStorage.removeItem(`temu_cs_${headerKey}_expiry`)
    }

    return null
  } catch (error) {
    console.error(`[Content Script] 从拦截器缓存获取 ${headerKey} 失败:`, error)
    return null
  }
}

// 从拦截器缓存添加认证头部
function addAuthHeadersFromInterceptor(headers: Record<string, string>): void {
  const authHeaders = [
    { key: 'authorization', storageKey: 'authorization' },
    { key: 'x-requested-with', storageKey: 'x_requested_with' },
    { key: 'x-csrf-token', storageKey: 'x_csrf_token' },
    { key: 'session-id', storageKey: 'session_id' }
  ]

  authHeaders.forEach(({ key, storageKey }) => {
    const value = getCachedHeaderFromInterceptor(storageKey)
    if (value) {
      headers[key] = value
      console.info(`[Content Script] 添加认证头部 ${key}:`, value.substring(0, 20) + '...')
    }
  })
}

// 从页面中获取 anti-content 值
function getAntiContentFromPage(): string | null {
  try {
    console.info('[Content Script] 开始获取 anti-content...')

    // 方法1: 拦截网络请求获取最新的 anti-content
    const antiContent = interceptAntiContentFromRequests()
    if (antiContent) {
      console.info('[Content Script] 从网络请求中获取到 anti-content:', antiContent.substring(0, 50) + '...')
      return antiContent
    }

    // 方法2: 从页面的全局变量中获取
    const win = window as any
    if (win.__ANTI_CONTENT__) {
      console.info('[Content Script] 从全局变量获取到 anti-content')
      return win.__ANTI_CONTENT__
    }

    // 方法3: 从页面的meta标签中获取
    const metaTag = document.querySelector('meta[name="anti-content"]')
    if (metaTag) {
      const content = metaTag.getAttribute('content')
      if (content) {
        console.info('[Content Script] 从meta标签获取到 anti-content')
        return content
      }
    }

    // 方法4: 从localStorage中获取
    const storedAntiContent = localStorage.getItem('anti-content')
    if (storedAntiContent) {
      console.info('[Content Script] 从localStorage获取到 anti-content')
      return storedAntiContent
    }

    // 方法5: 尝试从页面的script标签中提取
    const scripts = document.querySelectorAll('script')
    for (const script of scripts) {
      const content = script.textContent || script.innerHTML
      // 尝试多种模式匹配
      const patterns = [
        /anti-content['"]\s*:\s*['"]([^'"]+)['"]/i,
        /"anti-content"\s*:\s*"([^"]+)"/i,
        /'anti-content'\s*:\s*'([^']+)'/i,
        /antiContent['"]\s*:\s*['"]([^'"]+)['"]/i,
        /ANTI_CONTENT['"]\s*:\s*['"]([^'"]+)['"]/i
      ]

      for (const pattern of patterns) {
        const match = content.match(pattern)
        if (match) {
          console.info('[Content Script] 从script标签获取到 anti-content')
          return match[1]
        }
      }
    }

    console.warn('[Content Script] 未能找到 anti-content')
    return null
  } catch (error) {
    console.error('[Content Script] 获取 anti-content 失败:', error)
    return null
  }
}

// 拦截网络请求获取 anti-content
function interceptAntiContentFromRequests(): string | null {
  try {
    // 检查最近的网络请求
    const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]

    // 查找包含 anti-content 的请求
    for (const entry of entries.reverse()) {
      if (entry.name.includes('seller.kuajingmaihuo.com')) {
        console.info('[Content Script] 检查请求:', entry.name)
        // 这里我们无法直接获取请求头，但可以尝试其他方法
      }
    }

    // 尝试从页面的 fetch 拦截中获取
    const originalFetch = window.fetch
    let capturedAntiContent: string | null = null

    // 临时拦截 fetch 请求
    window.fetch = function(...args) {
      const [url, options] = args
      if (typeof url === 'string' && url.includes('seller.kuajingmaihuo.com')) {
        const headers = options?.headers as Record<string, string>
        if (headers && headers['anti-content']) {
          capturedAntiContent = headers['anti-content']
          console.info('[Content Script] 从fetch请求中捕获到 anti-content')
        }
      }
      return originalFetch.apply(this, args)
    }

    // 恢复原始 fetch
    setTimeout(() => {
      window.fetch = originalFetch
    }, 1000)

    return capturedAntiContent
  } catch (error) {
    console.error('[Content Script] 拦截网络请求失败:', error)
    return null
  }
}

// 通过页面上下文调用API
async function getTemuDataFromPage(apiType: string, params: any, mallId: string): Promise<any> {
  try {
    console.info('[Content Script] 开始通过页面上下文调用API:', apiType, params)

    // 优先从拦截器缓存获取 anti-content
    const antiContent = getAntiContentFromInterceptor() || getAntiContentFromPage()

    const headers: Record<string, string> = {
      'accept': '*/*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'cache-control': 'max-age=0',
      'origin': 'https://seller.kuajingmaihuo.com',
      'referer': 'https://seller.kuajingmaihuo.com/',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'priority': 'u=1, i'
    }

    if (antiContent) {
      headers['anti-content'] = antiContent
      console.info('[Content Script] 使用 anti-content:', antiContent.substring(0, 50) + '...')
    } else {
      console.warn('[Content Script] 未找到 anti-content，API 调用可能失败')
    }

    if (mallId) {
      headers['mallid'] = mallId
      console.info('[Content Script] 使用 mallId:', mallId)
    } else {
      // 尝试从拦截器缓存获取 mallId
      const cachedMallId = getCachedHeaderFromInterceptor('mall_id')
      if (cachedMallId) {
        headers['mallid'] = cachedMallId
        console.info('[Content Script] 从拦截器缓存获取到 mallId:', cachedMallId)
      } else {
        console.warn('[Content Script] 未找到 mallId')
      }
    }

    // 添加其他认证头部
    addAuthHeadersFromInterceptor(headers)

    let url: string
    let body: string

    if (apiType === 'todo') {
      url = 'https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount'
      body = JSON.stringify({})
    } else if (apiType === 'products') {
      url = 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier'
      body = JSON.stringify(params)
    } else {
      throw new Error(`未知的 API 类型: ${apiType}`)
    }

    console.info('[Content Script] 发送请求:', { url, headers, body })

    const response = await fetch(url, {
      method: 'POST',
      headers,
      credentials: 'include',
      body
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[Content Script] 收到响应:', data)

    return data
  } catch (error) {
    console.error('[Content Script] API调用失败:', error)
    throw error
  }
}

// 获取店铺账号列表
async function getShopAccountsFromPage(): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    console.info('[Content Script] 开始获取店铺账号数据...')

    // 使用API方式获取店铺列表
    console.info('[Content Script] 使用API方式获取店铺列表...')

    const response = await fetch('https://www.dianxiaomi.com/shop/list/pddkj.htm', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'text/html, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': navigator.userAgent
      }
    })

    console.info('[Content Script] 店铺列表API响应状态:', response.status)

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        return { success: false, error: '未登录店小秘，请先登录' }
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const htmlText = await response.text()
    console.info('[Content Script] 店铺列表API响应长度:', htmlText.length)

    // 检查是否是登录页面
    if (htmlText.includes('login') && htmlText.includes('password')) {
      return { success: false, error: '检测到登录页面，请先登录店小秘' }
    }

    // 创建临时DOM来解析HTML
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlText, 'text/html')

    // 解析店铺信息
    const shopAccounts: any[] = []

    // 尝试多种选择器
    const selectors = [
      'tr.content[data-id]',
      'tr[data-id]',
      '[data-id][data-shopName]',
      '.shop-row[data-id]'
    ]

    let shopRows: NodeListOf<Element> | null = null

    for (const selector of selectors) {
      shopRows = doc.querySelectorAll(selector)
      console.info(`[Content Script] 使用选择器 "${selector}" 找到行数:`, shopRows.length)
      if (shopRows.length > 0) {
        break
      }
    }

    if (!shopRows || shopRows.length === 0) {
      // 尝试查找包含店铺ID的所有元素
      const allElementsWithDataId = doc.querySelectorAll('[data-id]')
      console.info('[Content Script] 所有包含data-id的元素数量:', allElementsWithDataId.length)

      allElementsWithDataId.forEach((element, index) => {
        console.info(`[Content Script] 元素 ${index + 1}:`, {
          tagName: element.tagName,
          className: element.className,
          dataId: element.getAttribute('data-id'),
          dataShopName: element.getAttribute('data-shopName'),
          innerHTML: element.innerHTML.substring(0, 100) + '...'
        })
      })

      return {
        success: false,
        error: `未找到店铺数据。页面内容长度: ${htmlText.length}，包含data-id的元素: ${allElementsWithDataId.length}`
      }
    }

    shopRows.forEach((row, index) => {
      try {
        const shopId = row.getAttribute('data-id') || ''
        const shopName = row.getAttribute('data-shopName') || ''

        console.info(`[Content Script] 解析店铺 ${index + 1}:`, {
          shopId,
          shopName,
          tagName: row.tagName,
          className: row.className
        })

        // 获取币种
        const currencyElement = row.querySelector('.pddKJCurrency') ||
                               row.querySelector('[class*="currency"]') ||
                               row.querySelector('[class*="Currency"]')
        const currency = currencyElement?.textContent?.trim() || 'CNY'

        // 获取授权时间和过期时间
        const timeElements = row.querySelectorAll('td p span, span, p')
        let authTime = ''
        let expireTime = ''

        timeElements.forEach(span => {
          const text = span.textContent || ''
          if (text.includes('授权时间：')) {
            authTime = text.replace('授权时间：', '').trim()
          } else if (text.includes('过期时间：')) {
            expireTime = text.replace('过期时间：', '').trim()
          }
        })

        console.info(`[Content Script] 店铺详细信息:`, {
          shopId,
          shopName,
          currency,
          authTime,
          expireTime
        })

        if (shopId && shopName) {
          shopAccounts.push({
            shopId,
            shopName,
            currency,
            authTime,
            expireTime
          })
        } else {
          console.warn(`[Content Script] 店铺 ${index + 1} 缺少必要信息:`, { shopId, shopName })
        }
      } catch (error) {
        console.error(`[Content Script] 解析店铺 ${index + 1} 失败:`, error)
      }
    })

    console.info('[Content Script] 解析完成，共找到有效店铺:', shopAccounts.length)

    if (shopAccounts.length === 0) {
      // 输出页面内容的一部分用于调试
      const pagePreview = htmlText.substring(0, 1000)
      console.info('[Content Script] 页面内容预览:', pagePreview)

      return {
        success: false,
        error: `解析到 ${shopRows?.length || 0} 行数据，但没有有效的店铺信息。请检查页面是否正确加载。`
      }
    }

    return {
      success: true,
      data: shopAccounts
    }
  } catch (error) {
    console.error('[Content Script] 获取店铺账号失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取店铺信息失败'
    }
  }
}

// 获取发货仓库列表
async function getWarehousesFromPage(): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.info('[Content Script] 开始获取发货仓库列表...')

    // 首先获取店铺信息
    const shopAccounts = await getShopAccountsFromPage()
    let shopId = ''

    if (shopAccounts.success && shopAccounts.data && shopAccounts.data.length > 0) {
      shopId = shopAccounts.data[0].shopId
      console.info('[Content Script] 使用店铺ID:', shopId)
    } else {
      return {
        success: false,
        error: '无法获取店铺ID，请先同步店铺账号'
      }
    }

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append('shopId', shopId)
    formData.append('siteIdStr', '100')

    const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/warehouseList.json', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData.toString()
    })

    console.info('[Content Script] 仓库API响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[Content Script] 仓库API响应数据:', data)

    if (data.code === 0) {
      return {
        success: true,
        data: data.data
      }
    } else {
      return {
        success: false,
        error: data.msg || '获取仓库列表失败'
      }
    }
  } catch (error) {
    console.error('[Content Script] 获取仓库列表失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '请求失败'
    }
  }
}

// 获取运费模板列表
async function getFreightTemplatesFromPage(): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    console.info('[Content Script] 开始获取运费模板列表...')

    // 首先获取店铺信息
    const shopAccounts = await getShopAccountsFromPage()
    let shopId = ''

    if (shopAccounts.success && shopAccounts.data && shopAccounts.data.length > 0) {
      shopId = shopAccounts.data[0].shopId
      console.info('[Content Script] 使用店铺ID:', shopId)
    } else {
      return {
        success: false,
        error: '无法获取店铺ID，请先同步店铺账号'
      }
    }

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append('shopId', shopId)
    formData.append('siteIdStr', '100')

    const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/syncTemuShipments.json', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData.toString()
    })

    console.info('[Content Script] 运费模板API响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[Content Script] 运费模板API响应数据:', data)

    if (data.code === 0) {
      return {
        success: true,
        data: data.data
      }
    } else {
      return {
        success: false,
        error: data.msg || '获取运费模板失败'
      }
    }
  } catch (error) {
    console.error('[Content Script] 获取运费模板失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '请求失败'
    }
  }
}

// 获取商品分类列表
async function getProductCategoriesFromPage(categoryParentId?: number): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    console.info('[Content Script] 开始获取商品分类列表...', { categoryParentId })

    // 首先获取店铺信息
    const shopAccounts = await getShopAccountsFromPage()
    let shopId = ''

    if (shopAccounts.success && shopAccounts.data && shopAccounts.data.length > 0) {
      shopId = shopAccounts.data[0].shopId
      console.info('[Content Script] 使用店铺ID:', shopId)
    } else {
      return {
        success: false,
        error: '无法获取店铺ID，请先同步店铺账号'
      }
    }

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append('shopId', shopId)

    // 如果提供了父分类ID，则获取子分类；否则获取根分类
    if (categoryParentId !== undefined) {
      formData.append('categoryParentId', categoryParentId.toString())
    } else {
      formData.append('categoryParentId', '1') // 默认获取根分类
    }

    const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData.toString()
    })

    console.info('[Content Script] 商品分类API响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[Content Script] 商品分类API响应数据:', data)

    if (data.code === 0) {
      return {
        success: true,
        data: data.data
      }
    } else {
      return {
        success: false,
        error: data.msg || '获取商品分类失败'
      }
    }
  } catch (error) {
    console.error('[Content Script] 获取商品分类失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '请求失败'
    }
  }
}

// 获取指定店铺的Temu商品分类列表
async function getTemuCategoriesFromPage(shopId: string, categoryParentId?: number): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    console.info('[Content Script] 开始获取Temu商品分类列表...', { shopId, categoryParentId })

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append('shopId', shopId)

    // 如果提供了父分类ID，则获取子分类；否则获取根分类（默认为0）
    if (categoryParentId !== undefined) {
      formData.append('categoryParentId', categoryParentId.toString())
    } else {
      formData.append('categoryParentId', '0')
    }

    const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData.toString()
    })

    console.info('[Content Script] Temu商品分类API响应状态:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[Content Script] Temu商品分类API响应数据:', data)

    if (data.code === 0) {
      return {
        success: true,
        data: data.data
      }
    } else {
      return {
        success: false,
        error: data.msg || '获取Temu商品分类失败'
      }
    }
  } catch (error) {
    console.error('[Content Script] 获取Temu商品分类失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '请求失败'
    }
  }
}

// 监听店小秘商品配置保存事件
function setupProductConfigListener() {
  // 检查是否在店小秘商品配置页面
  if (!window.location.href.includes('popTemuAdd.htm')) {
    return
  }

  console.info('[Content Script] 设置商品配置保存监听器')

  // 监听表单提交事件
  const forms = document.querySelectorAll('form')
  forms.forEach(form => {
    form.addEventListener('submit', async (event) => {
      console.info('[Content Script] 检测到表单提交事件')

      // 延迟一下，等待表单数据处理完成
      setTimeout(() => {
        captureProductConfig()
      }, 1000)
    })
  })

  // 监听保存按钮点击事件
  const saveButtons = document.querySelectorAll('button[type="submit"], input[type="submit"], .save-btn, [onclick*="save"]')
  saveButtons.forEach(button => {
    button.addEventListener('click', async (event) => {
      console.info('[Content Script] 检测到保存按钮点击')

      // 延迟一下，等待数据处理完成
      setTimeout(() => {
        captureProductConfig()
      }, 1500)
    })
  })

  // 监听 AJAX 请求完成事件
  const originalFetch = window.fetch
  window.fetch = async function(...args) {
    const response = await originalFetch.apply(this, args)

    // 检查是否是商品保存相关的请求
    const url = args[0] as string
    if (url && (url.includes('popTemuProduct/add') || url.includes('popTemuSave'))) {
      console.info('[Content Script] 检测到商品保存请求:', url)

      // 延迟捕获配置
      setTimeout(() => {
        captureProductConfig()
      }, 2000)
    }

    return response
  }
}

// 捕获商品配置数据
function captureProductConfig() {
  try {
    console.info('[Content Script] 开始捕获商品配置数据')

    // 尝试从页面中提取配置数据
    const config = extractProductConfigFromPage()

    if (config) {
      console.info('[Content Script] 成功捕获商品配置:', config)

      // 发送配置数据到插件
      chrome.runtime.sendMessage({
        action: 'SAVE_PRODUCT_CONFIG',
        config: config
      }).then(response => {
        console.info('[Content Script] 配置保存响应:', response)
      }).catch(error => {
        console.error('[Content Script] 发送配置失败:', error)
      })

      // 也可以通过 postMessage 发送给父页面
      if (window.opener) {
        window.opener.postMessage({
          type: 'TEMU_PRODUCT_CONFIG_SAVED',
          config: config
        }, '*')
      }
    } else {
      console.warn('[Content Script] 未能捕获到商品配置数据')
    }
  } catch (error) {
    console.error('[Content Script] 捕获商品配置失败:', error)
  }
}

// 从页面中提取商品配置数据
function extractProductConfigFromPage(): any {
  try {
    // 方法1: 从全局变量中获取
    const win = window as any
    if (win.productConfig) {
      return win.productConfig
    }

    // 方法2: 从表单数据中构建
    const formData = extractFormData()
    if (formData) {
      return formData
    }

    // 方法3: 从页面元素中提取
    const pageData = extractPageData()
    if (pageData) {
      return pageData
    }

    return null
  } catch (error) {
    console.error('[Content Script] 提取配置数据失败:', error)
    return null
  }
}

// 提取表单数据
function extractFormData(): any {
  try {
    const forms = document.querySelectorAll('form')
    if (forms.length === 0) return null

    const form = forms[0]
    const formData = new FormData(form)
    const data: any = {}

    formData.forEach((value, key) => {
      data[key] = value
    })

    // 提取其他必要信息
    const urlParams = new URLSearchParams(window.location.search)
    const shopId = urlParams.get('shopId')
    const categoryId = urlParams.get('categoryId')

    if (shopId && categoryId) {
      return {
        data: data,
        form: {
          shopId: shopId,
          categoryId: parseInt(categoryId),
          ...data
        },
        apiConfig: {
          erp: "dianXiaoMi",
          platform: "temu",
          pointKey: "popTemuSave"
        },
        siteConfig: {
          currentSite: "ShangTemuView",
          erp: "dianXiaoMi"
        }
      }
    }

    return null
  } catch (error) {
    console.error('[Content Script] 提取表单数据失败:', error)
    return null
  }
}

// 提取页面数据
function extractPageData(): any {
  try {
    // 从页面的 script 标签中查找配置数据
    const scripts = document.querySelectorAll('script')
    for (const script of scripts) {
      const content = script.textContent || script.innerHTML

      // 查找配置相关的数据
      const patterns = [
        /var\s+config\s*=\s*({.*?});/s,
        /window\.config\s*=\s*({.*?});/s,
        /productConfig\s*=\s*({.*?});/s
      ]

      for (const pattern of patterns) {
        const match = content.match(pattern)
        if (match) {
          try {
            return JSON.parse(match[1])
          } catch (e) {
            continue
          }
        }
      }
    }

    return null
  } catch (error) {
    console.error('[Content Script] 提取页面数据失败:', error)
    return null
  }
}

// 执行认证请求
async function makeAuthenticatedRequest(url: string, options: any): Promise<any> {
  try {
    console.info('[Content Script] 开始执行认证请求:', url, options)

    // 优先从拦截器缓存获取 anti-content
    const antiContent = getAntiContentFromInterceptor() || getAntiContentFromPage()

    const headers: Record<string, string> = {
      'accept': '*/*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'cache-control': 'max-age=0',
      'origin': 'https://seller.kuajingmaihuo.com',
      'referer': 'https://seller.kuajingmaihuo.com/',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent': navigator.userAgent,
      'priority': 'u=1, i'
    }

    // 添加 anti-content
    if (antiContent) {
      headers['anti-content'] = antiContent
      console.info('[Content Script] 添加 anti-content 到请求头')
    }

    // 从拦截器缓存添加其他认证头部
    addAuthHeadersFromInterceptor(headers)

    // 合并用户提供的headers
    if (options?.headers) {
      Object.assign(headers, options.headers)
    }

    // 构建请求选项
    const requestOptions: RequestInit = {
      method: options?.method || 'GET',
      headers,
      credentials: 'include',
      ...options
    }

    console.info('[Content Script] 发送认证请求:', url, requestOptions)

    const response = await fetch(url, requestOptions)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info('[Content Script] 认证请求响应:', data)

    return data
  } catch (error) {
    console.error('[Content Script] 认证请求失败:', error)
    throw error
  }
}

// 页面加载完成后设置监听器
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupProductConfigListener)
} else {
  setupProductConfigListener()
}
