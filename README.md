# 胡建大卖家 Temu 扩展

<div align="center">

![Logo](public/logo.png)

**专业的 Temu 和店小秘平台商品管理工具**

[![Version](https://img.shields.io/badge/version-0.1.0-blue.svg)](package.json)
[![Vue](https://img.shields.io/badge/Vue-3.5.16-4FC08D.svg)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8.3-3178C6.svg)](https://www.typescriptlang.org/)
[![Manifest](https://img.shields.io/badge/Manifest-V3-FF6B6B.svg)](https://developer.chrome.com/docs/extensions/mv3/)

</div>

## 📋 项目简介

胡建大卖家 Temu 扩展是一个基于 Vue 3 + Ant Design Vue 开发的 Chrome 扩展程序，专为 Temu 商家和店小秘用户设计的商品管理工具。通过先进的跨域解决方案和智能化的数据处理，帮助商家高效管理商品信息，实现跨平台商品同步。

## ✨ 核心功能

### 🎯 店小秘商品上传工具
- ✅ **完全兼容原版逻辑** - 保持与 `test_upload.html` 相同的验证和上传机制
- ✅ **跨域问题解决** - 通过 Chrome 扩展 background.js 直接执行上传
- ✅ **本地 JSZip 库** - 使用项目内 `jszip.min.js`，无网络依赖
- ✅ **简化架构设计** - 在 background 中完成 ZIP 创建和 API 调用
- ✅ **完整测试工具** - 提供 `test_upload_final.html` 进行功能验证

### 🏪 Temu 店铺管理
- 🔍 **智能店铺检测** - 自动检测 Temu 登录状态和店铺信息
- 🔗 **后台绑定系统** - 支持多店铺绑定和管理
- 📊 **实时状态监控** - 监控店铺状态和数据同步
- 🛡️ **安全认证机制** - 保护用户数据和店铺信息安全

### 🔄 跨平台数据同步
- 📦 **商品信息同步** - Temu 与店小秘之间的商品数据同步
- 🏷️ **智能数据映射** - 自动处理不同平台的数据格式差异
- ⚡ **批量操作支持** - 支持批量商品上传和更新
- 📈 **操作日志记录** - 详细记录所有操作历史

## 🛠️ 技术栈

### 前端框架
- **Vue 3.5.16** - 渐进式 JavaScript 框架
- **TypeScript 5.8.3** - 类型安全的 JavaScript 超集
- **Ant Design Vue 4.2.6** - 企业级 UI 设计语言和组件库
- **Vite 6.3.5** - 下一代前端构建工具

### 扩展开发
- **Manifest V3** - Chrome 扩展最新规范
- **@crxjs/vite-plugin** - Vite 的 Chrome 扩展插件
- **webextension-polyfill** - 跨浏览器 WebExtension API

### 状态管理与工具
- **Pinia 3.0.2** - Vue 的状态管理库
- **Vue Router 4.5.1** - Vue.js 官方路由器
- **Vue I18n 11.1.5** - Vue.js 国际化插件
- **@vueuse/core** - Vue 组合式 API 工具集

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- pnpm >= 8.0.0 (推荐) 或 npm >= 9.0.0
- Chrome 浏览器 >= 88.0.0

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd temu-dmj-extension
```

2. **安装依赖**
```bash
pnpm install
# 或
npm install
```

3. **开发模式**
```bash
# 开发 Chrome 扩展
pnpm dev:chrome

# 开发 Firefox 扩展
pnpm dev:firefox

# 同时开发两个浏览器
pnpm dev
```

4. **构建生产版本**
```bash
# 构建所有浏览器版本
pnpm build

# 仅构建 Chrome 版本
pnpm build:chrome

# 仅构建 Firefox 版本
pnpm build:firefox
```

### 安装扩展

#### Chrome 浏览器
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `dist/chrome` 目录

#### Firefox 浏览器
1. 打开 Firefox 浏览器
2. 访问 `about:debugging#/runtime/this-firefox`
3. 点击"临时载入附加组件"
4. 选择 `dist/firefox` 目录中的 `manifest.json` 文件
## 📖 使用指南

### 店小秘商品上传

1. **环境准备**
   - 确保已安装并启用扩展
   - 在 `https://www.dianxiaomi.com` 登录店小秘账户
   - 保持店小秘标签页处于打开状态

2. **使用测试工具**
   - 打开项目根目录下的 `test_upload_final.html`
   - 点击"🔍 检查系统状态"确认所有组件就绪
   - 点击"📝 加载示例数据"或手动输入商品 JSON 数据
   - 点击"验证"确保 JSON 格式正确
   - 点击"🚀 开始上传"执行上传流程

3. **状态检查项目**
   - ✅ **扩展环境** - 确认 Chrome 扩展可用
   - ✅ **Background JSZip** - 确认 JSZip 库版本 3.10.1 可用
   - ✅ **店小秘登录** - 确认用户已登录
   - ✅ **系统就绪** - 所有组件状态正常

### Temu 店铺管理

1. **店铺检测**
   - 扩展会自动检测 Temu 登录状态
   - 支持多个 Temu 商家后台域名
   - 实时监控店铺状态变化

2. **店铺绑定**
   - 在侧边栏面板中进行店铺绑定
   - 支持多店铺管理
   - 安全的数据存储和同步

## 📁 项目结构

```
temu-dmj-extension/
├── dist/                    # 构建输出目录
│   ├── chrome/             # Chrome 扩展构建文件
│   └── firefox/            # Firefox 扩展构建文件
├── public/                 # 静态资源
│   └── logo.png           # 扩展图标
├── src/                    # 源代码目录
│   ├── background/         # 后台脚本
│   │   └── index.ts       # 主要的上传逻辑实现
│   ├── components/         # 共享 Vue 组件
│   ├── config/            # 配置文件
│   │   └── index.ts       # 应用配置
│   ├── content-scripts/   # 内容脚本
│   │   └── temu/          # Temu 相关脚本
│   ├── lib/               # 第三方库
│   │   └── jszip.min.js   # JSZip 库文件
│   ├── services/          # 服务层
│   ├── stores/            # Pinia 状态管理
│   ├── ui/                # UI 页面
│   │   ├── action-popup/  # 扩展弹窗
│   │   ├── options-page/  # 选项页面
│   │   ├── setup/         # 设置页面
│   │   └── side-panel/    # 侧边栏面板
│   └── utils/             # 工具函数
├── docs/                   # 文档目录
├── scripts/               # 构建脚本
├── test_upload_final.html # 测试工具页面
├── test_upload_final.js   # 测试工具脚本
├── manifest.config.ts     # Manifest 配置
├── vite.config.ts         # Vite 基础配置
├── vite.chrome.config.ts  # Chrome 专用配置
├── vite.firefox.config.ts # Firefox 专用配置
└── package.json           # 项目依赖和脚本
```

---

## 🔧 开发指南

### 开发环境设置

1. **代码规范**
```bash
# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 类型检查
pnpm typecheck
```

2. **调试模式**
```bash
# 启动开发服务器并自动打开浏览器
pnpm launch

# 启动所有浏览器的开发模式
pnpm launch:all
```

### 配置管理

项目使用分层配置系统：

```typescript
// src/config/index.ts
const config = {
  api: {
    baseUrl: 'http://127.0.0.1:32000/api',
    timeout: 30000,
    retryCount: 3
  },
  temu: {
    sellerDomains: [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      // ...
    ],
    apiUrl: 'https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo'
  },
  dianxiaomi: {
    baseUrl: 'https://api.dianxiaomi.com',
    apiVersion: 'v1'
  }
}
```

### 扩展架构

#### 双环境执行模型
- **MAIN World（页面主环境）**: 能拦截真实网络请求，获取认证信息
- **ISOLATED World（隔离环境）**: 能使用 Chrome API，处理扩展逻辑

#### 通信机制
- **Side Panel** ↔ **Content Script**: 使用 `chrome.tabs.sendMessage()`
- **Content Script** ↔ **页面 API**: 在页面上下文中直接调用
- **Background** ↔ **外部 API**: 使用 `fetch()` 调用后台 API

## 🚨 故障排除

### 常见问题

1. **扩展无法加载**
   - 检查 `dist/chrome` 目录是否存在
   - 确认已开启开发者模式
   - 重新构建项目：`pnpm build:chrome`

2. **店小秘上传失败**
   - 确认已登录店小秘账户
   - 检查网络连接状态
   - 查看浏览器控制台错误信息
   - 重新加载扩展：在 `chrome://extensions/` 重新加载

3. **Temu 店铺检测失败**
   - 确认已登录 Temu 商家后台
   - 检查域名是否在支持列表中
   - 刷新 Temu 页面重试

4. **开发环境问题**
   - 清除 node_modules：`rm -rf node_modules && pnpm install`
   - 清除构建缓存：`rm -rf dist && pnpm build`
   - 检查 Node.js 版本是否 >= 18.0.0

### 调试技巧

1. **查看扩展日志**
   - 打开 `chrome://extensions/`
   - 点击扩展的"检查视图"
   - 查看 Console 和 Network 标签

2. **内容脚本调试**
   - 在目标页面按 F12 打开开发者工具
   - 查看 Console 中的扩展日志

3. **后台脚本调试**
   - 在扩展管理页面点击"Service Worker"
   - 查看后台脚本的执行日志
## 📚 相关文档

- [店小秘商品上传工具说明](./店小秘商品上传工具说明.md) - 详细的上传工具使用指南
- [Chrome V3插件开发指南](.github/code_guide.md) - Vue3 + Ant Design Vue + Manifest V3 开发指南
- [业务逻辑流程文档](./docs/business-logic-flow.md) - 详细的业务流程说明
- [后台API需求文档](./docs/backend-api-requirements.md) - API 接口规范

## 🔗 相关链接

- [Chrome 扩展开发文档](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 迁移指南](https://developer.chrome.com/docs/extensions/migrating/)
- [Vue 3 官方文档](https://vuejs.org/)
- [Ant Design Vue 文档](https://antdv.com/)
- [Vite 官方文档](https://vitejs.dev/)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 代码规范
- 遵循 ESLint 和 Prettier 配置
- 使用 TypeScript 严格模式
- 编写清晰的提交信息
- 添加必要的测试用例

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Ant Design Vue](https://antdv.com/) - 企业级 UI 设计语言
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [@crxjs/vite-plugin](https://crxjs.dev/vite-plugin/) - Chrome 扩展开发工具

## 📞 支持

如果您在使用过程中遇到问题或有任何建议，请：

- 查看 [常见问题](#-故障排除) 部分
- 提交 [Issue](../../issues) 报告问题
- 参考相关文档和开发指南

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**

Made with ❤️ by 胡建大卖家团队

</div>


