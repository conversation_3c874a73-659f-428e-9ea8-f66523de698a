/**
 * Temu认证和缓存管理服务
 * 
 * 功能：
 * - 管理anti-content和mallId的获取、缓存和过期
 * - 提供统一的认证头部构建
 * - 处理登录状态检测和用户信息获取
 * - 兼容多种缓存源和键名
 * 
 * 依赖关系：
 * - 依赖：temu-config.ts (配置和常量)
 * - 被依赖：temu-api.ts, temu-unified.ts
 * 
 * 核心功能：
 * - getAntiContent(): 获取anti-content认证头
 * - getMallId(): 获取mallId
 * - buildAuthHeaders(): 构建完整的认证头部
 * - checkLoginStatus(): 检测登录状态
 */

import {
  TEMU_CONFIG,
  type CacheItem,
  type AuthHeaders,
  type LoginStatus
} from '../../config/temu-config'

class TemuAuthManager {
  private memoryCache = new Map<string, CacheItem>()
  private readonly logPrefix = TEMU_CONFIG.DEBUG.LOG_PREFIX + '[Auth]'

  /**
   * 获取anti-content认证头
   * 优先级：内存缓存 > localStorage > 页面检测
   */
  getAntiContent(): string | null {
    try {
      // 1. 从内存缓存获取
      const cached = this.getFromMemoryCache('anti-content')
      if (cached) {
        console.info(`${this.logPrefix} 从内存缓存获取 anti-content`)
        return cached
      }

      // 2. 从localStorage获取（支持多种键名）
      const storageKeys = [
        TEMU_CONFIG.CACHE.KEYS.ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_ANTI_CONTENT,
        'temu_cs_anti_content'
      ]

      for (const key of storageKeys) {
        const stored = this.getFromLocalStorage(key, TEMU_CONFIG.CACHE.TTL.ANTI_CONTENT)
        if (stored) {
          console.info(`${this.logPrefix} 从localStorage获取 anti-content:`, key)
          // 同步到内存缓存
          this.setToMemoryCache('anti-content', stored, TEMU_CONFIG.CACHE.TTL.ANTI_CONTENT)
          return stored
        }
      }

      // 3. 页面检测已移至 content script 环境处理

      console.warn(`${this.logPrefix} 未找到 anti-content`)
      return null
    } catch (error) {
      console.error(`${this.logPrefix} 获取 anti-content 失败:`, error)
      return null
    }
  }

  /**
   * 设置anti-content到缓存
   */
  setAntiContent(value: string, source = 'manual'): void {
    try {
      const ttl = TEMU_CONFIG.CACHE.TTL.ANTI_CONTENT
      
      // 设置到内存缓存
      this.setToMemoryCache('anti-content', value, ttl, source)
      
      // 设置到localStorage（多个键名确保兼容性）
      this.setToLocalStorage(TEMU_CONFIG.CACHE.KEYS.ANTI_CONTENT, value, ttl)
      this.setToLocalStorage(TEMU_CONFIG.CACHE.KEYS.LEGACY_ANTI_CONTENT, value, ttl)
      this.setToLocalStorage(TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_ANTI_CONTENT, value, ttl)
      
      // 记录来源
      localStorage.setItem(TEMU_CONFIG.CACHE.KEYS.ANTI_CONTENT_SOURCE, source)
      
      console.info(`${this.logPrefix} 成功缓存 anti-content (来源: ${source})`)
    } catch (error) {
      console.error(`${this.logPrefix} 缓存 anti-content 失败:`, error)
    }
  }

  /**
   * 获取mallId
   * 优先级：内存缓存 > localStorage > 页面检测
   */
  getMallId(): string | null {
    try {
      // 1. 从内存缓存获取
      const cached = this.getFromMemoryCache('mall-id')
      if (cached) {
        console.info(`${this.logPrefix} 从内存缓存获取 mallId`)
        return cached
      }

      // 2. 从localStorage获取（支持多种键名）
      const storageKeys = [
        TEMU_CONFIG.CACHE.KEYS.MALL_ID,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_MALL_ID,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_MALL_ID,
        'temu_cs_mall_id'
      ]

      for (const key of storageKeys) {
        const stored = this.getFromLocalStorage(key, TEMU_CONFIG.CACHE.TTL.MALL_ID)
        if (stored) {
          console.info(`${this.logPrefix} 从localStorage获取 mallId:`, key, stored)
          // 同步到内存缓存
          this.setToMemoryCache('mall-id', stored, TEMU_CONFIG.CACHE.TTL.MALL_ID)
          return stored
        }
      }

      console.warn(`${this.logPrefix} 未找到 mallId`)
      return null
    } catch (error) {
      console.error(`${this.logPrefix} 获取 mallId 失败:`, error)
      return null
    }
  }

  /**
   * 设置mallId到缓存
   */
  setMallId(value: string, source = 'manual'): void {
    try {
      const ttl = TEMU_CONFIG.CACHE.TTL.MALL_ID
      
      // 设置到内存缓存
      this.setToMemoryCache('mall-id', value, ttl, source)
      
      // 设置到localStorage（多个键名确保兼容性）
      this.setToLocalStorage(TEMU_CONFIG.CACHE.KEYS.MALL_ID, value, ttl)
      this.setToLocalStorage(TEMU_CONFIG.CACHE.KEYS.LEGACY_MALL_ID, value, ttl)
      this.setToLocalStorage(TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_MALL_ID, value, ttl)
      
      // 记录来源
      localStorage.setItem(TEMU_CONFIG.CACHE.KEYS.MALL_ID_SOURCE, source)
      
      console.info(`${this.logPrefix} 成功缓存 mallId (来源: ${source}):`, value)
    } catch (error) {
      console.error(`${this.logPrefix} 缓存 mallId 失败:`, error)
    }
  }

  /**
   * 构建认证头部
   */
  buildAuthHeaders(customHeaders: Record<string, string> = {}): AuthHeaders {
    const headers: AuthHeaders = {
      ...TEMU_CONFIG.DEFAULT_HEADERS,
      ...customHeaders
    }

    // 添加anti-content
    const antiContent = this.getAntiContent()
    if (antiContent) {
      headers['anti-content'] = antiContent
    }

    // 添加mallId
    const mallId = this.getMallId()
    if (mallId) {
      headers['mallid'] = mallId
    }

    // 其他认证头部可以通过 customHeaders 参数传入

    return headers
  }





  /**
   * 内存缓存操作
   */
  private setToMemoryCache(key: string, value: string, ttl: number, source?: string): void {
    this.memoryCache.set(key, {
      value,
      expiry: Date.now() + ttl,
      source
    })
  }

  private getFromMemoryCache(key: string): string | null {
    const item = this.memoryCache.get(key)
    if (item && Date.now() < item.expiry) {
      return item.value
    }
    if (item) {
      this.memoryCache.delete(key)
    }
    return null
  }

  /**
   * localStorage操作
   */
  private setToLocalStorage(key: string, value: string, ttl: number): void {
    try {
      localStorage.setItem(key, value)
      localStorage.setItem(key + '_expiry', (Date.now() + ttl).toString())
    } catch (error) {
      console.warn(`${this.logPrefix} localStorage写入失败:`, error)
    }
  }

  private getFromLocalStorage(key: string, _defaultTtl: number): string | null {
    try {
      const value = localStorage.getItem(key)
      const expiryStr = localStorage.getItem(key + '_expiry')
      
      if (value) {
        // 如果有过期时间，检查是否过期
        if (expiryStr) {
          const expiry = parseInt(expiryStr, 10)
          if (Date.now() < expiry) {
            return value
          } else {
            // 过期，清除
            localStorage.removeItem(key)
            localStorage.removeItem(key + '_expiry')
          }
        } else {
          // 没有过期时间，检查是否为旧格式
          return value
        }
      }
      
      return null
    } catch (error) {
      console.warn(`${this.logPrefix} localStorage读取失败:`, error)
      return null
    }
  }

  /**
   * 清除所有缓存
   */
  clearCache(): void {
    try {
      // 清除内存缓存
      this.memoryCache.clear()

      // 清除主要的localStorage键
      const mainKeys = [
        TEMU_CONFIG.CACHE.KEYS.ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.MALL_ID,
        TEMU_CONFIG.CACHE.KEYS.USER_INFO,
        // 兼容旧版本
        TEMU_CONFIG.CACHE.KEYS.LEGACY_ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_MALL_ID,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_ANTI_CONTENT,
        TEMU_CONFIG.CACHE.KEYS.LEGACY_CACHED_MALL_ID
      ]

      // 清除主键和对应的过期时间键
      mainKeys.forEach(key => {
        localStorage.removeItem(key)
        localStorage.removeItem(key + '_expiry')
      })

      console.info(`${this.logPrefix} 缓存已清除`)
    } catch (error) {
      console.error(`${this.logPrefix} 清除缓存失败:`, error)
    }
  }

  /**
   * 获取缓存状态报告
   */
  getCacheStatus(): { memoryCache: { size: number }, localStorage: { antiContent: boolean, mallId: boolean }, timestamp: string } {
    return {
      memoryCache: {
        size: this.memoryCache.size
      },
      localStorage: {
        antiContent: !!this.getAntiContent(),
        mallId: !!this.getMallId()
      },
      timestamp: new Date().toLocaleString()
    }
  }
}

// 创建单例实例
export const temuAuthManager = new TemuAuthManager()
export default temuAuthManager

// 导出类型
export type { AuthHeaders, LoginStatus }
