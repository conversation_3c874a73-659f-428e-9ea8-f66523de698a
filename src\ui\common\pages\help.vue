<template>
  <div class="p-6 max-w-4xl mx-auto">
    <RouterLinkUp />

    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">使用帮助</h1>
      <p class="text-lg text-gray-600">胡建大卖家使用指南与技术支持</p>
    </div>

    <div class="grid md:grid-cols-2 gap-8 mb-8">
      <!-- 快速入门 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4 text-blue-600 flex items-center">
          <span class="mr-2">🚀</span>
          快速入门
        </h2>
        <div class="space-y-3">
          <div class="flex items-start">
            <span class="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
            <div>
              <h4 class="font-medium">安装扩展</h4>
              <p class="text-sm text-gray-600">从Chrome应用商店安装胡建大卖家扩展</p>
            </div>
          </div>
          <div class="flex items-start">
            <span class="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
            <div>
              <h4 class="font-medium">绑定店铺</h4>
              <p class="text-sm text-gray-600">登录Temu卖家后台，绑定您的店铺账号</p>
            </div>
          </div>
          <div class="flex items-start">
            <span class="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
            <div>
              <h4 class="font-medium">开始使用</h4>
              <p class="text-sm text-gray-600">配置基础设置，开始商品采集和管理</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4 text-green-600 flex items-center">
          <span class="mr-2">❓</span>
          常见问题
        </h2>
        <div class="space-y-4">
          <div>
            <h4 class="font-medium text-gray-800">如何绑定多个店铺？</h4>
            <p class="text-sm text-gray-600">在设置页面中，点击"添加店铺"按钮，按提示完成绑定流程。</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800">商品上传失败怎么办？</h4>
            <p class="text-sm text-gray-600">检查网络连接和店铺权限，确保商品信息完整且符合平台规范。</p>
          </div>
          <div>
            <h4 class="font-medium text-gray-800">如何设置自动定价？</h4>
            <p class="text-sm text-gray-600">在商品配置中启用智能定价，设置价格倍数和调整策略。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系支持 -->
    <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-8 mb-8">
      <h2 class="text-2xl font-bold text-center mb-6 text-gray-800">技术支持</h2>
      <div class="grid md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <span class="text-3xl mb-2 block">📧</span>
            <h4 class="font-semibold mb-2">邮件支持</h4>
            <p class="text-sm text-gray-600 mb-3">遇到问题？发送邮件给我们</p>
            <a
              href="mailto:<EMAIL>"
              class="text-blue-600 hover:text-blue-800 font-medium"
            >
              <EMAIL>
            </a>
          </div>
        </div>
        <div class="text-center">
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <span class="text-3xl mb-2 block">💬</span>
            <h4 class="font-semibold mb-2">在线客服</h4>
            <p class="text-sm text-gray-600 mb-3">工作时间：9:00-18:00</p>
            <button class="text-green-600 hover:text-green-800 font-medium">
              开始对话
            </button>
          </div>
        </div>
        <div class="text-center">
          <div class="bg-white rounded-lg p-4 shadow-sm">
            <span class="text-3xl mb-2 block">📚</span>
            <h4 class="font-semibold mb-2">使用文档</h4>
            <p class="text-sm text-gray-600 mb-3">详细的功能说明和教程</p>
            <a
              href="#"
              class="text-purple-600 hover:text-purple-800 font-medium"
            >
              查看文档
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center text-sm text-gray-500">
      <p>我们承诺在24小时内回复您的咨询，感谢您的耐心等待。</p>
    </div>

    <div class="flex flex-wrap gap-4 justify-center mt-8">
      <RouterLink
        to="/common/about"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        关于我们
      </RouterLink>

      <RouterLink
        to="/common/features"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
      >
        功能特色
      </RouterLink>

      <RouterLink
        to="/common/privacy-policy"
        class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
      >
        隐私政策
      </RouterLink>
    </div>
  </div>
</template>
