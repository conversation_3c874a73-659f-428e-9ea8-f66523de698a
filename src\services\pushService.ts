/**
 * 推送服务
 * 负责将Amazon商品数据推送到店小秘
 */

export interface PushResult {
  success: boolean
  productId?: string
  message?: string
  error?: string
  data?: any
}

export interface BatchPushProgress {
  total: number
  completed: number
  failed: number
  currentProduct: string
  results: PushResult[]
}

export class PushService {
  private static instance: PushService

  static getInstance(): PushService {
    if (!PushService.instance) {
      PushService.instance = new PushService()
    }
    return PushService.instance
  }

  /**
   * 推送单个商品到店小秘
   */
  async pushSingleProduct(dianxiaomiData: any): Promise<PushResult> {
    try {
      console.info('[PushService] 开始推送单个商品到店小秘...')

      // 验证数据完整性
      this.validateProductData(dianxiaomiData)

      // 打包数据为ZIP格式
      const zipBlob = await this.packageDataAsZip(dianxiaomiData)

      // 推送到店小秘API
      const result = await this.pushToDianxiaomiAPI(zipBlob, dianxiaomiData)

      console.info('[PushService] 商品推送成功:', result)

      return {
        success: true,
        productId: result.productId,
        message: '商品推送成功',
        data: result
      }

    } catch (error) {
      console.error('[PushService] 商品推送失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '推送失败'
      }
    }
  }

  /**
   * 批量推送商品到店小秘
   */
  async batchPushProducts(
    productDataList: any[],
    onProgress?: (progress: BatchPushProgress) => void
  ): Promise<PushResult[]> {
    console.info('[PushService] 开始批量推送商品:', productDataList.length, '个')

    const results: PushResult[] = []
    let completed = 0
    let failed = 0

    for (let i = 0; i < productDataList.length; i++) {
      const productData = productDataList[i]
      const productName = productData.productName || `商品${i + 1}`

      // 更新进度
      onProgress?.({
        total: productDataList.length,
        completed,
        failed,
        currentProduct: productName,
        results: [...results]
      })

      try {
        const result = await this.pushSingleProduct(productData)
        results.push(result)

        if (result.success) {
          completed++
        } else {
          failed++
        }

        // 推送间隔，避免请求过快
        if (i < productDataList.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000))
        }

      } catch (error) {
        console.error('[PushService] 批量推送中的商品失败:', productName, error)
        results.push({
          success: false,
          error: error instanceof Error ? error.message : '推送失败'
        })
        failed++
      }
    }

    // 最终进度更新
    onProgress?.({
      total: productDataList.length,
      completed,
      failed,
      currentProduct: '',
      results
    })

    console.info('[PushService] 批量推送完成:', {
      total: productDataList.length,
      completed,
      failed
    })

    return results
  }

  /**
   * 打包数据为ZIP格式
   */
  private async packageDataAsZip(dianxiaomiData: any): Promise<Blob> {
    try {
      console.info('[PushService] 开始打包数据为ZIP格式...')

      // 使用现有的JSZip组件
      const JSZip = (window as any).JSZip
      if (!JSZip) {
        throw new Error('JSZip组件未加载')
      }

      const zip = new JSZip()

      // 创建主数据文件
      const mainData = {
        productData: dianxiaomiData,
        timestamp: new Date().toISOString(),
        source: 'amazon-collector',
        version: '1.0.0'
      }

      zip.file('product.json', JSON.stringify(mainData, null, 2))

      // 如果有图片数据，创建图片清单
      if (dianxiaomiData.mainImage) {
        const imageUrls = dianxiaomiData.mainImage.split('|').filter(url => url.trim())
        if (imageUrls.length > 0) {
          zip.file('images.json', JSON.stringify({
            mainImages: imageUrls,
            count: imageUrls.length
          }, null, 2))
        }
      }

      // 创建元数据文件
      const metadata = {
        productName: dianxiaomiData.productName,
        categoryId: dianxiaomiData.categoryId,
        shopId: dianxiaomiData.shopId,
        sourceUrl: dianxiaomiData.sourceUrl,
        createdAt: new Date().toISOString(),
        extCode: this.extractExtCodeFromVariations(dianxiaomiData.variationListStr)
      }

      zip.file('metadata.json', JSON.stringify(metadata, null, 2))

      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info('[PushService] ZIP打包完成，大小:', Math.round(zipBlob.size / 1024), 'KB')
      return zipBlob

    } catch (error) {
      console.error('[PushService] ZIP打包失败:', error)
      throw new Error(`数据打包失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 推送到店小秘API
   */
  private async pushToDianxiaomiAPI(zipBlob: Blob, productData: any): Promise<any> {
    try {
      console.info('[PushService] 开始推送到店小秘API...')

      // 创建FormData
      const formData = new FormData()
      formData.append('file', zipBlob, `amazon_product_${Date.now()}.zip`)
      formData.append('type', 'amazon_import')
      formData.append('source', 'chrome_extension')

      // 添加商品基本信息
      formData.append('productName', productData.productName || '')
      formData.append('categoryId', productData.categoryId || '')
      formData.append('shopId', productData.shopId || '')

      // 店小秘商品导入API
      const apiUrl = 'https://www.dianxiaomi.com/api/popTemuProduct/add.json'

      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
        credentials: 'include', // 包含cookies进行身份验证
        headers: {
          // 不设置Content-Type，让浏览器自动设置multipart/form-data
        }
      })

      if (!response.ok) {
        throw new Error(`API请求失败: HTTP ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        console.info('[PushService] 推送成功:', result)
        return {
          productId: result.data?.productId || result.data?.id,
          message: result.message || '推送成功',
          data: result.data
        }
      } else {
        throw new Error(result.message || result.error || '推送失败')
      }

    } catch (error) {
      console.error('[PushService] API推送失败:', error)
      throw new Error(`推送失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 验证商品数据完整性
   */
  private validateProductData(data: any): void {
    const requiredFields = [
      'productName',
      'categoryId',
      'shopId',
      'attributes',
      'variationListStr'
    ]

    for (const field of requiredFields) {
      if (!data[field]) {
        throw new Error(`缺少必要字段: ${field}`)
      }
    }

    // 验证JSON字段格式
    try {
      if (data.attributes) JSON.parse(data.attributes)
      if (data.variationListStr) JSON.parse(data.variationListStr)
      if (data.productWarehouseRouteReq) JSON.parse(data.productWarehouseRouteReq)
    } catch (error) {
      throw new Error('数据格式错误，包含无效的JSON字段')
    }

    console.info('[PushService] 数据验证通过')
  }

  /**
   * 从变体数据中提取extCode
   */
  private extractExtCodeFromVariations(variationListStr: string): string[] {
    try {
      const variations = JSON.parse(variationListStr || '[]')
      return variations.map((v: any) => v.extCode).filter((code: string) => code)
    } catch {
      return []
    }
  }

  /**
   * 获取推送状态
   */
  async getPushStatus(productId: string): Promise<any> {
    try {
      const response = await fetch(`https://www.dianxiaomi.com/api/product/status/${productId}`, {
        credentials: 'include'
      })

      if (response.ok) {
        return await response.json()
      }

      return null
    } catch (error) {
      console.warn('[PushService] 获取推送状态失败:', error)
      return null
    }
  }

  /**
   * 重试推送
   */
  async retryPush(dianxiaomiData: any, maxRetries: number = 3): Promise<PushResult> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.info(`[PushService] 推送重试 ${attempt}/${maxRetries}...`)
        
        const result = await this.pushSingleProduct(dianxiaomiData)
        
        if (result.success) {
          console.info(`[PushService] 重试成功，尝试次数: ${attempt}`)
          return result
        }
        
        lastError = new Error(result.error || '推送失败')
        
        // 重试间隔
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, attempt * 2000))
        }
        
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('推送失败')
        console.warn(`[PushService] 重试 ${attempt} 失败:`, error)
        
        // 重试间隔
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, attempt * 2000))
        }
      }
    }

    return {
      success: false,
      error: `推送失败，已重试${maxRetries}次: ${lastError?.message || '未知错误'}`
    }
  }
}

export default PushService.getInstance()
