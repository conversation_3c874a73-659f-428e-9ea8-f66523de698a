<template>
  <div class="product-detail-modal">
    <div class="product-header">
      <div class="product-image-section">
        <a-image
          :src="product.image"
          :alt="product.title"
          :width="120"
          :height="120"
          class="main-image"
        />
      </div>
      <div class="product-basic-info">
        <h3 class="product-title">{{ product.title }}</h3>
        <div class="product-meta">
          <div class="meta-item">
            <span class="label">SPU:</span>
            <span class="value">{{ product.spu }}</span>
          </div>
          <div class="meta-item">
            <span class="label">SKC:</span>
            <span class="value">{{ product.skc }}</span>
          </div>
          <div class="meta-item">
            <span class="label">站点:</span>
            <span class="value">{{ product.site }}</span>
          </div>
          <div class="meta-item">
            <span class="label">货号:</span>
            <span class="value">{{ product.sku.itemNo }}</span>
          </div>
          <div class="meta-item">
            <span class="label">状态:</span>
            <a-tag :color="getStatusColor(product.sku.selectStatus)">
              {{ product.sku.status }}
            </a-tag>
          </div>
        </div>
      </div>
    </div>

    <a-divider />

    <div class="price-analysis-section">
      <h4>价格分析</h4>
      <a-row :gutter="16">
        <a-col :span="12">
          <div class="price-card">
            <div class="price-card-header">
              <h5>申报价格信息</h5>
            </div>
            <div class="price-card-content">
              <div class="price-item">
                <span class="price-label">当前申报价:</span>
                <span class="price-value declared">{{ product.priceInfo.supplierPriceText }}</span>
              </div>
              <div class="price-item" v-if="product.priceInfo.officialDeclaredPrice">
                <span class="price-label">官方申报价:</span>
                <span class="price-value official">¥{{ product.priceInfo.officialDeclaredPrice }}</span>
              </div>
              <div class="price-item">
                <span class="price-label">申报次数:</span>
                <span class="price-value">{{ product.priceInfo.reviewTimes }}</span>
              </div>
              <div class="price-item">
                <span class="price-label">价格状态:</span>
                <a-tag :color="getPriceStatusColor(product.priceInfo.priceReviewStatus)">
                  {{ getPriceStatusText(product.priceInfo.priceReviewStatus) }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="price-card">
            <div class="price-card-header">
              <h5>Amazon成本信息</h5>
            </div>
            <div class="price-card-content">
              <AmazonPriceInfo
                :ext-code="product.sku.itemNo"
                :exchange-rate="7.2"
                :declared-price="product.priceInfo.supplierPriceText"
                :official-declared-price="product.priceInfo.officialDeclaredPrice"
                :price-order-id="product.sku.priceOrderId"
                @price-updated="handlePriceUpdated"
              />
            </div>
          </div>
        </a-col>
      </a-row>
    </div>

    <a-divider />

    <div class="profit-analysis-section" v-if="product.amazonPrice">
      <h4>毛利分析</h4>
      <div class="profit-cards">
        <div class="profit-card">
          <div class="profit-label">毛利金额</div>
          <div class="profit-value" :class="getProfitClass(product.profit)">
            ¥{{ product.profit?.toFixed(2) || '0.00' }}
          </div>
        </div>
        <div class="profit-card">
          <div class="profit-label">毛利率</div>
          <div class="profit-value" :class="getProfitClass(product.profitRate)">
            {{ product.profitRate?.toFixed(2) || '0.00' }}%
          </div>
        </div>
        <div class="profit-card">
          <div class="profit-label">成本价格</div>
          <div class="profit-value cost">
            ${{ product.amazonPrice.usdPrice }} (¥{{ (parseFloat(product.amazonPrice.usdPrice) * 7.2).toFixed(2) }})
          </div>
        </div>
      </div>
    </div>

    <a-divider />

    <div class="category-info-section">
      <h4>商品信息</h4>
      <a-descriptions bordered size="small" :column="2">
        <a-descriptions-item label="商品分类">
          {{ product.category }}
        </a-descriptions-item>
        <a-descriptions-item label="币种">
          {{ product.currency }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ product.createTime }}
        </a-descriptions-item>
        <a-descriptions-item label="SKU颜色" v-if="product.sku.color">
          {{ product.sku.color }}
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <div class="modal-actions">
      <a-space>
        <a-button @click="$emit('close')">
          关闭
        </a-button>
        <a-button type="primary" @click="confirmPrice">
          确认价格
        </a-button>
        <a-button @click="jumpToSource">
          查看货源
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { message } from 'ant-design-vue'
import AmazonPriceInfo from './AmazonPriceInfo.vue'

// Props
interface Props {
  product: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  priceConfirmed: [product: any]
  jumpToSource: [product: any]
}>()

// 方法
const handlePriceUpdated = (priceData: any) => {
  // 更新产品的Amazon价格信息
  props.product.amazonPrice = priceData
  
  // 重新计算毛利
  if (props.product.priceInfo.officialDeclaredPrice && priceData.usdPrice) {
    const cost = parseFloat(priceData.usdPrice) * 7.2
    const official = props.product.priceInfo.officialDeclaredPrice
    props.product.profit = official - cost
    props.product.profitRate = official > 0 ? (props.product.profit / official) * 100 : 0
  }
}

const confirmPrice = () => {
  emit('priceConfirmed', props.product)
  message.success('价格确认成功')
  emit('close')
}

const jumpToSource = () => {
  emit('jumpToSource', props.product)
  emit('close')
}

// 工具函数
const getStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    1: 'blue',
    7: 'orange',
    9: 'red',
    10: 'default',
    11: 'default',
    12: 'green',
    13: 'red'
  }
  return colorMap[status] || 'default'
}

const getPriceStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    0: 'default',
    1: 'processing',
    2: 'warning',
    3: 'error'
  }
  return colorMap[status] || 'default'
}

const getPriceStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    0: '待审核',
    1: '审核中',
    2: '待确认',
    3: '已拒绝'
  }
  return textMap[status] || '未知'
}

const getProfitClass = (value: number) => {
  if (!value) return ''
  if (value >= 30) return 'profit-high'
  if (value >= 15) return 'profit-medium'
  return 'profit-low'
}
</script>

<style>
.product-detail-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.product-header {
  display: flex;
  gap: var(--space-lg, 16px);
  margin-bottom: var(--space-lg, 16px);
}

.product-image-section {
  flex-shrink: 0;
}

.main-image {
  border-radius: var(--border-radius-md, 6px);
  border: 1px solid var(--border-secondary, #f0f0f0);
}

.product-basic-info {
  flex: 1;
}

.product-title {
  margin: 0 0 var(--space-md, 12px) 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #262626);
  line-height: 1.4;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm, 8px);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm, 8px);
}

.label {
  font-size: 12px;
  color: var(--text-secondary, #595959);
  min-width: 60px;
}

.value {
  font-size: 12px;
  color: var(--text-primary, #262626);
  font-weight: 500;
}

.price-analysis-section h4,
.profit-analysis-section h4,
.category-info-section h4 {
  margin: 0 0 var(--space-md, 12px) 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #262626);
}

.price-card {
  border: 1px solid var(--border-secondary, #f0f0f0);
  border-radius: var(--border-radius-md, 6px);
  overflow: hidden;
}

.price-card-header {
  background: var(--bg-tertiary, #f5f5f5);
  padding: var(--space-sm, 8px) var(--space-md, 12px);
  border-bottom: 1px solid var(--border-secondary, #f0f0f0);
}

.price-card-header h5 {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary, #262626);
}

.price-card-content {
  padding: var(--space-md, 12px);
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm, 8px);
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 12px;
  color: var(--text-secondary, #595959);
}

.price-value {
  font-size: 12px;
  font-weight: 500;
}

.price-value.declared {
  color: var(--ant-primary-color, #1677ff);
}

.price-value.official {
  color: var(--ant-success-color, #52c41a);
}

.price-value.cost {
  color: var(--text-primary, #262626);
}

.profit-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-md, 12px);
}

.profit-card {
  text-align: center;
  padding: var(--space-md, 12px);
  border: 1px solid var(--border-secondary, #f0f0f0);
  border-radius: var(--border-radius-md, 6px);
  background: var(--bg-primary, #ffffff);
}

.profit-label {
  font-size: 12px;
  color: var(--text-secondary, #595959);
  margin-bottom: var(--space-xs, 4px);
}

.profit-value {
  font-size: 16px;
  font-weight: 600;
}

.profit-high {
  color: var(--ant-success-color, #52c41a);
}

.profit-medium {
  color: var(--ant-warning-color, #faad14);
}

.profit-low {
  color: var(--ant-error-color, #ff4d4f);
}

.modal-actions {
  margin-top: var(--space-lg, 16px);
  text-align: right;
}
</style>
