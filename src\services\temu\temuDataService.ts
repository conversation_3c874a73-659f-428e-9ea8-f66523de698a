/**
 * Temu 数据获取服务
 * 
 * 仅跟Temu数据打交道，不要跟其他系统服务打交道
 *
 * 功能：
 * - 提供商品列表、待办事项等数据的获取和格式化
 * - 兼容现有接口，内部使用新的temu-api服务
 * - 处理数据格式化和状态映射
 *
 * 依赖关系：
 * - 依赖：temu-api.ts (API调用)
 * - 被依赖：dashboard组件, useDashboard组合式函数
 *
 * 迁移说明：
 * - 保持对外接口不变，确保现有代码无需修改
 * - 内部使用新的统一API服务
 */

import { temuApiService } from './temu-api'

// 待办事项数据接口
interface TodoCountData {
  success: boolean
  errorCode: number
  errorMsg: string | null
  result: {
    total: number
    todoStatusAggregationList: any[] | null
  }
}

// 商品列表数据接口
interface ProductListData {
  success: boolean
  errorCode: number
  errorMsg: string | null
  result: {
    total: number
    productSkcStatusAggregation: Array<{
      selectStatus: number
      count: number
    }>
    productIdNoStatusTotal: number
    recycleCount: number | null
    dataList: ProductItem[]
  }
}

// 商品项目接口
interface ProductItem {
  productId: number
  productName: string
  supplierPrice: string
  supplierPriceCurrencyType: string
  leafCategoryName: string
  fullCategoryName: string[]
  carouselImageUrlList: string[]
  productCreatedAt: number
  productUpdatedAt: number
  siteInfoList: Array<{
    siteId: number
    siteName: string
  }>
  skcList: Array<{
    skcId: number
    selectStatus: number
    supplierPrice: string
    extCode: string
    previewImgUrlList: string[]
    statusTime: {
      createdTime: number
      selectedTime: number | null
    }
    supplierPriceReviewInfoList?: Array<{
      priceOrderId: number
      times: number
      status: number
    }>
    skuList: Array<{
      skuId: number
      skuPreviewImage: string
      selectStatus: string
      priceReviewStatus?: number
      productPropertyList: Array<{
        name: string
        value: string
      }>
      extCode: string
      siteSupplierPriceList: Array<{
        siteId: number
        siteName: string
        supplierPrice: string
        supplierPriceValue: number
      }>
    }>
  }>
}

// 搜索参数接口
interface SearchParams {
  pageSize: number
  pageNum: number
  supplierTodoTypeList: number[]
  siteIds?: number[]
  productIds?: string[]
  skuCodes?: string[]
}

class TemuDataService {
  constructor() {
    console.info('[TemuDataService] 初始化服务（使用新的API架构）')
  }

  // 设置店铺ID（兼容性方法）
  setMallId(mallId: string): void {
    console.info('[TemuDataService] 设置店铺ID:', mallId)
  }

  // 获取待办事项数量（使用新的API服务）
  async getTodoCount(): Promise<TodoCountData> {
    console.info('[TemuDataService] 开始获取待办事项数量...')

    try {
      const response = await temuApiService.getTodoCount()

      if (response.success && response.data) {
        return response.data
      } else {
        throw new Error(response.error || '获取待办事项失败')
      }
    } catch (error) {
      console.error('[TemuDataService] 获取待办事项失败:', error)
      console.warn('[TemuDataService] 请确保Temu商家后台页面已打开并登录')

      // 返回默认数据，避免页面崩溃
      return {
        success: false,
        errorCode: 500,
        errorMsg: '获取待办事项失败，请确保Temu商家后台页面已打开并登录',
        result: {
          total: 0,
          todoStatusAggregationList: []
        }
      }
    }
  }

  // 获取商品列表（使用新的API服务）
  async getProductList(params: Partial<SearchParams> = {}): Promise<ProductListData> {
    console.info('[TemuDataService] 开始获取商品列表...', params)

    // 转换参数格式以适配新的API服务
    const apiParams = {
      pageSize: params.pageSize || 50,
      pageNum: params.pageNum || 1,  // 修复：API期望的是pageNum
      supplierTodoTypeList: params.supplierTodoTypeList || [],
      secondarySelectStatusList: (params as any).secondarySelectStatusList || [],
      priceReviewStatusList: (params as any).priceReviewStatusList || [],
      keyword: (params as any).keyword || ''
    }

    console.info('[TemuDataService] 转换后的API参数:', apiParams)

    try {
      console.info('[TemuDataService] 调用temuApiService.getProductList...')
      const response = await temuApiService.getProductList(apiParams)

      console.info('[TemuDataService] API响应:', {
        success: response.success,
        hasData: !!response.data,
        error: response.error,
        errorCode: response.errorCode
      })

      if (response.success && response.data) {
        console.info('[TemuDataService] API调用成功，返回数据结构:', {
          success: response.data.success,
          errorCode: response.data.errorCode,
          hasResult: !!response.data.result,
          hasDataList: !!response.data.result?.dataList,
          dataListLength: response.data.result?.dataList?.length || 0,
          total: response.data.result?.total || 0
        })
        return response.data
      } else {
        console.error('[TemuDataService] API调用失败:', response.error)
        throw new Error(response.error || '获取商品列表失败')
      }
    } catch (error) {
      console.error('[TemuDataService] 获取商品列表异常:', error)
      console.warn('[TemuDataService] 请确保Temu商家后台页面已打开并登录')

      // 返回默认数据，避免页面崩溃
      return {
        success: false,
        errorCode: 500,
        errorMsg: '获取商品列表失败，请确保Temu商家后台页面已打开并登录',
        result: {
          dataList: [],
          total: 0,
          productSkcStatusAggregation: [],
          productIdNoStatusTotal: 0,
          recycleCount: null
        }
      }
    }
  }





  // 获取价格确认商品列表（一次性获取所有数据）
  async getPriceConfirmationProductsWithOfficialPrices(params: any = {}): Promise<{
    success: boolean
    data: any[]
    total: number
    errorMsg?: string
  }> {
    try {
      console.info('[TemuDataService] 获取价格确认商品列表（一次性获取）')

      // 构建价格确认查询参数
      const productListParams = {
        ...params,
        secondarySelectStatusList: [7], // 价格待确认状态
        priceReviewStatusList: [0, 1, 2, 3]
      }

      const productListData = await this.getProductList(productListParams)

      if (!productListData.success || !productListData.result?.dataList) {
        return {
          success: false,
          data: [],
          total: 0,
          errorMsg: productListData.errorMsg || '获取商品列表失败'
        }
      }

      console.info('[TemuDataService] 商品数据获取成功，商品数量:', productListData.result.dataList.length)

      // 格式化数据（包含所有申报信息）
      const formattedData = this.formatProductData(productListData)

      console.info('[TemuDataService] 数据格式化完成，包含申报信息')

      // 异步获取官方申报价（不阻塞界面显示）
      this.asyncEnhanceWithOfficialPrices(formattedData, productListData.result.dataList)

      // 异步获取Amazon成本价（不阻塞界面显示）
      this.enhanceWithAmazonCosts(formattedData)

      return {
        success: true,
        data: formattedData,
        total: productListData.result.total
      }
    } catch (error) {
      console.error('[TemuDataService] 获取价格确认商品列表失败:', error)
      return {
        success: false,
        data: [],
        total: 0,
        errorMsg: error instanceof Error ? error.message : '获取失败'
      }
    }
  }

  /**
   * 获取官方申报价（支持单个或批量）
   * @param priceOrderIds 单个priceOrderId或priceOrderId数组
   * @returns 返回Map<priceOrderId, price>，单个查询时也返回Map格式
   */
  async getOfficialDeclaredPrices(priceOrderIds: number | number[]): Promise<Map<number, number>> {
    try {
      // 统一转换为数组格式
      const idsArray = Array.isArray(priceOrderIds) ? priceOrderIds : [priceOrderIds]

      if (idsArray.length === 0) {
        console.warn('[TemuDataService] priceOrderIds为空')
        return new Map()
      }

      console.info('[TemuDataService] 获取官方申报价:', idsArray.length, '个')

      // 直接调用基础API方法
      const response = await temuApiService.getOfficialPrices(idsArray)

      // 解析响应数据
      const result = new Map<number, number>()

      if (response.success && response.data?.success && response.data?.result) {
        const apiResult = response.data.result

        // 处理新的API响应格式：{ priceReviewItemList: [...] }
        if (apiResult.priceReviewItemList && Array.isArray(apiResult.priceReviewItemList)) {
          apiResult.priceReviewItemList.forEach((item: any) => {
            if (item?.id && typeof item.suggestSupplyPrice === 'number') {
              // id 对应 priceOrderId，suggestSupplyPrice 对应官方申报价
              // 官方申报价需要除以100才是实际值
              const actualPrice = item.suggestSupplyPrice / 100
              result.set(item.id, actualPrice)
              console.info('[TemuDataService] 解析官方申报价:', {
                priceOrderId: item.id,
                rawPrice: item.suggestSupplyPrice,
                actualPrice
              })
            }
          })
        }
        // 兼容旧的API响应格式：直接是数组
        else if (Array.isArray(apiResult)) {
          apiResult.forEach((item: any) => {
            if (item?.priceOrderId && typeof item.declaredPrice === 'number') {
              // 官方申报价需要除以100才是实际值
              const actualPrice = item.declaredPrice / 100
              result.set(item.priceOrderId, actualPrice)
              console.info('[TemuDataService] 解析官方申报价(旧格式):', {
                priceOrderId: item.priceOrderId,
                rawPrice: item.declaredPrice,
                actualPrice
              })
            }
          })
        }
      }

      console.info('[TemuDataService] 官方申报价获取完成，成功获取:', result.size, '个')
      return result
    } catch (error) {
      console.error('[TemuDataService] 获取官方申报价失败:', error)
      return new Map()
    }
  }

  // 异步增强官方申报价数据（不阻塞界面）
  private async asyncEnhanceWithOfficialPrices(formattedData: any[], originalData: any[]) {
    try {
      console.info('[TemuDataService] 开始异步获取官方申报价...')

      // 收集所有priceOrderId（去重）
      const priceOrderIdSet = new Set<number>()

      originalData.forEach(product => {
        product.skcList?.forEach(skc => {
          const priceReviewInfo = skc.supplierPriceReviewInfoList?.[0]
          if (priceReviewInfo?.priceOrderId) {
            priceOrderIdSet.add(priceReviewInfo.priceOrderId)
          }
        })
      })

      const priceOrderIds = Array.from(priceOrderIdSet)

      if (priceOrderIds.length === 0) {
        console.info('[TemuDataService] 没有需要获取官方申报价的商品')
        return
      }

      console.info('[TemuDataService] 收集到priceOrderId:', priceOrderIds.length, '个（去重后）')

      // 获取官方申报价（支持批量）
      const officialPricesMap = await this.getOfficialDeclaredPrices(priceOrderIds)

      console.info('[TemuDataService] 官方申报价获取完成，开始更新数据...')

      // 更新已格式化的数据
      formattedData.forEach(formattedProduct => {
        // 通过priceOrderId查找对应的官方申报价
        const priceOrderId = formattedProduct.sku?.priceOrderId

        if (priceOrderId && officialPricesMap.has(priceOrderId)) {
          const officialPrice = officialPricesMap.get(priceOrderId)

          if (officialPrice && officialPrice > 0) {
            // 更新priceInfo中的官方申报价
            if (formattedProduct.priceInfo) {
              formattedProduct.priceInfo.officialDeclaredPrice = officialPrice
            }
            // 更新sku中的官方申报价
            if (formattedProduct.sku) {
              formattedProduct.sku.officialDeclaredPrice = officialPrice
            }

            // 如果是多SKU商品，更新所有SKU的官方申报价
            // 注意：根据batch-suuuges.json，一个SKC下的所有SKU共享同一个官方申报价
            if (formattedProduct.skuList && formattedProduct.isMultiSku) {
              formattedProduct.skuList.forEach(skuInfo => {
                skuInfo.officialDeclaredPrice = officialPrice
              })
            }

            console.info('[TemuDataService] 更新商品官方申报价:', {
              title: formattedProduct.title,
              productId: formattedProduct.id,
              priceOrderId,
              officialPrice,
              isMultiSku: formattedProduct.isMultiSku,
              skuCount: formattedProduct.totalSkuCount
            })
          }
        } else {
          console.warn('[TemuDataService] 未找到官方申报价:', {
            title: formattedProduct.title,
            productId: formattedProduct.id,
            priceOrderId,
            hasPrice: priceOrderId ? officialPricesMap.has(priceOrderId) : false
          })
        }
      })

      console.info('[TemuDataService] 官方申报价数据更新完成')

      // 触发界面更新
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('priceConfirmationDataUpdated', {
          detail: { updatedData: formattedData }
        }))
      }

    } catch (error) {
      console.error('[TemuDataService] 异步获取官方申报价失败:', error)
    }
  }

  // 异步增强Amazon成本价数据（不阻塞界面）
  async enhanceWithAmazonCosts(formattedData: any[]): Promise<void> {
    try {
      console.info('[TemuDataService] 开始异步获取Amazon成本价...')

      // 收集所有Amazon商品的extCode
      const amazonProducts: Array<{
        productId: number
        skuId: number
        extCode: string
        asin: string
      }> = []

      formattedData.forEach(product => {
        if (product.skuList && product.isMultiSku) {
          // 多SKU商品
          product.skuList.forEach(sku => {
            if (sku.extCode && sku.extCode.includes('[am]')) {
              const asin = sku.extCode.split('[am]')[0]
              amazonProducts.push({
                productId: product.id,
                skuId: sku.skuId,
                extCode: sku.extCode,
                asin: asin
              })
            }
          })
        } else {
          // 单SKU商品
          const extCode = product.sku?.itemNo
          if (extCode && extCode.includes('[am]')) {
            const asin = extCode.split('[am]')[0]
            amazonProducts.push({
              productId: product.id,
              skuId: product.sku.productSkuId,
              extCode: extCode,
              asin: asin
            })
          }
        }
      })

      if (amazonProducts.length === 0) {
        console.info('[TemuDataService] 没有找到Amazon商品')
        return
      }

      console.info('[TemuDataService] 找到Amazon商品:', amazonProducts.length, '个')

      // 转换为按SKU维度的数据结构
      const amazonProductsBySku = amazonProducts.map(product => ({
        ...product,
        isMainSku: true // 这里可以根据需要调整
      }))

      // 通过amazonPriceService批量获取Amazon价格
      const amazonPriceService = (await import('../amazon/amazonPriceService')).AmazonPriceService.getInstance()
      const extCodes = amazonProducts.map(p => p.extCode)
      const amazonPricesMap = await amazonPriceService.batchGetAmazonPriceInfo(extCodes)

      console.info('[TemuDataService] Amazon价格获取完成，开始更新数据...')

      // 更新已格式化的数据（支持多SKU）
      formattedData.forEach(formattedProduct => {
        let hasAmazonPrice = false

        // 更新所有SKU的Amazon价格
        if (formattedProduct.skuList && formattedProduct.skuList.length > 0) {
          formattedProduct.skuList.forEach((skuInfo: any) => {
            if (skuInfo.extCode && amazonPricesMap.has(skuInfo.extCode)) {
              const amazonPriceInfo = amazonPricesMap.get(skuInfo.extCode)

              if (amazonPriceInfo && amazonPriceInfo.amazonPrice) {
                // 更新SKU的Amazon价格信息
                skuInfo.amazonPrice = {
                  usdPrice: amazonPriceInfo.amazonPrice.usdPrice,
                  stock: amazonPriceInfo.amazonPrice.stock,
                  costInCNY: parseFloat(amazonPriceInfo.amazonPrice.usdPrice) * 7.2,
                  lastUpdated: amazonPriceInfo.lastUpdated,
                  dataSource: amazonPriceInfo.dataSource
                }

                hasAmazonPrice = true

                console.info('[TemuDataService] 更新SKU Amazon价格:', {
                  productId: formattedProduct.id,
                  skuId: skuInfo.skuId,
                  extCode: skuInfo.extCode,
                  usdPrice: amazonPriceInfo.amazonPrice.usdPrice,
                  dataSource: amazonPriceInfo.dataSource
                })
              }
            }
          })
        }

        // 更新主要SKU的Amazon价格
        if (formattedProduct.sku?.itemNo && amazonPricesMap.has(formattedProduct.sku.itemNo)) {
          const amazonPriceInfo = amazonPricesMap.get(formattedProduct.sku.itemNo)

          if (amazonPriceInfo && amazonPriceInfo.amazonPrice) {
            formattedProduct.sku.amazonPrice = {
              usdPrice: amazonPriceInfo.amazonPrice.usdPrice,
              stock: amazonPriceInfo.amazonPrice.stock,
              costInCNY: parseFloat(amazonPriceInfo.amazonPrice.usdPrice) * 7.2,
              lastUpdated: amazonPriceInfo.lastUpdated,
              dataSource: amazonPriceInfo.dataSource
            }

            hasAmazonPrice = true
          }
        }

        // 更新产品级别的价格信息（向后兼容）
        if (hasAmazonPrice) {
          const amazonSku = formattedProduct.sku?.amazonPrice ||
                           formattedProduct.skuList?.find(sku => sku.amazonPrice)?.amazonPrice

          if (amazonSku) {
            if (!formattedProduct.priceInfo) {
              formattedProduct.priceInfo = {}
            }

            formattedProduct.priceInfo.usdPrice = amazonSku.usdPrice
            formattedProduct.priceInfo.stock = amazonSku.stock
            formattedProduct.priceInfo.costInCNY = amazonSku.costInCNY
            formattedProduct.priceInfo.lastUpdated = amazonSku.lastUpdated
            formattedProduct.priceInfo.dataSource = amazonSku.dataSource
          }
        }
      })

      console.info('[TemuDataService] Amazon成本价数据更新完成')

      // 触发界面更新
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('amazonCostDataUpdated', {
          detail: { updatedData: formattedData }
        }))
      }

    } catch (error) {
      console.error('[TemuDataService] 异步获取Amazon成本价失败:', error)
    }
  }











  // 格式化商品数据为显示格式（从supplier.json一次性提取所有信息）
  formatProductData(productList: ProductListData): any[] {
    console.info('[TemuDataService] 开始格式化商品数据（一次性提取所有信息）...', productList)

    if (!productList.result || !productList.result.dataList) {
      console.warn('[TemuDataService] 没有找到dataList数据')
      return []
    }

    console.info('[TemuDataService] 找到商品数据:', productList.result.dataList.length, '个商品')

    return productList.result.dataList.map((product, productIndex) => {
      console.info(`[TemuDataService] 处理第${productIndex + 1}个商品:`, product.productName)

      const firstSkc = product.skcList?.[0]
      if (!firstSkc) {
        console.warn(`[TemuDataService] 商品${productIndex + 1}没有SKC数据，跳过`)
        return null
      }

      // 提取SKC级别的申报信息（priceOrderId属于SKC，已经在数据中）
      const priceReviewInfo = firstSkc.supplierPriceReviewInfoList?.[0]
      const reviewTimes = priceReviewInfo?.times || 0
      const priceOrderId = priceReviewInfo?.priceOrderId || null
      const orderType = null // orderType字段不存在，设为null

      console.info(`[TemuDataService] SKC级别申报信息:`, {
        skcId: firstSkc.skcId,
        priceOrderId,
        reviewTimes,
        orderType,
        skuCount: firstSkc.skuList?.length || 0
      })

      // 处理SKU数据 - 保持商品作为一个整体，但包含所有SKU信息
      const skuList = firstSkc.skuList || []
      const firstSku = skuList[0]

      if (!firstSku) {
        console.warn(`[TemuDataService] 商品${productIndex + 1}没有SKU数据，跳过`)
        return null
      }

      // 使用第一个SKU的价格作为主要显示价格（已经在数据中）
      const siteSupplierPrice = firstSku.siteSupplierPriceList?.[0]
      const supplierPriceValue = siteSupplierPrice?.supplierPriceValue || 0
      const supplierPriceText = siteSupplierPrice?.supplierPrice || firstSkc.supplierPrice || product.supplierPrice || ''

      // 计算美元价格（假设汇率7.2）
      const exchangeRate = 7.2
      const usdPrice = supplierPriceValue ? (supplierPriceValue / 100 / exchangeRate) : 0

      // 注意：官方申报价需要通过API获取，这里先使用供应商价格作为默认值
      // 实际的官方申报价会通过异步方式获取并更新
      const defaultOfficialPrice = supplierPriceValue

      // 构建多SKU信息
      const skuInfoList = skuList.map(sku => {
        const skuSitePrice = sku.siteSupplierPriceList?.[0]
        const skuProperties = sku.productPropertyList?.map(p => `${p.name}: ${p.value}`).join(', ') || ''

        return {
          skuId: sku.skuId,
          extCode: sku.extCode,
          properties: skuProperties,
          price: skuSitePrice?.supplierPrice || '',
          priceValue: skuSitePrice?.supplierPriceValue || 0,
          image: sku.skuPreviewImage,
          priceReviewStatus: sku.priceReviewStatus || 0,
          selectStatus: sku.selectStatus
        }
      })

      const formattedProduct = {
        id: product.productId, // 使用商品ID作为主键
        image: firstSku.skuPreviewImage || product.carouselImageUrlList?.[0] || '',
        title: product.productName || '',
        spu: product.productId?.toString() || '',
        skc: firstSkc.skcId?.toString() || '',
        site: product.siteInfoList?.[0]?.siteName || '',
        currency: product.supplierPriceCurrencyType || '',
        category: product.fullCategoryName?.join('>') || '',
        declaredPrice: supplierPriceText,
        createTime: product.productCreatedAt ? new Date(product.productCreatedAt).toLocaleString('zh-CN') : '',

        // 扩展价格信息（包含申报信息）
        priceInfo: {
          supplierPriceValue: supplierPriceValue, // 分为单位的价格值
          supplierPriceText: supplierPriceText, // 格式化的价格文本
          usdPrice: usdPrice.toFixed(2), // 美元价格
          exchangeRate: exchangeRate,
          reviewTimes: reviewTimes, // 申报次数（已在数据中）
          priceReviewStatus: firstSku.priceReviewStatus || 0,
          officialDeclaredPrice: defaultOfficialPrice, // 默认使用供应商价格
          orderType: orderType, // 申报类型
          stock: 10 // 默认库存
        },

        // 主要SKU信息（用于显示）
        sku: {
          image: firstSku.skuPreviewImage || firstSkc.previewImgUrlList?.[0] || '',
          color: firstSku.productPropertyList?.find(p => p.name === '颜色')?.value || '',
          itemNo: firstSku.extCode || firstSkc.extCode || '',
          status: this.getStatusText(firstSkc.selectStatus),
          price: supplierPriceText,

          // 扩展SKU信息
          selectStatus: firstSkc.selectStatus || 0,
          priceReviewInfo: priceReviewInfo, // SKC级别的申报信息（已在数据中）
          siteSupplierPrice: siteSupplierPrice,

          // 重要：priceOrderId属于SKC级别，productSkuId属于SKU级别
          priceOrderId: priceOrderId, // SKC级别的priceOrderId（已在数据中）
          productSkuId: firstSku.skuId, // 主要SKU的ID
          skuId: firstSku.skuId, // 备用字段

          // 默认官方申报价
          officialDeclaredPrice: defaultOfficialPrice
        },

        // 多SKU信息
        skuList: skuInfoList, // 所有SKU的详细信息
        isMultiSku: skuList.length > 1,
        totalSkuCount: skuList.length,

        // 价格范围（用于多SKU显示）
        priceRange: skuList.length > 1 ? this.calculatePriceRange(skuList) : null,

        // 申报相关信息（已在数据中，无需额外API调用）
        priceReview: {
          priceOrderId: priceOrderId,
          times: reviewTimes,
          orderType: orderType,
          hasReviewInfo: !!priceReviewInfo
        }
      }

      console.info(`[TemuDataService] 格式化后的商品${productIndex + 1}:`, {
        id: formattedProduct.id,
        title: formattedProduct.title,
        priceOrderId,
        reviewTimes,
        skuCount: formattedProduct.totalSkuCount,
        isMultiSku: formattedProduct.isMultiSku,
        hasReviewInfo: formattedProduct.priceReview.hasReviewInfo
      })

      return formattedProduct
    }).filter(product => product !== null) // 过滤掉null值
  }

  // 计算多SKU价格范围
  private calculatePriceRange(skuList: any[]): { min: number; max: number; minText: string; maxText: string; range: string } | null {
    if (!skuList || skuList.length === 0) return null

    const prices = skuList
      .map(sku => sku.priceValue)
      .filter(price => price && price > 0)

    if (prices.length === 0) return null

    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    return {
      min: minPrice,
      max: maxPrice,
      minText: `${(minPrice / 100).toFixed(2)}¥`,
      maxText: `${(maxPrice / 100).toFixed(2)}¥`,
      range: minPrice === maxPrice
        ? `${(minPrice / 100).toFixed(2)}¥`
        : `${(minPrice / 100).toFixed(2)}~${(maxPrice / 100).toFixed(2)}¥`
    }
  }

  // 获取状态文本
  private getStatusText(status?: number): string {
    const statusMap: Record<number, string> = {
      1: '已选中',
      7: '价格申报中',
      9: '价格待确认',
      10: '未发布到站点',
      11: '待发布到站点',
      12: '已发布站点',
      13: '已下架'
    }

    const statusText = statusMap[status || 0] || `状态${status || 0}`
    console.info(`[TemuDataService] 状态映射: ${status} -> ${statusText}`)
    return statusText
  }

  // 获取状态统计数据
  formatStatusTabs(productList: ProductListData): any[] {
    console.info('[TemuDataService] 开始格式化状态标签页...', productList.result)

    if (!productList.result) {
      console.warn('[TemuDataService] 没有找到result数据')
      return [{ key: 'all', label: '全部', count: 0 }]
    }

    // 根据用户提供的状态映射关系
    const statusMap: Record<number, { key: string; label: string }> = {
      7: { key: 'price-confirming', label: '价格待确认' }, // 价格确认中，但在界面上显示为"价格待确认"
      10: { key: 'unpublished', label: '未发布到站点' },
      11: { key: 'unpublished', label: '未发布到站点' }, // 11也归类到未发布
      12: { key: 'published', label: '已发布站点' },
      13: { key: 'offline', label: '已下架' }
      // 注意：状态9（价格已作废）不显示在标签页中
      // 状态1（已选中）也不在主要标签页中显示
    }

    // 初始化标签页，保持用户界面显示的顺序
    const tabs = [
      { key: 'all', label: '全部', count: productList.result.total || 0 },
      { key: 'price-confirming', label: '价格待确认', count: 0 },
      { key: 'unpublished', label: '未发布到站点', count: 0 },
      { key: 'published', label: '已发布站点', count: 0 },
      { key: 'offline', label: '已下架', count: 0 }
    ]

    if (productList.result.productSkcStatusAggregation) {
      console.info('[TemuDataService] 状态统计数据:', productList.result.productSkcStatusAggregation)

      // 创建一个映射来累计计数
      const countMap: Record<string, number> = {}

      productList.result.productSkcStatusAggregation.forEach(item => {
        const statusInfo = statusMap[item.selectStatus]
        if (statusInfo) {
          // 累计相同key的计数（比如状态10和11都归类到unpublished）
          if (!countMap[statusInfo.key]) {
            countMap[statusInfo.key] = 0
          }
          countMap[statusInfo.key] += item.count
          console.info(`[TemuDataService] 状态${item.selectStatus}(${item.count}) -> ${statusInfo.key}`)
        } else {
          console.warn('[TemuDataService] 忽略状态:', item.selectStatus, '计数:', item.count)
        }
      })

      // 更新标签页的计数
      tabs.forEach(tab => {
        if (countMap[tab.key]) {
          tab.count = countMap[tab.key]
        }
      })
    } else {
      console.warn('[TemuDataService] 没有找到productSkcStatusAggregation数据')
    }

    console.info('[TemuDataService] 格式化后的标签页:', tabs)
    return tabs
  }


}

// 创建单例实例
export default new TemuDataService();
export type { TodoCountData, ProductListData, ProductItem, SearchParams }
