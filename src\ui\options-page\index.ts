import { i18n } from "src/utils/i18n"
import { pinia } from "src/utils/pinia"
import { appRouter } from "src/utils/router"
import { createApp } from "vue"
import Antd from 'ant-design-vue'
import App from "./app.vue"

import "./index.css"

appRouter.addRoute({
  path: "/",
  redirect: "/options-page",
})

const app = createApp(App).use(i18n).use(Antd).use(pinia).use(appRouter)

app.mount("#app")

export default app

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}
