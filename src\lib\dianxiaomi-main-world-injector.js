// ================================================================
//  店小秘MAIN World注入器 (dianxiaomi-main-world-injector.js)
//  此文件将通过动态注入的方式直接注入到页面主环境
// ================================================================

(function() {
  // 防止重复注入
  if (window.hasDxmMainInjector) {
    return;
  }
  window.hasDxmMainInjector = true;

  console.info('[DXM MAIN] ===== Main World Injector Loaded (Static Version) =====');
  console.info('[DXM MAIN] Current URL:', window.location.href);
  console.info('[DXM MAIN] Document ready state:', document.readyState);
  console.info('[DXM MAIN] Window object available:', !!window);

  // 设置全局标记
  window.__DXM_MAIN_WORLD_LOADED__ = true;
  console.info('[DXM MAIN] ===== 设置全局标记 __DXM_MAIN_WORLD_LOADED__ = true =====');

  // 验证标记是否设置成功
  console.info('[DXM MAIN] 验证全局标记:', window.__DXM_MAIN_WORLD_LOADED__);

  // 监听来自 ISOLATED World 的命令
  window.addEventListener('dxm-request-data', function(event) {
    console.info('[DXM MAIN] Received data request from content script:', event.detail);
    const pushtype = event.detail.pushtype;
    let result = null;
    let success = false;
    let errorMsg = '未找到可用的系统函数。';

    try {
      // 根据 pushtype 调用不同的原生函数
      switch (pushtype) {
        case 'popTemuProduct':
        case 'TemuPodProduct':
          if (window.POP_TEMU_PRODUCT_FN && window.POP_TEMU_PRODUCT_FN.saveFn) {
            result = window.POP_TEMU_PRODUCT_FN.saveFn.getSubmitData();
            success = true;
            console.info('[DXM MAIN] POP_TEMU_PRODUCT_FN.saveFn.getSubmitData() called successfully');
          } else {
            errorMsg = 'window.POP_TEMU_PRODUCT_FN 不可用';
            console.warn('[DXM MAIN] POP_TEMU_PRODUCT_FN not available:', {
              POP_TEMU_PRODUCT_FN: !!window.POP_TEMU_PRODUCT_FN,
              saveFn: !!(window.POP_TEMU_PRODUCT_FN && window.POP_TEMU_PRODUCT_FN.saveFn)
            });
          }
          break;

        case 'tikTokProductPod':
          if (window.TIKTOK_PRODUCT_FN && window.TIKTOK_PRODUCT_FN.saveFn) {
            result = window.TIKTOK_PRODUCT_FN.saveFn.getSubmitData();
            success = true;
            console.info('[DXM MAIN] TIKTOK_PRODUCT_FN.saveFn.getSubmitData() called successfully');
          } else {
            errorMsg = 'window.TIKTOK_PRODUCT_FN 不可用';
          }
          break;

        case 'localTemu':
          if (window.LOCAL_TEMU_PRODUCT_FN && window.LOCAL_TEMU_PRODUCT_FN.saveFn) {
            result = window.LOCAL_TEMU_PRODUCT_FN.saveFn.getSubmitData();
            success = true;
            console.info('[DXM MAIN] LOCAL_TEMU_PRODUCT_FN.saveFn.getSubmitData() called successfully');
          } else {
            errorMsg = 'window.LOCAL_TEMU_PRODUCT_FN 不可用';
          }
          break;

        case 'sheinProduct':
          if (window.SHEIN_PRODUCT_FN && window.SHEIN_PRODUCT_FN.saveFn) {
            result = window.SHEIN_PRODUCT_FN.saveFn.getSubmitSkcData();
            success = true;
            console.info('[DXM MAIN] SHEIN_PRODUCT_FN.saveFn.getSubmitSkcData() called successfully');
          } else {
            errorMsg = 'window.SHEIN_PRODUCT_FN 不可用';
          }
          break;

        case 'smtlocalProduct':
          if (window.SMTLOCAL_PRODUCT_FN && window.SMTLOCAL_PRODUCT_FN.saveFn) {
            result = window.SMTLOCAL_PRODUCT_FN.saveFn.getSubmitPropData(false);
            success = true;
            console.info('[DXM MAIN] SMTLOCAL_PRODUCT_FN.saveFn.getSubmitPropData() called successfully');
          } else {
            errorMsg = 'window.SMTLOCAL_PRODUCT_FN 不可用';
          }
          break;

        default:
          // 默认尝试 Temu
          if (window.POP_TEMU_PRODUCT_FN && window.POP_TEMU_PRODUCT_FN.saveFn) {
            result = window.POP_TEMU_PRODUCT_FN.saveFn.getSubmitData();
            success = true;
            console.info('[DXM MAIN] Default POP_TEMU_PRODUCT_FN.saveFn.getSubmitData() called successfully');
          } else {
            errorMsg = '未知的推送类型: ' + pushtype + '，且默认函数不可用';
          }
          break;
      }

      if (success) {
        console.info('[DXM MAIN] Data retrieved successfully:', result);

        // 提取分类信息
        const categoryInfo = extractCategoryInfo();

        // 重新组织数据结构
        const organizedData = organizeDataStructure(result, categoryInfo);

        console.info('[DXM MAIN] 数据重新组织完成:', organizedData);

        if (categoryInfo) {
          // 写入店小秘本地缓存
          saveToDianXiaoMiCache(categoryInfo, organizedData);
        }

        // 使用重新组织的数据
        result = organizedData;

        // 将获取的数据存入 localStorage，作为一种备用通信方式
        try {
          localStorage.setItem('submitDataResult', JSON.stringify(result));
        } catch (e) {
          console.warn('[DXM MAIN] Failed to store result in localStorage:', e);
        }

        // 发送成功事件回 ISOLATED World
        window.dispatchEvent(new CustomEvent('dxm-data-response', {
          detail: { success: true, data: result, pushtype: pushtype }
        }));
      } else {
        throw new Error(errorMsg);
      }
    } catch (e) {
      console.error('[DXM MAIN] Error retrieving data:', e);
      window.dispatchEvent(new CustomEvent('dxm-data-response', {
        detail: { success: false, error: e.message, pushtype: pushtype }
      }));
    }
  });

  // 数据结构重新组织函数
  function organizeDataStructure(originalData, categoryInfo) {
    try {
      console.info('[DXM MAIN] 开始重新组织数据结构...');

      // 分离属性数据和其他数据
      const attributes = [];
      const otherData = {};

      // 遍历原始数据，分离属性和非属性数据
      Object.keys(originalData).forEach(key => {
        const item = originalData[key];

        // 检查是否为属性数据（包含 propName 字段的对象）
        if (item && typeof item === 'object' && item.propName && !isNaN(key)) {
          attributes.push(item);
        } else {
          // 非属性数据保留在根级别
          otherData[key] = item;
        }
      });

      // 构建新的数据结构
      const organizedData = {
        ...otherData,  // 保留所有非属性数据
        attributes: attributes,  // 属性数据作为数组
        categoryInfo: categoryInfo || null // 分类信息作为独立字段
      };

      console.info('[DXM MAIN] 数据重新组织完成:', {
        attributesCount: attributes.length,
        hasCategoryInfo: !!categoryInfo,
        otherDataKeys: Object.keys(otherData)
      });

      return organizedData;

    } catch (error) {
      console.error('[DXM MAIN] 数据结构重新组织失败:', error);
      return originalData;  // 失败时返回原始数据
    }
  }

  // 分类信息提取函数
  function extractCategoryInfo() {
    try {
      console.info('[DXM MAIN] 开始提取分类信息...');

      const categoryContainer = document.querySelector('span.category.productCategory');
      if (!categoryContainer) {
        console.warn('[DXM MAIN] 未找到分类容器元素');
        return null;
      }

      const categorySpans = categoryContainer.querySelectorAll('span[name="categoryId"]');
      if (!categorySpans.length) {
        console.warn('[DXM MAIN] 未找到分类span元素');
        return null;
      }

      const categoryPath = [];
      let finalCategoryId = null;
      let categoryNames = [];

      // 遍历所有分类级别
      categorySpans.forEach(span => {
        const level = parseInt(span.getAttribute('data-level'));
        const innerSpan = span.querySelector('span[id]');

        if (innerSpan && innerSpan.id) {
          const categoryId = innerSpan.id;
          let categoryName = innerSpan.textContent || innerSpan.innerText || '';

          // 清理分类名称，移除 &nbsp;&gt;&nbsp; 等符号
          categoryName = categoryName
            .replace(/&nbsp;/g, ' ')
            .replace(/>/g, '')
            .replace(/\s+/g, ' ')
            .trim();

          if (categoryName && categoryId) {
            categoryPath.push({
              level: level,
              id: categoryId,
              name: categoryName
            });

            categoryNames.push(categoryName);
            finalCategoryId = parseInt(categoryId);
          }
        }
      });

      if (categoryPath.length === 0) {
        console.warn('[DXM MAIN] 未提取到有效的分类信息');
        return null;
      }

      const categoryInfo = {
        categoryId: finalCategoryId,
        categoryName: categoryNames.join(' > '),
        categoryPath: categoryPath
      };

      console.info('[DXM MAIN] 分类信息提取成功:', categoryInfo);
      return categoryInfo;

    } catch (error) {
      console.error('[DXM MAIN] 提取分类信息失败:', error);
      return null;
    }
  }



  // 保存到店小秘本地缓存
  function saveToDianXiaoMiCache(categoryInfo, fullData) {
    try {
      console.info('[DXM MAIN] 开始保存到店小秘本地缓存...');

      // 保存分类信息到店小秘缓存
      const dxmCategoryKey = 'dxm_category_info';
      const dxmConfigKey = 'dxm_product_config';

      // 保存分类信息
      localStorage.setItem(dxmCategoryKey, JSON.stringify(categoryInfo));
      console.info('[DXM MAIN] 分类信息已保存到店小秘缓存:', dxmCategoryKey);

      // 保存完整配置数据
      localStorage.setItem(dxmConfigKey, JSON.stringify(fullData));
      console.info('[DXM MAIN] 完整配置数据已保存到店小秘缓存:', dxmConfigKey);

      // 同时保存到原有的 submitDataResult 键
      localStorage.setItem('submitDataResult', JSON.stringify(fullData));

      console.info('[DXM MAIN] 店小秘本地缓存保存完成');

    } catch (error) {
      console.error('[DXM MAIN] 保存到店小秘本地缓存失败:', error);
    }
  }

  // 调试信息
  console.info('[DXM MAIN] Available window functions:', {
    POP_TEMU_PRODUCT_FN: !!window.POP_TEMU_PRODUCT_FN,
    TIKTOK_PRODUCT_FN: !!window.TIKTOK_PRODUCT_FN,
    LOCAL_TEMU_PRODUCT_FN: !!window.LOCAL_TEMU_PRODUCT_FN,
    SHEIN_PRODUCT_FN: !!window.SHEIN_PRODUCT_FN,
    SMTLOCAL_PRODUCT_FN: !!window.SMTLOCAL_PRODUCT_FN
  });

  console.info('[DXM MAIN] ✅ 所有事件监听器设置完毕，正在监听 dxm-request-data 事件...');
})();