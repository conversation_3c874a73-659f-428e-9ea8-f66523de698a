/**
 * 图片处理服务
 * 负责Amazon图片的下载、压缩、格式转换和上传到店小秘
 */

export interface ImageProcessingResult {
  success: boolean
  originalUrl: string
  processedUrl?: string
  error?: string
  size?: {
    width: number
    height: number
    fileSize: number
  }
}

export interface BatchProcessingProgress {
  total: number
  completed: number
  failed: number
  currentImage: string
}

export class ImageProcessingService {
  private static instance: ImageProcessingService
  private readonly TARGET_SIZE = 800
  private readonly TARGET_FORMAT = 'image/jpeg'
  private readonly QUALITY = 0.85
  private readonly MAX_FILE_SIZE = 2 * 1024 * 1024 // 2MB

  static getInstance(): ImageProcessingService {
    if (!ImageProcessingService.instance) {
      ImageProcessingService.instance = new ImageProcessingService()
    }
    return ImageProcessingService.instance
  }

  /**
   * 批量处理Amazon图片
   */
  async batchProcessImages(
    imageUrls: string[],
    onProgress?: (progress: BatchProcessingProgress) => void
  ): Promise<ImageProcessingResult[]> {
    console.info('[ImageProcessingService] 开始批量处理图片:', imageUrls.length, '张')

    const results: ImageProcessingResult[] = []
    let completed = 0
    let failed = 0

    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i]
      
      // 更新进度
      onProgress?.({
        total: imageUrls.length,
        completed,
        failed,
        currentImage: imageUrl
      })

      try {
        const result = await this.processImage(imageUrl)
        results.push(result)
        
        if (result.success) {
          completed++
        } else {
          failed++
        }
      } catch (error) {
        console.error('[ImageProcessingService] 处理图片失败:', imageUrl, error)
        results.push({
          success: false,
          originalUrl: imageUrl,
          error: error instanceof Error ? error.message : '处理失败'
        })
        failed++
      }

      // 添加延迟避免请求过快
      if (i < imageUrls.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    // 最终进度更新
    onProgress?.({
      total: imageUrls.length,
      completed,
      failed,
      currentImage: ''
    })

    console.info('[ImageProcessingService] 批量处理完成:', {
      total: imageUrls.length,
      completed,
      failed
    })

    return results
  }

  /**
   * 处理单张图片
   */
  async processImage(imageUrl: string): Promise<ImageProcessingResult> {
    try {
      console.info('[ImageProcessingService] 开始处理图片:', imageUrl)

      // 1. 下载图片
      const imageBlob = await this.downloadImage(imageUrl)
      
      // 2. 压缩并调整尺寸
      const processedBlob = await this.resizeAndCompressImage(imageBlob)
      
      // 3. 上传到店小秘
      const uploadedUrl = await this.uploadToDianxiaomi(processedBlob)

      const result: ImageProcessingResult = {
        success: true,
        originalUrl: imageUrl,
        processedUrl: uploadedUrl,
        size: {
          width: this.TARGET_SIZE,
          height: this.TARGET_SIZE,
          fileSize: processedBlob.size
        }
      }

      console.info('[ImageProcessingService] 图片处理成功:', result)
      return result

    } catch (error) {
      console.error('[ImageProcessingService] 图片处理失败:', imageUrl, error)
      return {
        success: false,
        originalUrl: imageUrl,
        error: error instanceof Error ? error.message : '处理失败'
      }
    }
  }

  /**
   * 下载Amazon图片
   */
  private async downloadImage(imageUrl: string): Promise<Blob> {
    try {
      // 清理Amazon图片URL，移除尺寸限制参数
      const cleanUrl = this.cleanAmazonImageUrl(imageUrl)
      console.info('[ImageProcessingService] 下载图片:', cleanUrl)

      // 通过background script下载图片以避免CORS问题
      const response = await chrome.runtime.sendMessage({
        action: 'DOWNLOAD_IMAGE',
        imageUrl: cleanUrl
      })

      if (!response.success) {
        throw new Error(response.error || '图片下载失败')
      }

      // 将base64转换为Blob
      const base64Data = response.data.split(',')[1]
      const binaryData = atob(base64Data)
      const bytes = new Uint8Array(binaryData.length)
      
      for (let i = 0; i < binaryData.length; i++) {
        bytes[i] = binaryData.charCodeAt(i)
      }

      return new Blob([bytes], { type: 'image/jpeg' })

    } catch (error) {
      console.error('[ImageProcessingService] 图片下载失败:', imageUrl, error)
      throw new Error(`图片下载失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 压缩并调整图片尺寸为800x800 JPG格式
   */
  private async resizeAndCompressImage(imageBlob: Blob): Promise<Blob> {
    return new Promise((resolve, reject) => {
      try {
        const img = new Image()
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) {
          throw new Error('无法创建Canvas上下文')
        }

        img.onload = () => {
          try {
            // 设置目标尺寸
            canvas.width = this.TARGET_SIZE
            canvas.height = this.TARGET_SIZE

            // 计算缩放比例，保持宽高比
            const scale = Math.min(
              this.TARGET_SIZE / img.width,
              this.TARGET_SIZE / img.height
            )

            const scaledWidth = img.width * scale
            const scaledHeight = img.height * scale

            // 计算居中位置
            const x = (this.TARGET_SIZE - scaledWidth) / 2
            const y = (this.TARGET_SIZE - scaledHeight) / 2

            // 填充白色背景
            ctx.fillStyle = '#FFFFFF'
            ctx.fillRect(0, 0, this.TARGET_SIZE, this.TARGET_SIZE)

            // 绘制缩放后的图片
            ctx.drawImage(img, x, y, scaledWidth, scaledHeight)

            // 转换为Blob
            canvas.toBlob(
              (blob) => {
                if (blob) {
                  console.info('[ImageProcessingService] 图片压缩完成:', {
                    originalSize: imageBlob.size,
                    compressedSize: blob.size,
                    compressionRatio: Math.round((1 - blob.size / imageBlob.size) * 100)
                  })
                  resolve(blob)
                } else {
                  reject(new Error('图片压缩失败'))
                }
              },
              this.TARGET_FORMAT,
              this.QUALITY
            )
          } catch (error) {
            reject(error)
          }
        }

        img.onerror = () => {
          reject(new Error('图片加载失败'))
        }

        // 创建图片URL
        const imageUrl = URL.createObjectURL(imageBlob)
        img.src = imageUrl

        // 清理URL
        img.onload = () => {
          URL.revokeObjectURL(imageUrl)
          img.onload()
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 上传图片到店小秘
   */
  private async uploadToDianxiaomi(imageBlob: Blob): Promise<string> {
    try {
      console.info('[ImageProcessingService] 开始上传图片到店小秘...')

      // 将Blob转换为base64
      const base64Data = await this.blobToBase64(imageBlob)

      // 通过background script上传图片
      const response = await chrome.runtime.sendMessage({
        action: 'UPLOAD_IMAGE_TO_DIANXIAOMI',
        imageData: base64Data,
        fileName: `amazon_image_${Date.now()}.jpg`
      })

      if (!response.success) {
        throw new Error(response.error || '图片上传失败')
      }

      console.info('[ImageProcessingService] 图片上传成功:', response.imageUrl)
      return response.imageUrl

    } catch (error) {
      console.error('[ImageProcessingService] 图片上传失败:', error)
      throw new Error(`图片上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 清理Amazon图片URL
   */
  private cleanAmazonImageUrl(imageUrl: string): string {
    try {
      const url = new URL(imageUrl)
      
      // 移除Amazon图片的尺寸限制参数，获取原始大图
      const cleanPath = url.pathname.replace(/\._[A-Z0-9_,]+_\./, '.')
      
      return `${url.protocol}//${url.host}${cleanPath}`
    } catch (error) {
      console.warn('[ImageProcessingService] URL清理失败，使用原始URL:', imageUrl)
      return imageUrl
    }
  }

  /**
   * 将Blob转换为base64
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result)
        } else {
          reject(new Error('转换失败'))
        }
      }
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 验证图片URL是否有效
   */
  async validateImageUrl(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(imageUrl, { method: 'HEAD' })
      return response.ok && response.headers.get('content-type')?.startsWith('image/')
    } catch {
      return false
    }
  }

  /**
   * 获取图片信息
   */
  async getImageInfo(imageUrl: string): Promise<{ width: number; height: number; size: number } | null> {
    try {
      const imageBlob = await this.downloadImage(imageUrl)
      
      return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => {
          resolve({
            width: img.width,
            height: img.height,
            size: imageBlob.size
          })
        }
        img.onerror = () => resolve(null)
        img.src = URL.createObjectURL(imageBlob)
      })
    } catch {
      return null
    }
  }
}

export default ImageProcessingService.getInstance()
