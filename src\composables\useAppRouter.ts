// 应用路由管理器
// 根据登录状态和绑定状态决定显示哪个页面

import { ref, computed, readonly } from 'vue'
import { temuApiService } from '../services/temu/temu-api'
import { useNotification } from './useNotification'

// 页面状态枚举
export enum AppPageState {
  LOADING = 'loading',           // 加载中
  TEMU_NOT_FOUND = 'temu-not-found', // Temu 未登录
  BINDING_REQUIRED = 'binding-required', // 需要绑定
  DASHBOARD = 'dashboard'        // 主页面
}

// 应用状态
interface AppState {
  pageState: AppPageState
  isLoading: boolean
  temuLoginStatus: {
    isLoggedIn: boolean
    userInfo?: any
    shopInfo?: any
    error?: string
  }
  backendBindingStatus: {
    isBound: boolean
    shopInfo?: any
    error?: string
  }
  error?: string
}

// 全局应用状态
const appState = ref<AppState>({
  pageState: AppPageState.LOADING,
  isLoading: true,
  temuLoginStatus: {
    isLoggedIn: false
  },
  backendBindingStatus: {
    isBound: false
  }
})

export function useAppRouter() {
  const { success, error: showError, warning, info } = useNotification()

  // 计算属性
  const currentPageState = computed(() => appState.value.pageState)
  const isLoading = computed(() => appState.value.isLoading)
  const temuLoginStatus = computed(() => appState.value.temuLoginStatus)
  const backendBindingStatus = computed(() => appState.value.backendBindingStatus)

  // 检测应用状态
  const checkAppState = async (): Promise<void> => {
    try {
      console.log('[useAppRouter] 开始检测应用状态...')
      appState.value.isLoading = true
      appState.value.pageState = AppPageState.LOADING

      // 第一步：检测 Temu 登录状态
      info('检测中', '正在检测 Temu 登录状态...')
      const response = await temuApiService.checkLoginStatus()
      const temuStatus = response.success ? response.data : { isLoggedIn: false, error: response.error }
      appState.value.temuLoginStatus = temuStatus

      if (!temuStatus.isLoggedIn) {
        console.info('[useAppRouter] Temu 未登录，显示登录页面')
        appState.value.pageState = AppPageState.TEMU_NOT_FOUND
        appState.value.isLoading = false
        warning('未登录', temuStatus.message || '请先登录 Temu 商家后台')
        return
      }

      // 第二步：检测后台绑定状态
      if (temuStatus.userInfo?.userId) {
        console.info('[useAppRouter] Temu 已登录，检测后台绑定状态...')
        info('检测中', '正在检测后台绑定状态...')

        try {
          // 动态导入 useShopBinding 以避免循环依赖
          const { useShopBinding } = await import('./useShopBinding')
          const { checkBackendBindingStatus } = useShopBinding()

          const bindingResult = await checkBackendBindingStatus()

          if (bindingResult.success && bindingResult.data?.isTemuBound) {
            // 后台已绑定，直接进入主页面
            console.info('[useAppRouter] 后台已绑定，进入主页面')
            appState.value.backendBindingStatus = {
              isBound: true,
              shopInfo: bindingResult.data.temuSiteInfo
            }
            appState.value.pageState = AppPageState.DASHBOARD
            appState.value.isLoading = false
            success('已绑定', '检测到店铺已绑定，进入工作台')
            return
          } else {
            // 后台未绑定，进入绑定页面
            console.info('[useAppRouter] 后台未绑定，进入绑定页面')
            appState.value.backendBindingStatus = {
              isBound: false,
              error: bindingResult.message
            }
            appState.value.pageState = AppPageState.BINDING_REQUIRED
            appState.value.isLoading = false
            info('需要绑定', '检测到 Temu 已登录，但需要绑定店铺到后台')
            return
          }
        } catch (bindingError) {
          console.warn('[useAppRouter] 检测后台绑定状态失败，进入绑定页面:', bindingError)
          // 如果检测绑定状态失败，默认进入绑定页面
          appState.value.backendBindingStatus = {
            isBound: false,
            error: '检测绑定状态失败'
          }
          appState.value.pageState = AppPageState.BINDING_REQUIRED
          appState.value.isLoading = false
          warning('检测失败', '无法检测绑定状态，请手动绑定店铺')
          return
        }
      } else {
        throw new Error('无法获取用户ID')
      }
    } catch (error) {
      console.error('[useAppRouter] 检测应用状态失败:', error)
      appState.value.error = error instanceof Error ? error.message : '检测失败'
      appState.value.pageState = AppPageState.TEMU_NOT_FOUND
      appState.value.isLoading = false
      showError('检测失败', '无法检测应用状态，请重试')
    }
  }

  // 重新检测状态
  const recheckAppState = async (): Promise<void> => {
    console.log('[useAppRouter] 重新检测应用状态...')
    await checkAppState()
  }

  // 手动设置页面状态
  const setPageState = (state: AppPageState): void => {
    console.log('[useAppRouter] 手动设置页面状态:', state)
    appState.value.pageState = state
  }

  // 处理绑定成功
  const handleBindingSuccess = async (): Promise<void> => {
    console.log('[useAppRouter] 绑定成功，直接跳转到主页面...')
    success('绑定成功', '店铺绑定成功，正在跳转到主页面...')

    // 更新绑定状态
    appState.value.backendBindingStatus = {
      isBound: true
    }

    // 直接跳转到主页面，不需要重新检测
    appState.value.pageState = AppPageState.DASHBOARD
    appState.value.isLoading = false

    console.info('[useAppRouter] 已跳转到主页面')
  }

  // 处理登录成功
  const handleLoginSuccess = async (): Promise<void> => {
    console.log('[useAppRouter] 登录成功，重新检测状态...')
    success('登录成功', '检测到 Temu 登录成功，正在检测绑定状态...')
    
    // 延迟一下再检测，确保登录状态已稳定
    setTimeout(async () => {
      await recheckAppState()
    }, 1000)
  }

  // 跳转到 Temu 登录页面
  const goToTemuLogin = (): void => {
    console.info('[useAppRouter] 跳转到 Temu 登录页面')
    window.open('https://seller.kuajingmaihuo.com/', '_blank')
    info('跳转中', '正在打开 Temu 商家后台，请完成登录后返回')
  }

  // 强制进入绑定页面
  const forceBindingPage = (): void => {
    console.log('[useAppRouter] 强制进入绑定页面')
    appState.value.pageState = AppPageState.BINDING_REQUIRED
  }

  // 强制进入主页面
  const forceDashboard = (): void => {
    console.log('[useAppRouter] 强制进入主页面')
    appState.value.pageState = AppPageState.DASHBOARD
  }

  // 获取当前状态描述
  const getStateDescription = (): string => {
    switch (appState.value.pageState) {
      case AppPageState.LOADING:
        return '正在检测登录和绑定状态...'
      case AppPageState.TEMU_NOT_FOUND:
        return 'Temu 未登录，请先登录'
      case AppPageState.BINDING_REQUIRED:
        return 'Temu 已登录，但需要绑定店铺'
      case AppPageState.DASHBOARD:
        return '已登录并绑定，进入主页面'
      default:
        return '未知状态'
    }
  }

  return {
    // 状态
    currentPageState,
    isLoading,
    temuLoginStatus,
    backendBindingStatus,
    appState: readonly(appState),

    // 方法
    checkAppState,
    recheckAppState,
    setPageState,
    handleBindingSuccess,
    handleLoginSuccess,
    goToTemuLogin,
    forceBindingPage,
    forceDashboard,
    getStateDescription,

    // 枚举
    AppPageState
  }
}
