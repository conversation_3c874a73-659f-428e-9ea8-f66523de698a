// 店小秘Token检测服务
// 统一的token检测和管理服务，提供可靠的身份验证机制

import { ref, computed } from 'vue'
import { dianxiaomiTokenCache, saveTokenToCache } from './dianxiaomiTokenCache'

// Token状态接口
export interface TokenStatus {
  isValid: boolean
  isLoggedIn: boolean
  message: string
  userInfo?: {
    id: number
    username?: string
    name?: string
    phone?: string
    account?: string
    accountAlias?: string
    puid?: number
    vipLevel?: number
    vipExpired?: number
    state?: number
    wechatBind?: boolean
  }
  shopCount?: number
  lastCheckTime: number
  error?: string
}

// Cookie信息接口
interface CookieInfo {
  [key: string]: string
}

// 店小秘Token检测服务类
class DianxiaomiTokenService {
  private static instance: DianxiaomiTokenService
  private tokenStatus = ref<TokenStatus>({
    isValid: false,
    isLoggedIn: false,
    message: '未检测',
    lastCheckTime: 0
  })
  
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存
  private readonly STORAGE_KEY = 'dianxiaomi_token_status'
  private readonly SESSION_STORAGE_KEY = 'dianxiaomi_token_session' // 会话存储key
  
  private constructor() {
    this.loadCachedStatus()
  }
  
  public static getInstance(): DianxiaomiTokenService {
    if (!DianxiaomiTokenService.instance) {
      DianxiaomiTokenService.instance = new DianxiaomiTokenService()
    }
    return DianxiaomiTokenService.instance
  }
  
  // 获取当前token状态（响应式）
  public get status() {
    return computed(() => this.tokenStatus.value)
  }
  
  // 检查token状态（主要方法）
  public async checkTokenStatus(forceRefresh = false): Promise<TokenStatus> {
    try {
      // 如果有缓存且未过期，直接返回缓存结果
      if (!forceRefresh && this.isCacheValid()) {
        console.info('[DianxiaomiToken] 使用缓存的token状态:', this.tokenStatus.value)
        return this.tokenStatus.value
      }

      console.info('[DianxiaomiToken] 开始检测店小秘token状态...')

      // 直接进行API验证（不依赖Cookie检测）
      // 因为运费模板同步成功说明Cookie是有效的，所以我们信任浏览器的Cookie机制
      const apiStatus = await this.verifyWithAPI()
      console.info('[DianxiaomiToken] API验证结果:', apiStatus)

      const finalStatus: TokenStatus = {
        isValid: apiStatus.success,
        isLoggedIn: apiStatus.success,
        message: apiStatus.message,
        userInfo: apiStatus.userInfo,
        shopCount: apiStatus.shopCount,
        lastCheckTime: Date.now(),
        error: apiStatus.error
      }

      this.updateStatus(finalStatus)
      return finalStatus

    } catch (error) {
      console.error('[DianxiaomiToken] 检测token状态失败:', error)

      const errorStatus: TokenStatus = {
        isValid: false,
        isLoggedIn: false,
        message: `检测失败: ${error instanceof Error ? error.message : '未知错误'}`,
        lastCheckTime: Date.now(),
        error: error instanceof Error ? error.message : '未知错误'
      }

      this.updateStatus(errorStatus)
      return errorStatus
    }
  }
  
  // 检查Cookie
  private checkCookies(): { hasDxmCookies: boolean; cookieDetails: CookieInfo } {
    const cookies = this.getCookieDetails()
    const dxmCookieKeys = Object.keys(cookies).filter(key =>
      key.includes('dxm_') || 
      key.includes('JSESSIONID') || 
      key.includes('MYJ_') ||
      key.includes('dianxiaomi')
    )
    
    return {
      hasDxmCookies: dxmCookieKeys.length > 0,
      cookieDetails: cookies
    }
  }
  
  // 通过API验证登录状态
  private async verifyWithAPI(): Promise<{
    success: boolean
    message: string
    userInfo?: any
    shopCount?: number
    error?: string
  }> {
    try {
      console.info('[DianxiaomiToken] 调用用户信息API验证登录状态...')

      const response = await fetch('https://www.dianxiaomi.com/api/userInfo.json', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'X-Requested-With': 'XMLHttpRequest',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[DianxiaomiToken] 用户信息API响应状态:', response.status)

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // 如果API返回未授权，但运费模板同步成功，说明可能是API权限问题而非登录问题
          // 我们采用更宽松的策略，尝试其他验证方式
          console.warn('[DianxiaomiToken] 用户信息API返回未授权，尝试其他验证方式...')
          return await this.fallbackVerification()
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiToken] 用户信息API响应数据:', data)

      if (data.code === 0 && data.data) {
        const userInfo = data.data

        // 计算Temu店铺数量
        let shopCount = 0
        if (userInfo.shopMap) {
          shopCount = Object.values(userInfo.shopMap).filter((shop: any) =>
            shop.platform === 'pddkj'
          ).length
        }

        return {
          success: true,
          message: `店小秘已登录 - 用户: ${userInfo.account || userInfo.username || userInfo.name || '未知'}${shopCount > 0 ? `, Temu店铺: ${shopCount}个` : ''}`,
          userInfo: {
            id: userInfo.id,
            username: userInfo.username || userInfo.account,
            name: userInfo.name || userInfo.accountAlias,
            phone: userInfo.phone,
            account: userInfo.account,
            vipLevel: userInfo.vipLevel
          },
          shopCount
        }
      } else if (data.code !== 0) {
        // API返回错误码，但可能仍然是登录状态，尝试备用验证
        console.warn('[DianxiaomiToken] 用户信息API返回错误码:', data.code, data.msg)
        return await this.fallbackVerification()
      } else {
        return {
          success: false,
          message: '店小秘认证无效，请重新登录',
          error: data.msg || 'Invalid response'
        }
      }

    } catch (error) {
      console.error('[DianxiaomiToken] API验证失败:', error)
      // 网络错误时也尝试备用验证
      return await this.fallbackVerification()
    }
  }

  // 备用验证方法 - 当主要API失败时使用
  private async fallbackVerification(): Promise<{
    success: boolean
    message: string
    userInfo?: any
    shopCount?: number
    error?: string
  }> {
    try {
      console.info('[DianxiaomiToken] 使用备用验证方法...')

      // 检查Cookie是否存在
      const cookieStatus = this.checkCookies()
      if (!cookieStatus.hasDxmCookies) {
        return {
          success: false,
          message: '未检测到店小秘认证信息，请先登录店小秘网站',
          error: 'No cookies found'
        }
      }

      // 尝试调用一个更简单的API来验证登录状态
      // 比如店铺列表API，这个API通常权限要求较低
      const response = await fetch('https://www.dianxiaomi.com/shop/list/pddkj.htm', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'text/html, */*; q=0.01',
          'X-Requested-With': 'XMLHttpRequest',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[DianxiaomiToken] 备用API响应状态:', response.status)

      if (response.ok) {
        const htmlText = await response.text()

        // 检查响应内容来判断登录状态
        if (htmlText.includes('login') && htmlText.includes('password')) {
          return {
            success: false,
            message: '检测到登录页面，请先登录店小秘',
            error: 'Login page detected'
          }
        }

        // 如果响应正常且不是登录页面，认为是已登录状态
        if (htmlText.length > 1000) {
          return {
            success: true,
            message: '店小秘已登录（通过备用验证）',
            userInfo: {
              id: 0,
              username: '未知用户',
              name: '通过备用验证',
              phone: '',
              account: '',
              vipLevel: 0
            },
            shopCount: 0
          }
        }
      }

      return {
        success: false,
        message: '无法验证登录状态，请检查网络连接',
        error: 'Fallback verification failed'
      }

    } catch (error) {
      console.error('[DianxiaomiToken] 备用验证失败:', error)
      return {
        success: false,
        message: `验证失败: ${error instanceof Error ? error.message : '网络错误'}`,
        error: error instanceof Error ? error.message : '网络错误'
      }
    }
  }
  
  // 获取Cookie详情
  private getCookieDetails(): CookieInfo {
    const cookies = document.cookie.split(';')
    const cookieObj: CookieInfo = {}
    
    cookies.forEach(cookie => {
      const [name, value] = cookie.trim().split('=')
      if (name) {
        cookieObj[name] = value || ''
      }
    })
    
    return cookieObj
  }
  
  // 检查缓存是否有效
  private isCacheValid(): boolean {
    const now = Date.now()
    const lastCheck = this.tokenStatus.value.lastCheckTime
    return (now - lastCheck) < this.CACHE_DURATION
  }
  
  // 更新状态并保存到缓存
  private updateStatus(status: TokenStatus): void {
    this.tokenStatus.value = status
    this.saveCachedStatus(status)
    // 同时保存到跨页面缓存
    saveTokenToCache(status)
  }

  // 公共方法：更新状态（用于外部推断）
  public setInferredStatus(status: TokenStatus): void {
    this.updateStatus(status)
  }
  
  // 保存状态到本地存储（双重缓存策略）
  private saveCachedStatus(status: TokenStatus): void {
    try {
      // 保存到localStorage（持久化）
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status))

      // 保存到sessionStorage（会话级别）
      sessionStorage.setItem(this.SESSION_STORAGE_KEY, JSON.stringify(status))

      console.info('[DianxiaomiToken] Token状态已保存到缓存:', {
        isValid: status.isValid,
        isLoggedIn: status.isLoggedIn,
        lastCheckTime: new Date(status.lastCheckTime).toLocaleString()
      })
    } catch (error) {
      console.warn('[DianxiaomiToken] 保存缓存状态失败:', error)
    }
  }

  // 从本地存储加载状态（优先级：sessionStorage > localStorage）
  private loadCachedStatus(): void {
    try {
      let cached: string | null = null
      let source = ''

      // 优先从sessionStorage加载（更新鲜）
      cached = sessionStorage.getItem(this.SESSION_STORAGE_KEY)
      if (cached) {
        source = 'sessionStorage'
      } else {
        // 降级到localStorage
        cached = localStorage.getItem(this.STORAGE_KEY)
        if (cached) {
          source = 'localStorage'
        }
      }

      if (cached) {
        const status = JSON.parse(cached) as TokenStatus
        // 检查缓存是否过期
        const isExpired = Date.now() - status.lastCheckTime > this.CACHE_DURATION

        if (!isExpired) {
          this.tokenStatus.value = status
          console.info(`[DianxiaomiToken] 从${source}加载缓存的token状态:`, {
            isValid: status.isValid,
            isLoggedIn: status.isLoggedIn,
            cacheAge: Math.round((Date.now() - status.lastCheckTime) / 1000) + 's'
          })
        } else {
          console.info(`[DianxiaomiToken] ${source}中的缓存已过期，将重新检测`)
          this.clearExpiredCache()
        }
      }
    } catch (error) {
      console.warn('[DianxiaomiToken] 加载缓存状态失败:', error)
    }
  }

  // 清除过期缓存
  private clearExpiredCache(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      sessionStorage.removeItem(this.SESSION_STORAGE_KEY)
      console.info('[DianxiaomiToken] 已清除过期缓存')
    } catch (error) {
      console.warn('[DianxiaomiToken] 清除过期缓存失败:', error)
    }
  }
  
  // 清除缓存
  public clearCache(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      sessionStorage.removeItem(this.SESSION_STORAGE_KEY)
      this.tokenStatus.value = {
        isValid: false,
        isLoggedIn: false,
        message: '缓存已清除，请重新检测',
        lastCheckTime: 0
      }
      console.info('[DianxiaomiToken] 所有缓存已清除')
    } catch (error) {
      console.warn('[DianxiaomiToken] 清除缓存失败:', error)
    }
  }

  // 获取缓存统计信息
  public getCacheInfo(): {
    hasLocalStorage: boolean
    hasSessionStorage: boolean
    cacheAge: number
    isExpired: boolean
  } {
    const now = Date.now()
    const lastCheckTime = this.tokenStatus.value.lastCheckTime
    const cacheAge = lastCheckTime ? now - lastCheckTime : 0
    const isExpired = cacheAge > this.CACHE_DURATION

    return {
      hasLocalStorage: !!localStorage.getItem(this.STORAGE_KEY),
      hasSessionStorage: !!sessionStorage.getItem(this.SESSION_STORAGE_KEY),
      cacheAge,
      isExpired
    }
  }
  
  // 获取详细的Cookie信息（用于调试）
  public getCookieInfo(): { 
    allCookies: CookieInfo
    dxmCookies: CookieInfo
    hasDxmCookies: boolean 
  } {
    const allCookies = this.getCookieDetails()
    const dxmCookies: CookieInfo = {}
    
    Object.keys(allCookies).forEach(key => {
      if (key.includes('dxm_') || key.includes('JSESSIONID') || key.includes('MYJ_')) {
        dxmCookies[key] = allCookies[key]
      }
    })
    
    return {
      allCookies,
      dxmCookies,
      hasDxmCookies: Object.keys(dxmCookies).length > 0
    }
  }
}

// 导出单例实例
export const dianxiaomiTokenService = DianxiaomiTokenService.getInstance()

// 导出便捷方法
export const checkDianxiaomiToken = (forceRefresh = false) => 
  dianxiaomiTokenService.checkTokenStatus(forceRefresh)

export const getDianxiaomiTokenStatus = () => 
  dianxiaomiTokenService.status

export const clearDianxiaomiTokenCache = () => 
  dianxiaomiTokenService.clearCache()

export const getDianxiaomiCookieInfo = () =>
  dianxiaomiTokenService.getCookieInfo()

export const getDianxiaomiCacheInfo = () =>
  dianxiaomiTokenService.getCacheInfo()

// 检查是否需要刷新token
export const shouldRefreshToken = () => {
  const cacheInfo = dianxiaomiTokenService.getCacheInfo()
  return cacheInfo.isExpired || !dianxiaomiTokenService.status.value.isValid
}

// 基于其他API成功调用来推断登录状态
export const inferLoginStatusFromSuccess = (apiName: string) => {
  console.info(`[DianxiaomiToken] 基于${apiName}API成功调用推断登录状态`)

  const inferredStatus: TokenStatus = {
    isValid: true,
    isLoggedIn: true,
    message: `店小秘已登录（基于${apiName}API成功推断）`,
    userInfo: {
      id: 0,
      username: '推断用户',
      name: `通过${apiName}验证`,
      phone: '',
      account: '',
      vipLevel: 0
    },
    shopCount: 0,
    lastCheckTime: Date.now()
  }

  dianxiaomiTokenService.setInferredStatus(inferredStatus)
  return inferredStatus
}
