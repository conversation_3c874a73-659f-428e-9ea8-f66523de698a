// Dashboard 数据管理
import { ref, computed, onMounted, onUnmounted, nextTick, triggerRef } from 'vue'
import { useShopBinding } from './useShopBinding'
import productListService from '../services/productListService'
import { useNotification } from './useNotification'
import { amazonPriceService, type AmazonPriceInfo } from '../services/amazon/amazonPriceService'

// 待办事项数据类型
interface TodoData {
  total: number
  details: {
    [key: string]: number
  }
}

// 价格信息类型
interface PriceInfo {
  supplierPriceValue: number // 分为单位的价格值
  supplierPriceText: string // 格式化的价格文本
  usdPrice: string // 美元价格
  exchangeRate: number // 汇率
  reviewTimes: number // 申报次数
  priceReviewStatus: number // 价格审核状态
  officialDeclaredPrice: number // 官方申报价
  stock: number // 库存
}

// 商品数据类型
// SKU信息接口
interface SkuInfo {
  skuId: number
  image: string
  color: string
  itemNo: string // extCode信息
  status: string
  price: string
  priceValue: number
  properties: string // 属性描述，如 "尺码: 11"x11""
  selectStatus?: number
  priceReviewStatus?: number
  siteSupplierPrice?: any
  // Amazon价格相关
  amazonPrice?: {
    usdPrice: string
    stock: string
    costInCNY: number
    lastUpdated: number
    dataSource: 'cache' | 'api'
  }
}

interface Product {
  id: number
  image: string
  title: string
  spu: string
  skc: string
  site: string
  currency: string
  category: string
  declaredPrice: string
  createTime: string
  extCode?: string // 添加extCode字段作为备用
  priceInfo?: PriceInfo // 价格信息

  // 多SKU支持
  isMultiSku: boolean
  totalSkuCount: number
  skuList: SkuInfo[] // 所有SKU信息

  // 主要SKU信息（用于向后兼容和主要显示）
  sku: SkuInfo

  // 价格范围（多SKU时使用）
  priceRange?: {
    min: number
    max: number
    minText: string
    maxText: string
    range: string
  }

  // 申报相关信息
  priceReview?: {
    priceOrderId: number | null
    times: number
    orderType: string | null
    hasReviewInfo: boolean
  }
}

// 兼容ProductData类型
type ProductData = Product

// 标签页数据类型
interface Tab {
  key: string
  label: string
  count: number
}

// 搜索表单类型
interface SearchForm {
  site: string // 站点：us=美国站(100)
  productIdType: string // 商品ID类型：SPU/SKC/SKU
  productId: string // 商品ID输入框内容（支持空格分隔多选）
  extCodeType: string // 货号类型：SKC/SKU
  extCode: string // 货号输入框内容（支持空格分隔多选）
  category?: string // 商品分类筛选
  stockFilter?: number // 货盘库存过滤（小于等于）
  profitRateOperator?: string // 毛利率操作符：gte/lte
  profitRateFilter?: number // 毛利率过滤值（百分比）
}

export function useDashboard() {
  // 使用通知功能
  const { info } = useNotification()

  // 基础状态
  const isLoading = ref(false)
  const todoData = ref<TodoData>({ total: 0, details: {} })
  const products = ref<Product[]>([])
  const tabs = ref<Tab[]>([
    { key: 'all', label: '全部', count: 0 },
    { key: 'price-confirming', label: '价格待确认', count: 0 },
    { key: 'unpublished', label: '未发布到站点', count: 0 },
    { key: 'published', label: '已发布站点', count: 0 },
    { key: 'offline', label: '已下架', count: 0 }
  ])

  // Amazon价格获取状态管理
  const isAmazonPriceFetching = ref(false)
  const lastAmazonFetchTime = ref(0)
  const AMAZON_FETCH_COOLDOWN = 5000 // 5秒冷却时间
  const activeTab = ref('all')

  // 防重复调用标志
  const isInitialized = ref(false)

  // 分页相关
  const pageNum = ref(1)
  const pageSize = ref(50)
  const total = ref(0)

  // 商品选择状态管理
  const selectedProductIds = ref<Set<number>>(new Set())
  const isAllSelected = ref(false)
  const isIndeterminate = ref(false)

  // 解析多选输入（空格分隔）
  const parseMultipleInput = (input: string): number[] => {
    if (!input || !input.trim()) return []

    return input
      .trim()
      .split(/\s+/) // 按空格分割
      .map(item => item.trim())
      .filter(item => item.length > 0)
      .map(item => {
        const num = parseInt(item, 10)
        return isNaN(num) ? 0 : num
      })
      .filter(num => num > 0)
  }

  // 解析多选货号输入（空格分隔）
  const parseMultipleExtCode = (input: string): string[] => {
    if (!input || !input.trim()) return []

    return input
      .trim()
      .split(/\s+/) // 按空格分割
      .map(item => item.trim())
      .filter(item => item.length > 0)
  }

  // 获取站点ID
  const getSiteId = (site: string): number => {
    switch (site.toLowerCase()) {
      case 'us':
      case 'usa':
        return 100 // 美国站
      default:
        return 100 // 默认美国站
    }
  }
  
  // 搜索表单
  const searchForm = ref<SearchForm>({
    site: 'us', // 默认美国站
    productIdType: 'SPU', // 商品ID类型：SPU/SKC/SKU
    productId: '', // 商品ID（支持空格分隔多选）
    extCodeType: 'SKC', // 货号类型：SKC/SKU
    extCode: '', // 货号（支持空格分隔多选）
    stockFilter: undefined, // 货盘库存过滤
    profitRateOperator: 'gte', // 毛利率操作符，默认大于等于
    profitRateFilter: undefined // 毛利率过滤值
  })

  // 使用店铺绑定状态
  const { shopBinding } = useShopBinding()
  
  // 当前店铺信息
  const currentShopInfo = computed(() => {
    return shopBinding.value?.temuSiteInfo || null
  })

  // 店铺ID
  const mallId = computed(() => {
    return currentShopInfo.value?.mallId || currentShopInfo.value?.shopId || null
  })

  // 获取待办事项数量
  const fetchTodoCount = async () => {
    try {
      isLoading.value = true
      const result = await productListService.getTodoCount()

      if (result && typeof result === 'object') {
        // 处理待办事项数据
        const todoCount = (result as any).total || 0
        todoData.value = {
          total: todoCount,
          details: (result as any).details || {}
        }
        console.info('[useDashboard] 待办事项数据更新成功:', result)
      }

      return result
    } catch (error) {
      console.error('[useDashboard] 获取待办事项失败:', error)

      // 提供更友好的错误提示
      if (error instanceof Error) {
        if (error.message.includes('HTTP 403')) {
          console.error('[useDashboard] 权限验证失败，请确保已登录Temu商家后台')
        } else if (error.message.includes('未找到 Temu 商家后台标签页')) {
          console.error('[useDashboard] 请先打开Temu商家后台页面')
        } else if (error.message.includes('Chrome API 不可用')) {
          console.error('[useDashboard] 扩展权限不足，请重新安装扩展')
        }
      }

      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取商品列表
  const fetchProducts = async (customParams?: any) => {
    try {
      isLoading.value = true
      console.info('[useDashboard] 开始获取商品列表...')

      // 如果没有自定义参数，使用统一的查询参数构造函数
      let params = customParams
      if (!params) {
        params = buildQueryParams(activeTab.value, pageNum.value, pageSize.value)
      }

      console.info('[useDashboard] 请求参数:', params)

      // 如果是价格待确认标签页，使用优化的方法
      let result
      if (activeTab.value === 'price-confirming') {
        console.info('[useDashboard] 价格待确认标签页，使用优化方法获取数据...')

        try {
          result = await productListService.getPriceConfirmationProducts(params)
          console.info('[useDashboard] 价格确认数据获取结果:', result)

          // 转换为标准格式
          if (result.success) {
            result = {
              success: true,
              result: {
                dataList: result.data,
                total: result.total,
                productSkcStatusAggregation: [],
                productIdNoStatusTotal: result.total,
                recycleCount: null
              }
            }
            console.info('[useDashboard] 价格确认数据格式转换完成')
          } else {
            console.error('[useDashboard] 价格确认数据获取失败:', result.errorMsg)
            throw new Error(result.errorMsg || '获取价格确认数据失败')
          }
        } catch (error) {
          console.error('[useDashboard] 价格确认标签页数据获取异常:', error)
          // 降级到普通方法
          console.info('[useDashboard] 降级使用普通方法获取数据...')
          result = await productListService.getProductList(params)
        }
      } else {
        result = await productListService.getProductList(params)
      }

      console.info('[useDashboard] API返回结果:', result)
      console.info('[useDashboard] 数据结构分析:', {
        hasResult: !!result,
        resultType: typeof result,
        success: result?.success,
        hasData: !!result?.data,
        dataType: typeof result?.data,
        dataIsArray: Array.isArray(result?.data),
        dataLength: result?.data?.length,
        total: result?.total
      })

      // 检查新的 productListService 返回格式
      if (result && result.success && result.data) {
        console.info('[useDashboard] 使用新的productListService格式数据...')
        console.info('[useDashboard] 商品数量:', result.data.length)
        console.info('[useDashboard] 总数:', result.total)

        // 使用新的productListService返回的格式化数据
        products.value = result.data as any[]
        total.value = result.total || 0

        console.info('[useDashboard] 设置总数:', total.value)

        // 设置标签页数据 - 使用原始数据
        let formattedTabs: any[]
        if (result.rawData) {
          formattedTabs = productListService.formatStatusTabs(result.rawData)
          console.info('[useDashboard] 标签页数据更新:', formattedTabs)
        } else {
          // 兜底：构造基础标签页
          formattedTabs = [{ key: 'all', label: '全部', count: result.total || 0 }]
          console.warn('[useDashboard] 没有原始数据，使用基础标签页')
        }
        console.info('[useDashboard] 格式化后的标签页:', formattedTabs)
        tabs.value = formattedTabs

        // 如果是价格待确认标签页，优先从缓存获取Amazon价格
        if (activeTab.value === 'price-confirming') {
          console.info('[useDashboard] 价格待确认标签页，优先从缓存获取Amazon价格...')

          // 立即从缓存获取Amazon价格
          loadAmazonPricesFromCache()

          // 延迟执行API请求，确保界面先渲染
          setTimeout(() => {
            batchFetchAmazonPricesWithCooldown(false)
          }, 500) // 减少延迟时间
        }
      } else {
        console.warn('[useDashboard] API返回结果无效:', {
          hasResult: !!result,
          success: result?.success,
          hasData: !!result?.data,
          dataLength: result?.data?.length,
          total: result?.total,
          errorMsg: result?.errorMsg
        })
        // 设置空数据
        products.value = []
        total.value = 0
      }

      return result
    } catch (error) {
      console.error('[useDashboard] 获取商品列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 搜索商品
  const searchProducts = async () => {
    try {
      isLoading.value = true
      console.info('[useDashboard] 搜索参数:', searchForm.value)

      // 重置页码
      pageNum.value = 1

      // 构造查询参数，包含搜索条件
      const params = buildQueryParams(activeTab.value, 1, pageSize.value, true)
      console.info('[useDashboard] 构造的查询参数:', params)

      const result = await productListService.getProductList(params)

      if (result && result.success) {
        products.value = result.data
        total.value = result.total || 0

        // 更新标签页数据 - 使用原始数据
        if (result.rawData) {
          const formattedTabs = productListService.formatStatusTabs(result.rawData)
          tabs.value = formattedTabs
        } else {
          // 兜底：保持当前标签页不变
          console.warn('[useDashboard] 搜索结果没有原始数据，保持当前标签页')
        }

        console.info('[useDashboard] 搜索完成，找到', total.value, '个商品')

        // 搜索后不自动获取Amazon价格，避免重复请求
        // 用户可以手动刷新或等待自动获取
      } else {
        products.value = []
        total.value = 0
        console.warn('[useDashboard] 搜索结果为空')
      }

      return result
    } catch (error) {
      console.error('[useDashboard] 搜索商品失败:', error)
      info('错误', '搜索商品失败，请稍后重试')
      products.value = []
      total.value = 0
      throw error
    } finally {
      isLoading.value = false
    }
  }



  // 构造查询参数
  const buildQueryParams = (tabKey: string, pageNum: number = 1, pageSize: number = 20, includeSearchForm: boolean = false) => {
    const baseParams: any = {
      pageSize,
      pageNum,
      supplierTodoTypeList: [] as number[]
    }

    // 添加站点信息
    const siteId = getSiteId(searchForm.value.site)
    if (siteId) {
      baseParams.siteIdList = [siteId]
    }

    // 如果包含搜索表单，添加搜索条件
    if (includeSearchForm) {
      // 处理商品ID查询
      if (searchForm.value.productId && searchForm.value.productId.trim()) {
        const productIds = parseMultipleInput(searchForm.value.productId)
        if (productIds.length > 0) {
          switch (searchForm.value.productIdType) {
            case 'SPU':
              baseParams.productSpuIdList = productIds
              break
            case 'SKC':
              baseParams.productSkcIdList = productIds
              break
            case 'SKU':
              baseParams.productSkuIdList = productIds
              break
          }
        }
      }

      // 处理货号查询
      if (searchForm.value.extCode && searchForm.value.extCode.trim()) {
        const extCodes = parseMultipleExtCode(searchForm.value.extCode)
        if (extCodes.length > 0) {
          switch (searchForm.value.extCodeType) {
            case 'SKC':
              baseParams.skcExtCodeList = extCodes
              break
            case 'SKU':
              baseParams.skuExtCodeList = extCodes
              break
          }
        }
      }

      // 处理货盘库存过滤
      if (searchForm.value.stockFilter !== undefined && searchForm.value.stockFilter > 0) {
        baseParams.maxStock = searchForm.value.stockFilter
      }

      // 处理毛利率过滤
      if (searchForm.value.profitRateFilter !== undefined && searchForm.value.profitRateFilter >= 0) {
        if (searchForm.value.profitRateOperator === 'gte') {
          baseParams.minProfitRate = searchForm.value.profitRateFilter
        } else if (searchForm.value.profitRateOperator === 'lte') {
          baseParams.maxProfitRate = searchForm.value.profitRateFilter
        }
      }
    }

    // 根据标签页构造不同的查询参数
    switch (tabKey) {
      case 'all':
        // 全部：空的supplierTodoTypeList
        return baseParams

      case 'price-confirming':
        // 价格待确认：特殊的查询参数结构
        return {
          ...baseParams,
          priceReviewStatusList: [0, 1, 2, 3],
          secondarySelectStatusList: [7],
          supplierTodoTypeList: [1]
        }

      case 'unpublished':
        // 未发布到站点
        return {
          ...baseParams,
          secondarySelectStatusList: [10, 11],
          supplierTodoTypeList: []
        }

      case 'published':
        // 已发布站点
        return {
          ...baseParams,
          secondarySelectStatusList: [12],
          supplierTodoTypeList: []
        }

      case 'offline':
        // 已下架
        return {
          ...baseParams,
          secondarySelectStatusList: [13],
          supplierTodoTypeList: []
        }

      case 'selected':
        // 已选中
        return {
          ...baseParams,
          secondarySelectStatusList: [1],
          supplierTodoTypeList: []
        }

      case 'price-reporting':
        // 价格申报中
        return {
          ...baseParams,
          secondarySelectStatusList: [7],
          supplierTodoTypeList: []
        }

      default:
        return baseParams
    }
  }

  // 切换标签页
  const switchTab = async (tabKey: string) => {
    const startTime = Date.now()
    console.info('[useDashboard] 切换标签页:', tabKey, '开始时间:', new Date().toLocaleTimeString())

    try {
      activeTab.value = tabKey

      // 重置页码
      pageNum.value = 1

      // 构造查询参数
      const params = buildQueryParams(tabKey, 1, pageSize.value)
      console.info('[useDashboard] 标签页查询参数:', params)

      // 特殊处理价格确认标签页
      if (tabKey === 'price-confirming') {
        console.info('[useDashboard] 🎯 开始处理价格确认标签页...')
      }

      await fetchProducts(params)

      const elapsed = Date.now() - startTime
      console.info('[useDashboard] 标签页切换完成:', tabKey, '耗时:', elapsed + 'ms')

      if (tabKey === 'price-confirming') {
        console.info('[useDashboard] ✅ 价格确认标签页处理完成')
      }

    } catch (error) {
      const elapsed = Date.now() - startTime
      console.error('[useDashboard] 标签页切换失败:', tabKey, '耗时:', elapsed + 'ms', '错误:', error)
      throw error
    }
  }

  // 处理分页变化
  const handlePageChange = async (page: number) => {
    console.info('[useDashboard] 页码变更:', page)
    pageNum.value = page

    // 使用统一的查询参数构造函数
    const params = buildQueryParams(activeTab.value, page, pageSize.value)
    console.info('[useDashboard] 分页查询参数:', params)

    await fetchProducts(params)
  }

  // 跳转到货源
  const jumpToSource = (product: ProductData) => {
    console.info('[useDashboard] 跳转货源:', product)

    try {
      // 从产品数据中提取extCode
      let extCode = ''

      // 尝试从不同的字段获取extCode
      if (product.sku?.itemNo) {
        extCode = product.sku.itemNo
      } else if (product.extCode) {
        extCode = product.extCode
      } else {
        // 如果没有找到extCode，显示错误信息
        info('错误', '未找到商品货源信息')
        return
      }

      console.info('[useDashboard] 提取到extCode:', extCode)

      // 解析extCode，提取ASIN和平台信息
      // 格式：B07X46RK45[am]1
      const match = extCode.match(/^([A-Z0-9]{10})\[([a-z]+)\](\d+)$/)

      if (!match) {
        info('错误', '货源信息格式不正确')
        return
      }

      const [, asin, platform, version] = match
      console.info('[useDashboard] 解析结果:', { asin, platform, version })

      // 根据平台构造链接
      let sourceUrl = ''

      switch (platform.toLowerCase()) {
        case 'am':
        case 'amazon':
          sourceUrl = `https://www.amazon.com/dp/${asin}`
          break
        case 'eb':
        case 'ebay':
          sourceUrl = `https://www.ebay.com/itm/${asin}`
          break
        case 'al':
        case 'aliexpress':
          sourceUrl = `https://www.aliexpress.com/item/${asin}.html`
          break
        default:
          info('错误', `不支持的平台: ${platform}`)
          return
      }

      console.info('[useDashboard] 构造的源链接:', sourceUrl)

      // 在新窗口中打开链接
      window.open(sourceUrl, '_blank', 'noopener,noreferrer')

      // 显示成功信息
      info('跳转', `正在跳转到 ${platform.toUpperCase()} 货源页面`)

      // 触发价格获取（如果是Amazon）
      if (platform.toLowerCase() === 'am' || platform.toLowerCase() === 'amazon') {
        // 通过Chrome扩展API触发价格获取
        if (typeof chrome !== 'undefined' && chrome.runtime) {
          chrome.runtime.sendMessage({
            action: 'GET_AMAZON_PRICE',
            asin: asin,
            extCode: extCode,
            sourceUrl: sourceUrl
          }).catch(error => {
            console.warn('[useDashboard] 价格获取请求失败:', error)
          })
        }
      }

    } catch (error) {
      console.error('[useDashboard] 跳转货源失败:', error)
      info('错误', '跳转货源失败，请稍后重试')
    }
  }

  // 商品选择相关方法
  const updateSelectionState = () => {
    const totalProducts = products.value.length
    const selectedCount = selectedProductIds.value.size

    isAllSelected.value = totalProducts > 0 && selectedCount === totalProducts
    isIndeterminate.value = selectedCount > 0 && selectedCount < totalProducts
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      products.value.forEach(product => {
        selectedProductIds.value.add(product.id)
      })
    } else {
      selectedProductIds.value.clear()
    }
    updateSelectionState()
  }

  const handleSelectProduct = (productId: number, checked: boolean) => {
    if (checked) {
      selectedProductIds.value.add(productId)
    } else {
      selectedProductIds.value.delete(productId)
    }
    updateSelectionState()
  }

  const isProductSelected = (productId: number) => {
    return selectedProductIds.value.has(productId)
  }

  const getSelectedProducts = () => {
    return products.value.filter(product => selectedProductIds.value.has(product.id))
  }

  const clearSelection = () => {
    selectedProductIds.value.clear()
    updateSelectionState()
  }

  // 获取Amazon价格信息
  const fetchAmazonPrice = async (extCode: string, forceRefresh: boolean = false): Promise<AmazonPriceInfo | null> => {
    try {
      console.info('[useDashboard] 获取Amazon价格:', extCode)
      const priceInfo = await amazonPriceService.getAmazonPriceInfo(extCode, forceRefresh)

      if (priceInfo) {
        console.info('[useDashboard] Amazon价格获取成功:', priceInfo)
        return priceInfo
      } else {
        console.warn('[useDashboard] Amazon价格获取失败:', extCode)
        return null
      }
    } catch (error) {
      console.error('[useDashboard] Amazon价格获取异常:', error)
      return null
    }
  }

  // 强制刷新商品数据显示
  const forceRefreshProducts = () => {
    console.info('[useDashboard] 强制刷新商品数据显示')
    // 创建新的数组引用触发响应式更新
    const currentProducts = [...products.value]
    products.value = currentProducts
    // 强制触发响应式更新
    triggerRef(products)
    console.info('[useDashboard] 商品数据强制刷新完成')
  }

  // 从缓存加载Amazon价格
  const loadAmazonPricesFromCache = async () => {
    try {
      console.info('[useDashboard] 开始从缓存加载Amazon价格...')

      // 提取所有Amazon商品的extCode
      const amazonExtCodes = products.value
        .map(product => {
          let extCode = ''
          if (product.sku?.itemNo) {
            extCode = product.sku.itemNo
          } else if (product.extCode) {
            extCode = product.extCode
          }
          return extCode
        })
        .filter(extCode => extCode && extCode.includes('[am]'))

      if (amazonExtCodes.length === 0) {
        console.info('[useDashboard] 没有Amazon商品需要加载缓存价格')
        return
      }

      console.info('[useDashboard] 找到', amazonExtCodes.length, '个Amazon商品，开始从缓存加载价格')

      // 批量从缓存获取价格（不强制刷新）
      const priceMap = await amazonPriceService.batchGetAmazonPriceInfo(amazonExtCodes)
      console.info('[useDashboard] 从缓存获取到的价格数据:', priceMap.size, '个')

      if (priceMap.size === 0) {
        console.info('[useDashboard] 缓存中没有价格数据')
        return
      }

      // 更新商品的价格信息
      let updatedCount = 0
      const updatedProducts = products.value.map(product => {
        const extCode = product.sku?.itemNo || product.extCode || ''
        if (extCode && priceMap.has(extCode)) {
          const amazonPrice = priceMap.get(extCode)!

          // 确保priceInfo对象存在
          const currentPriceInfo = product.priceInfo || {
            supplierPriceValue: 0,
            supplierPriceText: '',
            usdPrice: '0.00',
            exchangeRate: 7.2,
            reviewTimes: 0,
            priceReviewStatus: 0,
            officialDeclaredPrice: 0,
            stock: 10
          }

          // 创建更新的商品对象
          const updatedProduct = {
            ...product,
            priceInfo: {
              ...currentPriceInfo,
              usdPrice: amazonPrice.amazonPrice?.usdPrice || '0.00',
              stock: Number(amazonPrice.amazonPrice?.stock) || 0,
              exchangeRate: 7.2
            }
          }

          updatedCount++
          console.info('[useDashboard] 从缓存更新商品价格:', {
            productId: product.id,
            extCode: extCode,
            price: amazonPrice.amazonPrice?.usdPrice || '0.00',
            stock: amazonPrice.amazonPrice?.stock || 0
          })

          return updatedProduct
        }
        return product
      })

      if (updatedCount > 0) {
        // 直接替换整个products数组
        products.value = updatedProducts
        triggerRef(products)
        await nextTick()

        console.info('[useDashboard] 从缓存更新了', updatedCount, '个Amazon商品的价格')
      }

    } catch (error) {
      console.error('[useDashboard] 从缓存加载Amazon价格失败:', error)
    }
  }

  // 带冷却时间的Amazon价格获取
  const batchFetchAmazonPricesWithCooldown = async (forceRefresh: boolean = false) => {
    const now = Date.now()

    // 检查是否正在获取中
    if (isAmazonPriceFetching.value) {
      console.info('[useDashboard] Amazon价格获取正在进行中，跳过')
      return
    }

    // 检查冷却时间
    if (!forceRefresh && (now - lastAmazonFetchTime.value) < AMAZON_FETCH_COOLDOWN) {
      console.info('[useDashboard] Amazon价格获取冷却中，跳过')
      return
    }

    isAmazonPriceFetching.value = true
    lastAmazonFetchTime.value = now

    try {
      await batchFetchAmazonPrices(forceRefresh)
    } finally {
      isAmazonPriceFetching.value = false
    }
  }

  // 批量获取Amazon价格
  const batchFetchAmazonPrices = async (forceRefresh: boolean = false) => {
    try {
      console.info('[useDashboard] 开始批量获取Amazon价格...')

      // 提取所有Amazon商品的extCode
      const amazonExtCodes = products.value
        .map(product => {
          // 尝试从不同字段获取extCode
          let extCode = ''
          if (product.sku?.itemNo) {
            extCode = product.sku.itemNo
          } else if (product.extCode) {
            extCode = product.extCode
          }
          return extCode
        })
        .filter(extCode => extCode && extCode.includes('[am]'))

      if (amazonExtCodes.length === 0) {
        console.info('[useDashboard] 没有Amazon商品需要获取价格')
        return
      }

      console.info('[useDashboard] 找到', amazonExtCodes.length, '个Amazon商品:', amazonExtCodes)

      // 批量获取价格
      const priceMap = await amazonPriceService.batchGetAmazonPriceInfo(amazonExtCodes)
      console.info('[useDashboard] 获取到的价格数据:', priceMap)

      // 更新商品的价格信息 - 创建全新的products数组确保响应式更新
      let updatedCount = 0
      const updatedProducts = products.value.map(product => {
        const extCode = product.sku?.itemNo || product.extCode || ''
        if (extCode && priceMap.has(extCode)) {
          const amazonPrice = priceMap.get(extCode)!

          // 确保priceInfo对象存在
          const currentPriceInfo = product.priceInfo || {
            supplierPriceValue: 0,
            supplierPriceText: '',
            usdPrice: '0.00',
            exchangeRate: 7.2,
            reviewTimes: 0,
            priceReviewStatus: 0,
            officialDeclaredPrice: 0,
            stock: 10
          }

          // 创建全新的商品对象和priceInfo对象确保响应式更新
          const updatedProduct = {
            ...product,
            priceInfo: {
              ...currentPriceInfo,
              usdPrice: amazonPrice.amazonPrice?.usdPrice || '0.00',
              stock: Number(amazonPrice.amazonPrice?.stock) || 0,
              exchangeRate: 7.2 // 默认汇率
            }
          }

          updatedCount++
          console.info('[useDashboard] 更新商品价格信息:', {
            productId: product.id,
            extCode: extCode,
            price: amazonPrice.amazonPrice?.usdPrice || '0.00',
            stock: amazonPrice.amazonPrice?.stock || 0,
            newPriceInfo: updatedProduct.priceInfo
          })

          return updatedProduct
        }
        return product
      })

      console.info('[useDashboard] 批量价格获取完成，更新了', updatedCount, '个商品的价格')

      if (updatedCount > 0) {
        // 直接替换整个products数组确保响应式更新
        products.value = updatedProducts

        // 强制触发响应式更新
        triggerRef(products)

        // 使用nextTick确保DOM更新
        await nextTick()

        console.info('[useDashboard] 已替换products数组并强制触发响应式更新')

        // 显示成功信息
        info('成功', `已更新 ${updatedCount} 个Amazon商品的价格信息`)
      } else {
        info('提示', '没有找到需要更新价格的Amazon商品')
      }

    } catch (error) {
      console.error('[useDashboard] 批量获取Amazon价格失败:', error)
      info('错误', 'Amazon价格获取失败，请稍后重试')
    }
  }

  // 批量价格确认
  const batchPriceConfirm = async () => {
    const selectedProducts = getSelectedProducts()
    if (selectedProducts.length === 0) {
      info('提示', '请先选择要确认价格的商品')
      return
    }

    try {
      isLoading.value = true
      console.info('[useDashboard] 批量价格确认:', selectedProducts.length, '个商品')

      // 这里可以调用批量价格确认的API
      // await temuDataService.batchConfirmPrice(selectedProducts)

      info('成功', `已成功确认 ${selectedProducts.length} 个商品的价格`)
      clearSelection()
      await refreshData()
    } catch (error) {
      console.error('[useDashboard] 批量价格确认失败:', error)
      info('错误', '批量价格确认失败，请稍后重试')
    } finally {
      isLoading.value = false
    }
  }

  // 刷新数据
  const refreshData = async () => {
    try {
      console.info('[useDashboard] 开始刷新数据...')
      await Promise.all([
        fetchTodoCount(),
        fetchProducts()
      ])
      console.info('[useDashboard] 数据刷新完成')
    } catch (error) {
      console.error('[useDashboard] 刷新数据失败:', error)
    }
  }

  // 监听官方申报价数据更新事件
  const handlePriceConfirmationDataUpdate = (event: Event) => {
    console.info('[useDashboard] 收到官方申报价数据更新事件')
    const customEvent = event as CustomEvent
    const { updatedData } = customEvent.detail

    if (updatedData && Array.isArray(updatedData)) {
      // 更新products数据
      products.value = updatedData
      triggerRef(products)
      console.info('[useDashboard] 官方申报价数据更新完成')
    }
  }

  // 组件挂载时初始化数据
  onMounted(() => {
    console.info('[useDashboard] 组件挂载，初始化数据...')

    // 防止重复初始化
    if (isInitialized.value) {
      console.info('[useDashboard] 已经初始化过，跳过')
      return
    }

    // 监听官方申报价数据更新事件
    if (typeof window !== 'undefined') {
      window.addEventListener('priceConfirmationDataUpdated', handlePriceConfirmationDataUpdate)
    }

    // 设置店铺ID到数据服务
    if (mallId.value) {
      console.info('[useDashboard] 设置店铺ID到数据服务:', mallId.value)
      productListService.setMallId(mallId.value.toString())
    } else {
      console.warn('[useDashboard] mallId 为空，尝试从其他来源获取...')

      // 尝试从localStorage获取
      const sources = [
        'temu_cs_mall_id',
        'ultimate_mall_id',
        'temu_cached_mall_id',
        'temu_mall_id'
      ]

      for (const key of sources) {
        const cached = localStorage.getItem(key)
        if (cached) {
          console.info('[useDashboard] 从localStorage获取到mallId:', key, cached)
          // mallId 是计算属性，不能直接赋值，需要通过 shopBinding 更新
          console.info('[useDashboard] 找到缓存的mallId，但需要通过店铺绑定更新')
          productListService.setMallId(cached)
          break
        }
      }

      if (!mallId.value) {
        console.error('[useDashboard] 无法获取mallId，请确保已登录Temu商家后台')
      }
    }

    isInitialized.value = true
    refreshData()
  })

  // 初始化数据服务
  const initDataService = () => {
    console.info('[useDashboard] 初始化数据服务')
    // 这里可以添加数据服务初始化逻辑
  }

  // 组件卸载时清理
  onUnmounted(() => {
    console.info('[useDashboard] 组件卸载，清理事件监听...')
    if (typeof window !== 'undefined') {
      window.removeEventListener('priceConfirmationDataUpdated', handlePriceConfirmationDataUpdate)
    }
  })

  return {
    // 状态
    isLoading,
    todoData,
    products,
    tabs,
    activeTab,
    pageNum: pageNum,
    pageSize,
    total,
    searchForm,
    currentShopInfo,
    mallId,

    // 商品选择状态
    selectedProductIds,
    isAllSelected,
    isIndeterminate,

    // 方法
    refreshData,
    fetchTodoCount,
    fetchProducts,
    searchProducts,
    switchTab,
    handlePageChange,
    jumpToSource,
    initDataService,
    buildQueryParams,

    // 商品选择方法
    handleSelectAll,
    handleSelectProduct,
    isProductSelected,
    getSelectedProducts,
    clearSelection,
    updateSelectionState,
    batchPriceConfirm,

    // Amazon价格方法
    fetchAmazonPrice,
    batchFetchAmazonPrices,
    forceRefreshProducts,

    // 工具方法
    parseMultipleInput,
    parseMultipleExtCode,
    getSiteId
  }
}
