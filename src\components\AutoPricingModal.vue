<template>
  <a-modal
    v-model:open="visible"
    title="自动核价"
    :width="'90%'"
    class="auto-pricing-modal"
    :footer="null"
    :closable="!isRunning"
    :mask-closable="false"
    @cancel="handleClose"
  >
    <div class="auto-pricing-container">
      <!-- 控制面板 -->
      <div class="control-panel mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <a-button
              v-if="canStart"
              type="primary"
              size="large"
              @click="startAutoPricing"
              :loading="status === 'running'"
            >
              {{ isPaused ? '继续核价' : '开始自动核价' }}
            </a-button>
            
            <a-button
              v-if="canPause"
              size="large"
              @click="pauseAutoPricing"
            >
              暂停核价
            </a-button>
            
            <a-button
              v-if="isCompleted || status === 'error'"
              size="large"
              @click="resetAutoPricing"
            >
              重新开始
            </a-button>
          </div>
          
          <div class="text-right">
            <div class="text-lg font-semibold">
              进度: {{ currentIndex }}/{{ total }}
            </div>
            <div class="text-sm text-gray-500">
              {{ progress.toFixed(1) }}%
            </div>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div class="mt-4">
          <a-progress
            :percent="progress"
            :status="status === 'error' ? 'exception' : 'normal'"
            :stroke-color="status === 'running' ? '#1890ff' : '#52c41a'"
          />
        </div>
        
        <!-- 错误信息 -->
        <div v-if="errorMessage" class="mt-4">
          <a-alert
            :message="errorMessage"
            type="error"
            show-icon
            closable
          />
        </div>
      </div>

      <!-- 统计信息 -->
      <div v-if="stats.total > 0" class="stats-panel mb-6">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-card class="text-center" size="small">
              <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
              <div class="text-sm text-gray-600">核价成功数据</div>
            </a-card>
          </a-col>

          <a-col :span="4">
            <a-card class="text-center" size="small">
              <div class="text-2xl font-bold text-green-600">{{ stats.firstTimePassRate.toFixed(2) }}%</div>
              <div class="text-sm text-gray-600">首次申报通过率</div>
            </a-card>
          </a-col>

          <a-col :span="4">
            <a-card class="text-center" size="small">
              <div class="text-2xl font-bold text-orange-600">{{ stats.currentPassRate.toFixed(2) }}%</div>
              <div class="text-sm text-gray-600">当前通过率</div>
            </a-card>
          </a-col>

          <a-col :span="4">
            <a-card class="text-center" size="small">
              <div class="text-2xl font-bold text-purple-600">{{ stats.repriced }}</div>
              <div class="text-sm text-gray-600">重新报价</div>
            </a-card>
          </a-col>

          <a-col :span="4">
            <a-card class="text-center" size="small">
              <div class="text-2xl font-bold text-red-600">{{ stats.rejected }}</div>
              <div class="text-sm text-gray-600">拒绝核价</div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 结果展示 -->
      <div v-if="results.length > 0" class="results-panel">
        <a-tabs default-active-key="approved">
          <!-- 核价成功 -->
          <a-tab-pane key="approved" :tab="`核价成功数据 ${approvedResults.length}`">
            <a-table
              :dataSource="approvedResults"
              :columns="approvedColumns"
              :pagination="{ pageSize: 10 }"
              size="small"
              :scroll="{ x: 1200 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'profitRate'">
                  <span :class="record.profitRate >= 20 ? 'text-green-600' : 'text-red-600'">
                    {{ record.profitRate.toFixed(2) }}%
                  </span>
                </template>
                <template v-else-if="column.key === 'stock'">
                  <span class="text-red-600 font-bold">{{ record.stock }}(货盘库存)</span>
                </template>
                <template v-else-if="column.key === 'costPrice'">
                  <span>{{ record.costPrice.toFixed(3) }}(货盘价格)</span>
                </template>
              </template>
            </a-table>
          </a-tab-pane>

          <!-- 重新报价 -->
          <a-tab-pane key="repriced" :tab="`重新报价数据 ${repricedResults.length}`">
            <a-table
              :dataSource="repricedResults"
              :columns="repricedColumns"
              :pagination="{ pageSize: 10 }"
              size="small"
              :scroll="{ x: 1400 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'originalProfitRate'">
                  <span class="text-red-600">
                    {{ calculateProfitRate(record.suggestedPrice, record.costPrice).toFixed(2) }}%
                  </span>
                </template>
                <template v-else-if="column.key === 'newProfitRate'">
                  <span class="text-green-600">
                    {{ record.profitRate.toFixed(2) }}%
                  </span>
                </template>
                <template v-else-if="column.key === 'stock'">
                  <span class="text-red-600 font-bold">{{ record.stock }}(货盘库存)</span>
                </template>
                <template v-else-if="column.key === 'costPrice'">
                  <span>{{ record.costPrice.toFixed(3) }}(货盘价格)</span>
                </template>
              </template>
            </a-table>
          </a-tab-pane>

          <!-- 拒绝核价 -->
          <a-tab-pane key="rejected" :tab="`拒绝核价数据 ${rejectedResults.length}`">
            <a-table
              :dataSource="rejectedResults"
              :columns="rejectedColumns"
              :pagination="{ pageSize: 10 }"
              size="small"
              :scroll="{ x: 1100 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'profitRate'">
                  <span class="text-red-600">
                    {{ record.profitRate.toFixed(2) }}%
                  </span>
                </template>
                <template v-else-if="column.key === 'stock'">
                  <span class="text-red-600 font-bold">{{ record.stock }}(货盘库存)</span>
                </template>
                <template v-else-if="column.key === 'costPrice'">
                  <span>{{ record.costPrice.toFixed(3) }}(货盘价格)</span>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits } from 'vue'
import { useAutoPricing } from '../composables/useAutoPricing'

// 定义事件
const emit = defineEmits<{
  close: []
}>()

// 使用自动核价功能
const {
  status,
  progress,
  currentIndex,
  total,
  errorMessage,
  results,
  stats,
  isRunning,
  isPaused,
  isCompleted,
  canStart,
  canPause,
  approvedResults,
  repricedResults,
  rejectedResults,
  startAutoPricing,
  pauseAutoPricing,
  resetAutoPricing
} = useAutoPricing()

// 弹窗显示状态
const visible = ref(true)

// 表格列定义
const approvedColumns = [
  { title: 'SPU', dataIndex: 'spu', key: 'spu', width: 120 },
  { title: 'SKC', dataIndex: 'skc', key: 'skc', width: 120 },
  { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 120 },
  { title: '原申报价', dataIndex: 'originalPrice', key: 'originalPrice', width: 100 },
  { title: '官方建议报价', dataIndex: 'suggestedPrice', key: 'suggestedPrice', width: 120 },
  { title: '核价次数', dataIndex: 'reviewTimes', key: 'reviewTimes', width: 100 },
  { title: '通过价格', dataIndex: 'finalPrice', key: 'finalPrice', width: 100 },
  { title: '货号', dataIndex: 'extCode', key: 'extCode', width: 150 },
  { title: '货盘库存', key: 'stock', width: 120 },
  { title: '成本价格', key: 'costPrice', width: 120 },
  { title: '利润率', key: 'profitRate', width: 100 },
  { title: '其他', dataIndex: 'remark', key: 'remark', width: 120 }
]

const repricedColumns = [
  { title: 'SPU', dataIndex: 'spu', key: 'spu', width: 120 },
  { title: 'SKC', dataIndex: 'skc', key: 'skc', width: 120 },
  { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 120 },
  { title: '原申报价', dataIndex: 'originalPrice', key: 'originalPrice', width: 100 },
  { title: '官方建议报价', dataIndex: 'suggestedPrice', key: 'suggestedPrice', width: 120 },
  { title: '核价次数', dataIndex: 'reviewTimes', key: 'reviewTimes', width: 100 },
  { title: '重报价格', dataIndex: 'finalPrice', key: 'finalPrice', width: 100 },
  { title: '货号', dataIndex: 'extCode', key: 'extCode', width: 150 },
  { title: '货盘库存', key: 'stock', width: 120 },
  { title: '成本价格', key: 'costPrice', width: 120 },
  { title: '利润率(官方建议价)', key: 'originalProfitRate', width: 150 },
  { title: '重报利润率', key: 'newProfitRate', width: 120 },
  { title: '其他', dataIndex: 'remark', key: 'remark', width: 120 }
]

const rejectedColumns = [
  { title: 'SPU', dataIndex: 'spu', key: 'spu', width: 120 },
  { title: 'SKC', dataIndex: 'skc', key: 'skc', width: 120 },
  { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 120 },
  { title: '原申报价', dataIndex: 'originalPrice', key: 'originalPrice', width: 100 },
  { title: '官方建议报价', dataIndex: 'suggestedPrice', key: 'suggestedPrice', width: 120 },
  { title: '核价次数', dataIndex: 'reviewTimes', key: 'reviewTimes', width: 100 },
  { title: '货号', dataIndex: 'extCode', key: 'extCode', width: 150 },
  { title: '货盘库存', key: 'stock', width: 120 },
  { title: '成本价格', key: 'costPrice', width: 120 },
  { title: '利润率', key: 'profitRate', width: 100 },
  { title: '其他', dataIndex: 'remark', key: 'remark', width: 120 }
]

// 计算利润率
const calculateProfitRate = (sellingPrice: number, costPrice: number): number => {
  if (costPrice <= 0) return 0
  return ((sellingPrice - costPrice) / sellingPrice) * 100
}

// 处理关闭
const handleClose = () => {
  if (!isRunning.value) {
    visible.value = false
    emit('close')
  }
}
</script>

<style scoped>
.auto-pricing-container {
  max-height: 80vh;
  overflow-y: auto;
}

.control-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stats-panel {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.results-panel {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  font-size: 12px;
}

:deep(.ant-table-tbody > tr > td) {
  font-size: 12px;
  padding: 8px;
}

:deep(.ant-tabs-tab) {
  font-weight: 500;
}

:deep(.ant-progress-text) {
  font-weight: 600;
}

/* 替代内联样式的CSS类 */
:deep(.auto-pricing-modal) {
  top: 5% !important;
}
</style>
