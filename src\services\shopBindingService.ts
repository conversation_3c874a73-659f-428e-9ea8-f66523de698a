// 店铺绑定服务
// 处理与后端的店铺绑定相关 API 交互

import { getApiUrl, config } from '../config'

interface TemuSiteInfo {
  fromPlat: string
  mallId: string | number
  shopId: string | number
  mallName: string
  shopName: string
  mallStatus: number
  isSemiManagedMall: boolean
  logo: string
}

interface BindingResponse {
  code: number
  message: string
  data?: {
    bindingId?: string
    userId?: string
    shopInfo?: TemuSiteInfo
    bindTime?: string
    status?: string
    isTemuBound?: boolean
    temuSiteInfo?: TemuSiteInfo
    lastCheckTime?: string
  }
  error?: {
    code: number
    message: string
  }
}

class ShopBindingService {
  private userId: string | null = null
  private shopId: string | null = null

  constructor() {
    this.initializeUserId()
  }

  // 检查响应是否成功
  private isSuccess(response: any): boolean {
    return response && response.code === 200
  }

  // 初始化用户ID（从登录状态或存储中获取）
  private async initializeUserId() {
    try {
      // 尝试从 Chrome Storage 获取用户ID
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['userId', 'userInfo'])
        this.userId = result.userId || result.userInfo?.userId || null
      } else {
        // 降级到 localStorage
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const parsed = JSON.parse(userInfo)
          this.userId = parsed.userId || null
        }
      }
    } catch (error) {
      console.error('[ShopBindingService] 初始化用户ID失败:', error)
    }
  }

  // 设置用户ID
  public setUserId(userId: string) {
    this.userId = userId
  }

  // 设置店铺ID
  public setShopId(shopId: string) {
    this.shopId = shopId
  }

  // 获取店铺ID
  public getShopId(): string | null {
    return this.shopId
  }

  // 获取请求头
  private getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      // 在实际项目中，这里应该添加认证token
      // 'Authorization': `Bearer ${this.getAuthToken()}`
    }
  }

  // 绑定店铺到后端
  public async bindShop(shopInfo: TemuSiteInfo): Promise<BindingResponse> {
    try {
      if (!this.userId) {
        return {
          code: 404,
          message: '用户未登录，请先登录',
          error: { code: 4001, message: 'User not authenticated' }
        }
      }

      console.info('[ShopBindingService] 开始绑定店铺到后端:', shopInfo)

      const response = await fetch(getApiUrl('/dmj/shop/bind'), {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          userId: this.userId,
          shopInfo
        })
      })

      const result = await response.json()

      if (response.ok && (result.code === 200 || (result.code === 400 && result.msg && result.msg.includes('用户已绑定该平台店铺')))) {
        if (result.code === 200) {
          console.info('[ShopBindingService] 店铺绑定成功:', result)
        } else {
          console.info('[ShopBindingService] 店铺已绑定（视为成功）:', result)
        }
        return {
          ...result,
          code: 200, // 统一返回成功状态码
          message: result.code === 200 ? result.message : '店铺已绑定'
        }
      } else {
        console.error('[ShopBindingService] 店铺绑定失败:', result)
        return {
          code: 403,
          message: result.message || result.msg || '绑定失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[ShopBindingService] 绑定请求失败:', error)
      return {
        code: 401,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 检查绑定状态
  public async checkBindingStatus(): Promise<BindingResponse> {
    try {
      if (!this.shopId) {
        return {
          code: 4001,
          message: '店铺ID未设置',
          error: { code: 4001, message: 'Shop ID not set' }
        }
      }

      console.info('[ShopBindingService] 检查绑定状态...')

      const response = await fetch(getApiUrl(`/dmj/shop/binding-status?shopId=${this.shopId}`), {
        method: 'GET',
        headers: this.getHeaders()
      })

      const result = await response.json()

      if (response.ok && result.code===200) {
        console.info('[ShopBindingService] 绑定状态检查成功:', result)
        return result
      } else {
        console.error('[ShopBindingService] 绑定状态检查失败:', result)
        return {
          code: result.code || response.status || 500,
          message: result.message || '检查失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[ShopBindingService] 状态检查请求失败:', error)
      return {
        code: 5000,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 解绑店铺
  public async unbindShop(mallId: string | number): Promise<BindingResponse> {
    try {
      if (!this.userId) {
        return {
          code: 4001,
          message: '用户未登录',
          error: { code: 4001, message: 'User not authenticated' }
        }
      }

      console.info('[ShopBindingService] 开始解绑店铺:', mallId)

      const response = await fetch(getApiUrl('/dmj/shop/unbind'), {
        method: 'DELETE',
        headers: this.getHeaders(),
        body: JSON.stringify({
          userId: this.userId,
          mallId
        })
      })

      const result = await response.json()

      if (response.ok && result.code === 200) {
        console.info('[ShopBindingService] 店铺解绑成功:', result)
        return result
      } else {
        console.error('[ShopBindingService] 店铺解绑失败:', result)
        return {
          code: result.code || response.status || 500,
          message: result.message || '解绑失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[ShopBindingService] 解绑请求失败:', error)
      return {
        code: 5000,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 验证店铺信息
  public async verifyShop(shopInfo: Partial<TemuSiteInfo>): Promise<BindingResponse> {
    try {
      console.info('[ShopBindingService] 验证店铺信息:', shopInfo)

      const response = await fetch(getApiUrl('/dmj/shop/binding/verify'), {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ shopInfo })
      })

      const result = await response.json()

      if (response.ok && result.code === 200) {
        console.info('[ShopBindingService] 店铺验证成功:', result)
        return result
      } else {
        console.error('[ShopBindingService] 店铺验证失败:', result)
        return {
          code: result.code || response.status || 500,
          message: result.message || '验证失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[ShopBindingService] 验证请求失败:', error)
      return {
        code: 5000,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }


}

// 创建单例实例
export const shopBindingService = new ShopBindingService()
export default shopBindingService
