@import "../../assets/base.css";

/* 修复下拉菜单显示问题 */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* 确保下拉菜单内容可见 */
.ui-select-content,
[data-radix-select-content] {
  z-index: 9999 !important;
  position: fixed !important;
}

/* 修复可能的容器溢出问题 */
.ui-select-trigger,
[data-radix-select-trigger] {
  position: relative;
}

/* 确保下拉菜单不被遮挡 */
body {
  overflow: visible !important;
}

#app {
  overflow: visible !important;
}

/* 移除可能导致aria-hidden问题的属性 */
[aria-hidden="true"] {
  aria-hidden: false !important;
}
