/* Amazon采集器样式 - CSP兼容版本 */

/* 采集按钮悬停效果 */
.amazon-collect-btn-hover {
  background: #ff7875 !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4) !important;
}

/* 已采集商品样式 */
.amazon-collected {
  opacity: 0.6 !important;
  border: 2px solid #52c41a !important;
}

.amazon-collected-container {
  position: relative !important;
}

.amazon-collected-badge {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  background: #52c41a !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 10px !important;
  z-index: 10 !important;
}

.amazon-collected-badge-multi {
  background: #1890ff !important;
}

/* 批量采集按钮状态 */
.amazon-batch-collect-btn-loading {
  background: #faad14 !important;
  cursor: not-allowed !important;
}

.amazon-batch-collect-btn-idle {
  background: #ff4d4f !important;
  cursor: pointer !important;
}

/* 标签页样式 */
.amazon-tab-active {
  border-bottom-color: #ff4d4f !important;
  color: #ff4d4f !important;
}

.amazon-tab-content-active {
  display: block !important;
}

/* 通知样式 */
.amazon-notification {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important;
  padding: 12px 16px !important;
  border-radius: 6px !important;
  color: white !important;
  font-size: 14px !important;
  max-width: 300px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;
  transform: translateX(100%) !important;
}

.amazon-notification-show {
  transform: translateX(0) !important;
}

.amazon-notification-success {
  background: #52c41a !important;
}

.amazon-notification-error {
  background: #ff4d4f !important;
}

.amazon-notification-warning {
  background: #faad14 !important;
}

.amazon-notification-info {
  background: #1677ff !important;
}
