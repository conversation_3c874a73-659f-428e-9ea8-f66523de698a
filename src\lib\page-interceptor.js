// ================================================================
//  页面环境拦截器 (page-interceptor.js)
//  此文件将通过 manifest.json 的 "world": "MAIN" 直接注入页面
// ================================================================

(function() {
  // 防止重复注入
  if (window.hasTemuInterceptorInjected) {
    return;
  }
  window.hasTemuInterceptorInjected = true;

  console.log('🚀 [Page-Interceptor] 脚本已注入页面主环境，开始设置拦截器...');

  const storeToLocalStorage = (key, value, expiryMinutes) => {
    try {
      const expiry = Date.now() + expiryMinutes * 60 * 1000;
      localStorage.setItem(`temu_cs_${key}`, value);
      localStorage.setItem(`temu_cs_${key}_expiry`, expiry.toString());
      // 可选：发送一个自定义事件，通知内容脚本数据已更新
      // window.dispatchEvent(new CustomEvent('temuHeaderCaptured', { detail: { key, value } }));
    } catch (e) {
      console.warn(`[Page-Interceptor] 保存 ${key} 到 localStorage 失败:`, e);
    }
  };

  const checkAndStoreHeaders = (headers, source) => {
    const lowerCaseHeaders = {};
    for (const key in headers) {
      lowerCaseHeaders[key.toLowerCase()] = headers[key];
    }

    const antiContent = lowerCaseHeaders['anti-content'];
    const mallId = lowerCaseHeaders['mallid'];

    // 捕获其他可能重要的认证头部
    const authorization = lowerCaseHeaders['authorization'];
    const xRequestedWith = lowerCaseHeaders['x-requested-with'];
    const xCsrfToken = lowerCaseHeaders['x-csrf-token'];
    const sessionId = lowerCaseHeaders['session-id'] || lowerCaseHeaders['sessionid'];

    if (antiContent) {
      console.log(`🎉 [Page-Interceptor] 捕获到 anti-content (来自 ${source})!`);
      storeToLocalStorage('anti_content', antiContent, 30); // 30分钟有效期
    }

    if (mallId) {
      console.log(`🎉 [Page-Interceptor] 捕获到 mallId (来自 ${source})!`);
      storeToLocalStorage('mall_id', mallId, 60); // 60分钟有效期
    }

    // 存储其他认证相关头部
    if (authorization) {
      console.log(`🎉 [Page-Interceptor] 捕获到 authorization (来自 ${source})!`);
      storeToLocalStorage('authorization', authorization, 30);
    }

    if (xRequestedWith) {
      console.log(`🎉 [Page-Interceptor] 捕获到 x-requested-with (来自 ${source})!`);
      storeToLocalStorage('x_requested_with', xRequestedWith, 60);
    }

    if (xCsrfToken) {
      console.log(`🎉 [Page-Interceptor] 捕获到 x-csrf-token (来自 ${source})!`);
      storeToLocalStorage('x_csrf_token', xCsrfToken, 30);
    }

    if (sessionId) {
      console.log(`🎉 [Page-Interceptor] 捕获到 session-id (来自 ${source})!`);
      storeToLocalStorage('session_id', sessionId, 60);
    }
  };

  // 1. 拦截 fetch
  const originalFetch = window.fetch;
  window.fetch = function (...args) {
    const [url, options] = args;
    const urlString = typeof url === 'string' ? url : url.toString();

    // 检查请求头，覆盖更广的域名
    if (urlString.includes('kuajingmaihuo.com') || urlString.includes('temu.com')) {
      if (options && options.headers) {
        const headersObj = {};
        if (options.headers instanceof Headers) {
          options.headers.forEach((value, key) => {
            headersObj[key] = value;
          });
        } else {
          Object.assign(headersObj, options.headers);
        }
        checkAndStoreHeaders(headersObj, 'fetch');
      }
    }
    return originalFetch.apply(this, args);
  };

  // 2. 拦截 XHR
  const originalXHRSetHeader = XMLHttpRequest.prototype.setRequestHeader;
  XMLHttpRequest.prototype.setRequestHeader = function (name, value) {
    const lowerName = name.toLowerCase();
    // 捕获所有可能重要的认证头部
    const importantHeaders = [
      'anti-content', 'mallid', 'authorization',
      'x-requested-with', 'x-csrf-token', 'session-id', 'sessionid'
    ];

    if (importantHeaders.includes(lowerName)) {
        checkAndStoreHeaders({ [name]: value }, 'XHR-SetHeader');
    }
    return originalXHRSetHeader.call(this, name, value);
  };
  
  console.log('✅ [Page-Interceptor] 所有拦截器设置完毕，正在监听所有网络请求...');
})();