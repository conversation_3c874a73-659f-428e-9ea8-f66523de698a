import process from "node:process"
import type { ManifestV3Export } from "@crxjs/vite-plugin"
import packageJson from "./package.json" with { type: "json" }

const { version, name, description, displayName } = packageJson
// Convert from Semver (example: 0.1.0-beta6)
const [major, minor, patch, label = "0"] = version
  // can only contain digits, dots, or dash
  .replace(/[^\d.-]+/g, "")
  // split into version parts
  .split(/[.-]/)

export default {
  author: {
    email: "<EMAIL>",
  },
  name: process.env.NODE_ENV === "staging" ? `[INTERNAL] ${name}` : displayName || name,
  description,
  // up to four numbers separated by dots
  version: `${major}.${minor}.${patch}.${label}`,
  // semver is OK in "version_name"
  version_name: version,
  manifest_version: 3,
  // key: '',
  action: {
    default_popup: "src/ui/action-popup/index.html",
  },
  background: {
    service_worker: "src/background/index.ts",
    type: "module",
  },
  content_scripts: [
    {
      all_frames: false,
      js: ["src/content-scripts/index.ts"],
      matches: ["*://*/*"],
      run_at: "document_end",
    },
   
    {
      "comment": "页面环境拦截器: 在MAIN world中拦截网络请求，提取认证信息",
      "matches": [
        "*://seller.temu.com/*",
        "*://seller.kuajingmaihuo.com/*",
        "*://seller-cn.temu.com/*",
        "*://agentseller.temu.com/*",
        "*://agentseller-us.temu.com/*",
        "*://*.temu.com/seller/*",
        "*://*.temu.com/agentseller/*"
      ],
      "js": ["src/lib/page-interceptor.js"],
      "run_at": "document_start",
      "world": "MAIN"
    },
    
    {
      "comment": "Temu统一Content Script: 处理消息通信和API代理",
      "matches": [
        "*://seller.temu.com/*",
        "*://seller.kuajingmaihuo.com/*",
        "*://seller-cn.temu.com/*",
        "*://agentseller.temu.com/*",
        "*://agentseller-us.temu.com/*",
        "*://*.temu.com/seller/*",
        "*://*.temu.com/agentseller/*"
      ],
      "js": [
        "src/content-scripts/temu/temu-unified.ts"
      ],
      "run_at": "document_start"
    },

    {
      all_frames: false,
      js: ["src/content-scripts/amazon/amazon-collector.ts"],
      matches: [
        "*://www.amazon.com/*",
        "*://amazon.com/*",
        "*://*.amazon.com/*"
      ],
      run_at: "document_end",
    },    {
      comment: "店小秘配置注入器: 仅在特定页面运行",
      all_frames: false,
      js: ["src/content-scripts/dxm/dianxiaomi-config-injector.ts"],
      matches: [
        "*://www.dianxiaomi.com/userTemplate/popTemuAdd.htm*",
        "*://www.dianxiaomi.com/userTemplate/popTemuEdit.htm*",
        "*://dianxiaomi.com/userTemplate/popTemuAdd.htm*",
        "*://dianxiaomi.com/userTemplate/popTemuEdit.htm*",
        "*://*.dianxiaomi.com/userTemplate/popTemuAdd.htm*",
        "*://*.dianxiaomi.com/userTemplate/popTemuEdit.htm*"
      ],
      run_at: "document_idle",
    },
    {
      comment: "店小秘API处理器: 在其他店小秘页面运行",
      all_frames: false,
      js: ["src/content-scripts/dxm/dianxiaomi-api-handler.ts"],
      matches: [
        "*://www.dianxiaomi.com/*",
        "*://dianxiaomi.com/*",
        "*://*.dianxiaomi.com/*"
      ],
      exclude_matches: [
        "*://www.dianxiaomi.com/userTemplate/popTemuAdd.htm*",
        "*://www.dianxiaomi.com/userTemplate/popTemuEdit.htm*",
        "*://dianxiaomi.com/userTemplate/popTemuAdd.htm*",
        "*://dianxiaomi.com/userTemplate/popTemuEdit.htm*",
        "*://*.dianxiaomi.com/userTemplate/popTemuAdd.htm*",
        "*://*.dianxiaomi.com/userTemplate/popTemuEdit.htm*"
      ],
      run_at: "document_end",
    },
  ],
  side_panel: {
    default_path: "src/ui/side-panel/index.html",
  },
  options_page: "src/ui/options-page/index.html",
  offline_enabled: true,
  host_permissions: ["<all_urls>"],
  permissions: ["storage", "tabs", "background", "sidePanel", "scripting", "cookies"],
  web_accessible_resources: [
    {
      resources: [
        "src/ui/setup/index.html",
        "src/lib/page-interceptor.js",
        "src/lib/dianxiaomi-main-world-injector.js",
      ],
      matches: ["<all_urls>"],
      use_dynamic_url: false,
    },
  ],
  icons: {
    16: "src/assets/logo.png",
    24: "src/assets/logo.png",
    32: "src/assets/logo.png",
    128: "src/assets/logo.png",
  },
  content_security_policy: {
    extension_pages: "script-src 'self' 'wasm-unsafe-eval' http://localhost:* http://127.0.0.1:*; object-src 'self'; style-src 'self' 'unsafe-inline'; connect-src 'self' http://localhost:* http://127.0.0.1:* ws://localhost:* ws://127.0.0.1:* https://seller.kuajingmaihuo.com https://seller.temu.com https://www.dianxiaomi.com https://dianxiaomi.com https://www.amazon.com https://amazon.com https://*.amazon.com https://*.myqcloud.com;"
  },
} as ManifestV3Export
