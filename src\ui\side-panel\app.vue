<script setup lang="ts">
import TopNavbar from '../../components/TopNavbar.vue'
</script>

<template>
  <a-layout class="extension-layout">
    <!-- 顶部导航栏 - 始终显示 -->
    <TopNavbar />

    <!-- 主内容区域 -->
    <a-layout-content class="extension-main-content">
      <RouterView />
    </a-layout-content>
  </a-layout>
</template>

<style scoped>
/* ========================================
   Chrome Extension 主布局样式
======================================== */

.extension-layout {
  height: 100vh;
  background: var(--bg-secondary);
}

.extension-main-content {
  flex: 1;
  overflow: hidden;
  background: var(--bg-secondary);
}

/* 确保路由视图占满容器 */
:deep(.router-view) {
  height: 100%;
  overflow: hidden;
}
</style>
