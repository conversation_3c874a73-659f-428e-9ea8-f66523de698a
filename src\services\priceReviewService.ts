/**
 * 价格核价服务
 * 负责处理Temu价格核价相关的API调用
 */

import productListService from './productListService'
import temuApiService from './temu/temu-api'

// 核价商品信息接口
export interface PriceReviewItem {
  skcId: number
  priceOrderSn: string
  autoRejectSimilar: boolean
  isApparel: boolean
  priceBeforeExchange: number
  skuInfoList: SkuInfo[]
  productName: string
  currencyName: string | null
  cat: CategoryInfo
  catName: string
  semiHostedBindSiteList: SiteInfo[]
  suggestSupplyPrice: number
  id: number
  reviewTimes: number
  image: string
  canAppealTime: string | null
  productSkcExtCode: string
  productId: number
  exchangeDesc: string | null
  supplyPriceCNY: number | null
  rejectRemark: string
  retainPriceOrderId: number | null
  semiHosted: boolean
  catId: number | null
  priceCurrency: string
  semiHostedBindSiteIdList: number[]
  suggestPriceCurrency: string
  canAppeal: boolean
  semiHostedBindSiteNameList: string[]
  ratio: number | null
}

export interface SkuInfo {
  productSkuId: number
  priceCurrency: string
  currencyName: string
  exchangeDesc: string | null
  supplyPriceCNY: number | null
  suggestPriceCurrency: string
  suggestSupplyPrice: number
  productSkuExtCode: string
  priceBeforeExchange: number
  spec: string
  ratio: number | null
}

export interface CategoryInfo {
  cat7Id: number
  cat8Id: number
  cat9Id: number
  cat7Name: string
  cat10Name: string
  cat3Id: number
  cat4Id: number
  cat9Name: string
  cat5Id: number
  cat6Id: number
  cat1Id: number
  cat2Id: number
  cat6Name: string
  cat4Name: string
  cat2Name: string
  cat8Name: string
  catId: number
  catName: string
  cat10Id: number
  cat5Name: string
  cat1Name: string
  cat3Name: string
}

export interface SiteInfo {
  siteName: string
  siteId: number
}

// 核价结果接口
export interface PriceReviewResult {
  priceReviewItemList: PriceReviewItem[]
  canBargainTime: string | null
  canBargain: boolean
}

// 批量操作结果接口
export interface BatchOperateResult {
  [priceOrderId: string]: {
    priceOrderId: number
    success: boolean
    failDesc: string
  }
}

// 核价操作请求接口
export interface PricingOperationRequest {
  priceOrderId: number
  supplierResult: number // 1=同意申报价, 2=重新报价, 3=放弃
  items: {
    productSkuId: number
    price?: number // 重新报价时需要，同意申报价时可选
  }[]
}

// 重新报价请求接口（向后兼容）
export interface RepricingRequest extends PricingOperationRequest {}

export class PriceReviewService {
  private static instance: PriceReviewService

  static getInstance(): PriceReviewService {
    if (!PriceReviewService.instance) {
      PriceReviewService.instance = new PriceReviewService()
    }
    return PriceReviewService.instance
  }

  /**
   * 获取核价确认信息
   */
  async getPriceReviewInfo(orderIds: number[]): Promise<PriceReviewResult> {
    try {
      console.info('[PriceReviewService] 获取核价确认信息:', orderIds)

      const response = await temuApiService.makeRequest(
        'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch/info/query',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: {
            orderIds: orderIds
          }
        }
      )

      if (response.success && response.data) {
        console.info('[PriceReviewService] 核价信息获取成功:', response.data)
        return response.data as PriceReviewResult
      } else {
        throw new Error(response.error || '获取核价信息失败')
      }
    } catch (error) {
      console.error('[PriceReviewService] 获取核价信息失败:', error)
      throw error
    }
  }

  /**
   * 批量核价操作（统一接口）
   */
  async batchPricingOperation(requests: PricingOperationRequest[]): Promise<BatchOperateResult> {
    try {
      console.info('[PriceReviewService] 批量核价操作:', requests)

      // 验证请求格式
      this.validateRequestFormat(requests)

      // 构造请求体，完全按照API示例格式
      const requestBody = {
        itemRequests: requests
      }

      console.info('[PriceReviewService] 请求体格式:', JSON.stringify(requestBody, null, 2))

      const response = await temuApiService.makeRequest(
        'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: requestBody
        }
      )

      // 检查API层面的成功：success: true 和 errorCode: 1000000
      if (response.success && response.data && response.data.success && response.data.errorCode === 1000000) {
        console.info('[PriceReviewService] API调用成功:', response.data)

        // 检查是否有业务结果数据
        if (response.data.result && response.data.result.batchOperateResult) {
          console.info('[PriceReviewService] 业务操作结果:', response.data.result.batchOperateResult)
          return response.data.result.batchOperateResult as BatchOperateResult
        } else {
          // API成功但没有业务结果，可能是某些特殊情况
          console.warn('[PriceReviewService] API成功但缺少batchOperateResult')
          throw new Error('API调用成功但缺少业务操作结果')
        }
      } else {
        // API层面失败
        const errorMsg = response.data?.errorMsg || response.error || '批量核价操作失败'
        console.error('[PriceReviewService] API调用失败:', {
          success: response.success,
          errorCode: response.data?.errorCode,
          errorMsg: response.data?.errorMsg,
          responseError: response.error
        })
        throw new Error(errorMsg)
      }
    } catch (error) {
      console.error('[PriceReviewService] 批量核价操作失败:', error)
      throw error
    }
  }

  /**
   * 批量重新报价（向后兼容）
   */
  async batchRepricing(requests: RepricingRequest[]): Promise<BatchOperateResult> {
    return this.batchPricingOperation(requests)
  }

  /**
   * 批量放弃报价
   */
  async batchRejectPricing(priceOrderIds: number[], productSkuIds: number[]): Promise<BatchOperateResult> {
    try {
      console.info('[PriceReviewService] 批量放弃报价:', { priceOrderIds, productSkuIds })

      const requests: PricingOperationRequest[] = priceOrderIds.map((priceOrderId, index) => ({
        priceOrderId: priceOrderId,
        supplierResult: 3, // 3=放弃，拒绝上新
        items: [{
          productSkuId: productSkuIds[index] || 0
          // 放弃时不需要价格信息
        }]
      }))

      return await this.batchPricingOperation(requests)
    } catch (error) {
      console.error('[PriceReviewService] 批量放弃报价失败:', error)
      throw error
    }
  }

  /**
   * 单个商品同意申报价
   */
  async singleAcceptPrice(
    priceOrderId: number,
    productSkuId: number,
    acceptedPrice: number
  ): Promise<boolean> {
    try {
      console.info('[PriceReviewService] 单个商品同意申报价:', {
        priceOrderId,
        productSkuId,
        acceptedPrice
      })

      // 构造请求，完全按照API示例格式
      const request: PricingOperationRequest = {
        priceOrderId: priceOrderId,
        supplierResult: 1, // 1=同意申报价
        items: [{
          productSkuId: productSkuId,
          price: acceptedPrice // 必须包含价格，使用官方申报价
        }]
      }

      const result = await this.batchPricingOperation([request])
      const operateResult = result[priceOrderId.toString()]

      console.info('[PriceReviewService] 单个商品同意申报价结果:', operateResult)

      if (operateResult && operateResult.success) {
        console.info('[PriceReviewService] 单个商品同意申报价成功')
        return true
      } else {
        const failDesc = operateResult?.failDesc || '未知错误'
        console.warn('[PriceReviewService] 单个商品同意申报价失败:', failDesc)
        throw new Error(`同意申报价失败: ${failDesc}`)
      }
    } catch (error) {
      console.error('[PriceReviewService] 单个商品同意申报价失败:', error)
      return false
    }
  }

  /**
   * 单个商品重新报价
   */
  async singleRepricing(
    priceOrderId: number,
    productSkuId: number,
    newPrice: number
  ): Promise<boolean> {
    try {
      console.info('[PriceReviewService] 单个商品重新报价:', {
        priceOrderId,
        productSkuId,
        newPrice
      })

      // 构造请求，完全按照API示例格式
      const request: PricingOperationRequest = {
        priceOrderId: priceOrderId,
        supplierResult: 2, // 2=重新报价
        items: [{
          productSkuId: productSkuId,
          price: newPrice // 必须包含新的价格
        }]
      }

      const result = await this.batchPricingOperation([request])
      const operateResult = result[priceOrderId.toString()]

      console.info('[PriceReviewService] 单个商品重新报价结果:', operateResult)

      if (operateResult && operateResult.success) {
        console.info('[PriceReviewService] 单个商品重新报价成功')
        return true
      } else {
        const failDesc = operateResult?.failDesc || '未知错误'
        console.warn('[PriceReviewService] 单个商品重新报价失败:', failDesc)
        throw new Error(`重新报价失败: ${failDesc}`)
      }
    } catch (error) {
      console.error('[PriceReviewService] 单个商品重新报价失败:', error)
      return false
    }
  }

  /**
   * 单个商品放弃报价
   */
  async singleRejectPricing(priceOrderId: number, productSkuId: number): Promise<boolean> {
    try {
      console.info('[PriceReviewService] 单个商品放弃报价:', { priceOrderId, productSkuId })

      // 构造请求，完全按照API示例格式
      const request: PricingOperationRequest = {
        priceOrderId: priceOrderId,
        supplierResult: 3, // 3=放弃，拒绝上新
        items: [{
          productSkuId: productSkuId
          // 放弃时不包含price字段，与API示例一致
        }]
      }

      const result = await this.batchPricingOperation([request])
      const operateResult = result[priceOrderId.toString()]

      console.info('[PriceReviewService] 单个商品放弃报价结果:', operateResult)

      if (operateResult && operateResult.success) {
        console.info('[PriceReviewService] 单个商品放弃报价成功')
        return true
      } else {
        const failDesc = operateResult?.failDesc || '未知错误'
        console.warn('[PriceReviewService] 单个商品放弃报价失败:', failDesc)
        throw new Error(`放弃上新失败: ${failDesc}`)
      }
    } catch (error) {
      console.error('[PriceReviewService] 单个商品放弃报价失败:', error)
      return false
    }
  }

  /**
   * 计算利润率
   */
  calculateProfitRate(sellingPrice: number, costPrice: number): number {
    if (costPrice <= 0) return 0
    return ((sellingPrice - costPrice) / sellingPrice) * 100
  }

  /**
   * 格式化价格（分转元）
   */
  formatPrice(priceInCents: number): number {
    return priceInCents / 100
  }

  /**
   * 格式化价格（元转分）
   */
  formatPriceToCents(priceInYuan: number): number {
    return Math.round(priceInYuan * 100)
  }

  /**
   * 验证请求格式（调试用）
   */
  validateRequestFormat(requests: PricingOperationRequest[]): void {
    console.info('[PriceReviewService] 验证请求格式...')

    requests.forEach((request, index) => {
      console.info(`[PriceReviewService] 请求 ${index + 1}:`, {
        priceOrderId: request.priceOrderId,
        supplierResult: request.supplierResult,
        items: request.items
      })

      // 验证必要字段
      if (!request.priceOrderId) {
        console.error(`[PriceReviewService] 请求 ${index + 1} 缺少 priceOrderId`)
      }

      if (!request.supplierResult || ![1, 2, 3].includes(request.supplierResult)) {
        console.error(`[PriceReviewService] 请求 ${index + 1} supplierResult 无效:`, request.supplierResult)
      }

      if (!request.items || request.items.length === 0) {
        console.error(`[PriceReviewService] 请求 ${index + 1} 缺少 items`)
      } else {
        request.items.forEach((item, itemIndex) => {
          if (!item.productSkuId) {
            console.error(`[PriceReviewService] 请求 ${index + 1} item ${itemIndex + 1} 缺少 productSkuId`)
          }

          // 同意申报价和重新报价必须有价格
          if ([1, 2].includes(request.supplierResult) && !item.price) {
            console.error(`[PriceReviewService] 请求 ${index + 1} item ${itemIndex + 1} 缺少 price (supplierResult=${request.supplierResult})`)
          }

          // 放弃报价不应该有价格
          if (request.supplierResult === 3 && item.price) {
            console.warn(`[PriceReviewService] 请求 ${index + 1} item ${itemIndex + 1} 不应该包含 price (supplierResult=3)`)
          }
        })
      }
    })

    console.info('[PriceReviewService] 请求格式验证完成')
  }
}

// 导出单例实例
export const priceReviewService = PriceReviewService.getInstance()
