/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AmazonDataPreviewModal: typeof import('./../components/AmazonDataPreviewModal.vue')['default']
    AppFooter: typeof import('./../components/AppFooter.vue')['default']
    AppHeader: typeof import('./../components/AppHeader.vue')['default']
    AutoPricingModal: typeof import('./../components/AutoPricingModal.vue')['default']
    DisplayError: typeof import('./../components/state/DisplayError.vue')['default']
    LoadingSpinner: typeof import('./../components/state/LoadingSpinner.vue')['default']
    LoginModal: typeof import('./../components/LoginModal.vue')['default']
    NotificationToast: typeof import('./../components/NotificationToast.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterLinkUp: typeof import('./../components/RouterLinkUp.vue')['default']
    RouterView: typeof import('vue-router')['RouterView']
    TailwindEmptyState: typeof import('./../components/state/tailwind-empty-state.vue')['default']
    TemuNotFound: typeof import('./../components/TemuNotFound.vue')['default']
    ThemeSwitch: typeof import('./../components/ThemeSwitch.vue')['default']
    TopNavbar: typeof import('./../components/TopNavbar.vue')['default']
    UApp: typeof import('./../../node_modules/.pnpm/@nuxt+ui@3.2.0_@babel+parse_9d95cca0c04b99ab17cf8427cc7e5d76/node_modules/@nuxt/ui/dist/runtime/components/App.vue')['default']
    UButton: typeof import('./../../node_modules/.pnpm/@nuxt+ui@3.2.0_@babel+parse_9d95cca0c04b99ab17cf8427cc7e5d76/node_modules/@nuxt/ui/dist/runtime/components/Button.vue')['default']
  }
}
