// Background script for Temu extension
// 导入JSZip库以支持ZIP文件创建
import '../lib/jszip.min.js'

// 导入服务模块
import dataMapperService from '../services/dataMapperService'
import configStorageService from '../services/configStorageService'

// 导入重构后的服务
import AmazonDataService from '../services/amazon/amazonDataService'
import { dianxiaomiApiService } from '../services/dxm/apiService'
import { dianxiaomiImageUploadService } from '../services/dxm/imageUploadService'
import { amazonDataAssemblyService } from '../services/amazon/dataAssemblyService'

// 声明JSZip全局变量
declare const JSZip: any

chrome.runtime.onInstalled.addListener(async (opt) => {
  if (opt.reason === "install") {
    // 首次安装时，打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则打开 setup 页面
      chrome.tabs.create({
        active: true,
        url: chrome.runtime.getURL("src/ui/setup/index.html#/setup/install"),
      })
    }
    return
  }
})

// 店小秘配置管理器
class DianXiaoMiConfigManager {
  private configData: any = null
  private systemData: any = null

  // 保存配置数据
  saveConfigData(data: any) {
    this.configData = data
    console.info('[DianXiaoMiConfigManager] 配置数据已保存:', data)
  }

  // 保存系统数据
  saveSystemData(data: any) {
    this.systemData = data
    console.info('[DianXiaoMiConfigManager] 系统数据已保存:', data)
  }

  // 获取完整数据
  getCompleteData() {
    return {
      configData: this.configData,
      systemData: this.systemData
    }
  }

  // 清理数据
  clearData() {
    this.configData = null
    this.systemData = null
    console.info('[DianXiaoMiConfigManager] 数据已清理')
  }
}

// 创建配置管理器实例
const dxmConfigManager = new DianXiaoMiConfigManager()

// 注意：消息监听器已合并到下面的主要监听器中

// 处理店小秘配置数据
async function handleDXMConfigData(message: any, sender: any, sendResponse: any) {
  try {
    console.info('[Background] 处理店小秘配置数据:', message)

    // 保存配置数据
    dxmConfigManager.saveConfigData(message.configData)
    dxmConfigManager.saveSystemData(message.systemData)

    // 从localStorage中提取额外数据
    const localStorageData = message.localStorageData || {}

    // 提取关键信息
    const extractedInfo = {
      timestamp: message.timestamp,
      url: message.url,
      systemData: message.systemData,
      configData: message.configData,
      localStorageData: localStorageData,
      summary: {
        hasSystemData: !!message.systemData,
        hasConfigData: !!message.configData,
        hasSubmitDataResult: !!localStorageData.submitDataResult,
        hasAttributes: !!(message.configData?.attributes || localStorageData.submitDataResult?.attributes),
        categoryId: message.configData?.categoryId || localStorageData.submitDataResult?.categoryId,
        shopId: message.configData?.shopId || localStorageData.submitDataResult?.shopId
      }
    }

    console.info('[Background] 提取的关键信息:', extractedInfo)

    // 发送响应
    sendResponse({
      success: true,
      message: '配置数据已成功处理',
      data: extractedInfo
    })

  } catch (error) {
    console.error('[Background] 处理配置数据失败:', error)
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : '处理失败'
    })
  }
}

// 处理手动注入MAIN world脚本
async function handleInjectMainWorldScript(message: any, sender: any, sendResponse: any) {
  try {
    console.info('[Background] 手动注入MAIN world脚本到标签页:', sender.tab?.id)

    if (!sender.tab?.id) {
      throw new Error('无法获取标签页ID')
    }

    // 使用 chrome.scripting API 注入到 MAIN world
    await chrome.scripting.executeScript({
      target: { tabId: sender.tab.id },
      world: 'MAIN',
      func: () => {
        // MAIN World 脚本内容
        if (!(window as any).hasDxmMainInjector) {
          (window as any).hasDxmMainInjector = true;

          console.info('[DXM MAIN] Main World Injector Loaded (Background Injection).');

          // 设置全局标记
          (window as any).__DXM_MAIN_WORLD_LOADED__ = true;
          console.info('[DXM MAIN] 设置全局标记 __DXM_MAIN_WORLD_LOADED__ = true');

          // 监听来自 ISOLATED World 的命令
          window.addEventListener('dxm-request-data', (event: any) => {
            console.info('[DXM MAIN] Received data request:', event.detail);
            const pushtype = event.detail.pushtype;
            let result = null;
            let success = false;
            let errorMsg = '未找到可用的系统函数。';

            try {
              // 尝试调用系统函数
              const win = window as any;
              if (win.submitData && typeof win.submitData === 'function') {
                console.info('[DXM MAIN] 调用 submitData 函数...');
                result = win.submitData(pushtype);
                success = true;
                errorMsg = '';
              } else if (win.pushData && typeof win.pushData === 'function') {
                console.info('[DXM MAIN] 调用 pushData 函数...');
                result = win.pushData(pushtype);
                success = true;
                errorMsg = '';
              } else {
                console.warn('[DXM MAIN] 未找到可用的系统函数');
                // 尝试查找其他可能的函数
                const possibleFunctions = ['submit', 'push', 'save', 'upload'];
                for (const funcName of possibleFunctions) {
                  if (win[funcName] && typeof win[funcName] === 'function') {
                    console.info(`[DXM MAIN] 尝试调用 ${funcName} 函数...`);
                    try {
                      result = win[funcName](pushtype);
                      success = true;
                      errorMsg = '';
                      break;
                    } catch (e) {
                      console.warn(`[DXM MAIN] 调用 ${funcName} 失败:`, e);
                    }
                  }
                }
              }
            } catch (error) {
              console.error('[DXM MAIN] 调用系统函数失败:', error);
              errorMsg = error instanceof Error ? error.message : '调用失败';
            }

            // 发送结果回 ISOLATED World
            window.dispatchEvent(new CustomEvent('dxm-response-data', {
              detail: {
                success,
                result,
                error: errorMsg,
                pushtype
              }
            }));
          });
        }
      }
    })

    console.info('[Background] MAIN world脚本注入成功')
    sendResponse({
      success: true,
      message: 'MAIN world脚本注入成功'
    })

  } catch (error) {
    console.error('[Background] 注入MAIN world脚本失败:', error)
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : '注入失败'
    })
  }
}

// IndexedDB管理器
class IndexDBManager {
  private dbName = 'TemuExtensionDB'
  private version = 1
  private storeName = 'dataStore'

  async openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'id' })
          store.createIndex('timestamp', 't', { unique: false })
        }
      }
    })
  }

  async saveData(key: string, data: any): Promise<void> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)

    const record = {
      id: key,
      data: data,
      t: Date.now()
    }

    return new Promise((resolve, reject) => {
      const request = store.put(record)
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve()
    })
  }

  async getData(key: string): Promise<any> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readonly')
    const store = transaction.objectStore(this.storeName)

    return new Promise((resolve, reject) => {
      const request = store.get(key)
      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        const result = request.result
        resolve(result ? result.data : null)
      }
    })
  }

  // 清理过期数据（保留最近24小时的数据）
  async cleanExpiredData(): Promise<number> {
    const db = await this.openDB()
    const transaction = db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)

    const expireTime = Date.now() - (24 * 60 * 60 * 1000) // 24小时前
    let deletedCount = 0

    return new Promise((resolve, reject) => {
      const request = store.openCursor()

      request.onerror = () => reject(request.error)
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          const data = cursor.value
          if (data.t && data.t < expireTime) {
            cursor.delete()
            deletedCount++
          }
          cursor.continue()
        } else {
          console.info('[IndexDBManager] 清理过期数据完成，删除了', deletedCount, '条记录')
          resolve(deletedCount)
        }
      }
    })
  }
}

// 完全在background中运行的Amazon价格获取服务
class BackgroundAmazonPriceService {
  private cache = new Map<string, { data: any; timestamp: number }>()
  private readonly CACHE_DURATION = 60 * 60 * 1000 // 1小时缓存
  private activeRequests = new Set<string>() // 跟踪正在进行的请求
  private readonly MAX_CONCURRENT = 3 // 最大并发数

  /**
   * 等待可用的并发槽位
   */
  private async waitForAvailableSlot(): Promise<void> {
    return new Promise((resolve) => {
      const checkSlot = () => {
        if (this.activeRequests.size < this.MAX_CONCURRENT) {
          resolve()
        } else {
          setTimeout(checkSlot, 500) // 每500ms检查一次
        }
      }
      checkSlot()
    })
  }

  /**
   * 获取Amazon页面HTML
   */
  private async fetchAmazonPage(asin: string): Promise<string> {
    const url = `https://www.amazon.com/dp/${asin}`

    console.info('[BackgroundAmazonPriceService] 开始获取Amazon页面:', url)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const html = await response.text()
    console.info('[BackgroundAmazonPriceService] 成功获取Amazon页面，长度:', html.length)

    // 更精确的反爬虫页面检测
    const isRobotCheckPage = (
      html.includes('Robot Check') ||
      html.includes('robot check') ||
      (html.includes('captcha') && html.includes('amazon')) ||
      html.includes('Sorry, we just need to make sure you\'re not a robot') ||
      html.includes('Enter the characters you see below') ||
      html.includes('Type the characters you see in this image') ||
      (html.includes('To discuss automated access') && html.includes('amazon'))
    )

    const isSignInRedirect = (
      html.includes('<title>Amazon Sign-In</title>') ||
      html.includes('ap_signin_notification_message') ||
      (html.includes('Sign in') && html.includes('Your account') && html.length < 50000)
    )

    const isErrorPage = (
      html.includes('Page Not Found') ||
      html.includes('404 - Not Found') ||
      html.includes('Sorry! Something went wrong') ||
      (html.includes('Dogs of Amazon') && html.length < 10000)
    )

    if (isRobotCheckPage) {
      console.warn('[BackgroundAmazonPriceService] 检测到机器人验证页面')
      console.warn('[BackgroundAmazonPriceService] 页面标题:', html.match(/<title[^>]*>([^<]*)<\/title>/i)?.[1] || '未找到标题')
      throw new Error('Amazon返回了机器人验证页面，请在浏览器中访问Amazon并完成验证')
    }

    if (isSignInRedirect) {
      console.warn('[BackgroundAmazonPriceService] 检测到登录重定向页面')
      console.warn('[BackgroundAmazonPriceService] 页面标题:', html.match(/<title[^>]*>([^<]*)<\/title>/i)?.[1] || '未找到标题')
      throw new Error('Amazon要求登录，请在浏览器中登录Amazon账户')
    }

    if (isErrorPage) {
      console.warn('[BackgroundAmazonPriceService] 检测到错误页面')
      console.warn('[BackgroundAmazonPriceService] 页面标题:', html.match(/<title[^>]*>([^<]*)<\/title>/i)?.[1] || '未找到标题')
      throw new Error('Amazon返回了错误页面，可能商品不存在或链接无效')
    }

    // 检查页面是否正常加载（降低阈值，因为有些页面可能比较简洁）
    if (html.length < 500) {
      console.warn('[BackgroundAmazonPriceService] 页面内容过短，可能加载失败')
      console.warn('[BackgroundAmazonPriceService] 完整页面内容:', html)
      throw new Error(`Amazon页面内容异常，长度仅${html.length}字符`)
    }

    // 检查是否为正常的商品页面
    const isValidProductPage = (
      html.includes('data-asin=') ||
      html.includes('dp/') ||
      html.includes('product') ||
      html.includes('buybox') ||
      html.includes('a-price')
    )

    if (!isValidProductPage) {
      console.warn('[BackgroundAmazonPriceService] 页面不是有效的商品页面')
      console.warn('[BackgroundAmazonPriceService] 页面标题:', html.match(/<title[^>]*>([^<]*)<\/title>/i)?.[1] || '未找到标题')
      // 不抛出错误，继续尝试解析
    }

    return html
  }

  /**
   * 从HTML中提取价格和库存信息
   */
  private extractPriceFromHtml(html: string, asin: string): { price: number; stock: number; imageUrl: string } {
    let price = 0
    let stock = 0
    let imageUrl = ''

    console.info('[BackgroundAmazonPriceService] 开始解析HTML，ASIN:', asin)

    // 价格提取模式（按优先级排序）
    const pricePatterns = [
      // 主要价格模式
      /<span[^>]*class="[^"]*a-price-current[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/s,
      /<span[^>]*class="[^"]*a-price[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/s,
      // 备用价格模式
      /<span[^>]*id="priceblock_dealprice"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/,
      /<span[^>]*id="priceblock_ourprice"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/,
      /<span[^>]*class="[^"]*a-price-whole[^"]*"[^>]*>([0-9,]+)<\/span>/,
      // JSON数据中的价格
      /"priceAmount":([0-9.]+)/,
      /"displayPrice":"?\$?([0-9,]+\.?[0-9]*)"/
    ]

    // 库存提取模式
    const stockPatterns = [
      /<span[^>]*id="availability"[^>]*>.*?(\d+)[^<]*in stock/i,
      /<div[^>]*id="availability"[^>]*>.*?(\d+)[^<]*in stock/i,
      /"availabilityType":"([^"]*)".*?"stockLevel":(\d+)/,
      /Only (\d+) left in stock/i
    ]

    // 图片提取模式
    const imagePatterns = [
      /<img[^>]*id="landingImage"[^>]*src="([^"]+)"/,
      /<div[^>]*id="imgTagWrapperId"[^>]*>.*?<img[^>]*src="([^"]+)"/s,
      /<img[^>]*data-old-hires="([^"]+)"/,
      /<img[^>]*data-a-dynamic-image="[^"]*([^"]+\.jpg)[^"]*"/
    ]

    // 提取价格
    for (const pattern of pricePatterns) {
      const match = html.match(pattern)
      if (match) {
        const priceStr = match[1].replace(/,/g, '')
        const parsedPrice = parseFloat(priceStr)
        if (!isNaN(parsedPrice) && parsedPrice > 0) {
          price = parsedPrice
          console.info('[BackgroundAmazonPriceService] 找到价格:', price, '使用模式:', pattern.source.substring(0, 50))
          break
        }
      }
    }

    // 提取库存
    for (const pattern of stockPatterns) {
      const match = html.match(pattern)
      if (match) {
        if (pattern.source.includes('stockLevel')) {
          // JSON格式的库存
          const stockStr = match[2]
          const parsedStock = parseInt(stockStr)
          if (!isNaN(parsedStock)) {
            stock = parsedStock
            console.info('[BackgroundAmazonPriceService] 找到库存:', stock)
            break
          }
        } else {
          // 文本格式的库存
          const stockStr = match[1]
          const parsedStock = parseInt(stockStr)
          if (!isNaN(parsedStock)) {
            stock = parsedStock
            console.info('[BackgroundAmazonPriceService] 找到库存:', stock)
            break
          }
        }
      }
    }

    // 如果仍然没有找到库存信息，进行文本检查
    if (stock === 0) {
      const stockTexts = ['In Stock', 'in stock', 'Available', 'available']
      for (const text of stockTexts) {
        if (html.includes(text)) {
          stock = 999 // 默认库存充足
          console.info('[BackgroundAmazonPriceService] 通过文本检测到有库存:', text)
          break
        }
      }
    }

    // 提取图片
    for (const pattern of imagePatterns) {
      const matches = [...html.matchAll(pattern)]
      for (const match of matches) {
        if (match[1] && match[1].startsWith('http')) {
          imageUrl = match[1]
          console.info('[BackgroundAmazonPriceService] 找到图片:', imageUrl)
          break
        }
      }
      if (imageUrl) break
    }

    console.info('[BackgroundAmazonPriceService] 解析结果:', { price, stock, imageUrl })

    // 如果价格提取失败，提供更多调试信息
    if (price === 0) {
      console.warn('[BackgroundAmazonPriceService] 价格提取失败，提供调试信息:')
      console.warn('页面标题:', html.match(/<title[^>]*>([^<]*)<\/title>/i)?.[1] || '未找到标题')

      // 查找所有可能的价格相关元素
      const priceElements = [
        ...html.matchAll(/\$\d+\.?\d*/g),
        ...html.matchAll(/price[^>]*>([^<]*)/gi),
        ...html.matchAll(/value="(\d+\.?\d*)"/g)
      ]

      console.warn('页面中找到的价格相关内容:', priceElements.slice(0, 10).map(m => m[0]))

      // 检查是否有基本的商品页面元素
      const hasBasicElements = {
        hasAsin: html.includes('data-asin='),
        hasBuybox: html.includes('buybox'),
        hasPriceClass: html.includes('a-price'),
        hasAddToCart: html.includes('add-to-cart') || html.includes('Add to Cart')
      }
      console.warn('页面基本元素检查:', hasBasicElements)
    }

    return { price, stock, imageUrl }
  }

  /**
   * 获取Amazon价格（主要方法）
   */
  async getAmazonPrice(asin: string, extCode: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[BackgroundAmazonPriceService] 开始获取Amazon价格:', asin, extCode)

      // 检查缓存
      const cached = this.cache.get(extCode)
      if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
        console.info('[BackgroundAmazonPriceService] 使用缓存数据:', extCode)
        return { success: true, data: cached.data }
      }

      // 等待可用的并发槽位
      await this.waitForAvailableSlot()

      // 检查是否已有相同请求在进行
      if (this.activeRequests.has(extCode)) {
        console.info('[BackgroundAmazonPriceService] 相同请求正在进行中，等待结果:', extCode)
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 2000))
        return this.getAmazonPrice(asin, extCode)
      }

      // 标记请求开始
      this.activeRequests.add(extCode)

      try {
        // 直接通过HTTP请求获取Amazon页面
        const html = await this.fetchAmazonPage(asin)

        // 从HTML中提取价格和库存信息
        const priceData = this.extractPriceFromHtml(html, asin)

        if (priceData.price > 0) {
          // 缓存结果
          this.cache.set(extCode, {
            data: priceData,
            timestamp: Date.now()
          })

          console.info('[BackgroundAmazonPriceService] 价格获取成功:', priceData)
          return { success: true, data: priceData }
        } else {
          throw new Error('未能从页面中提取到有效的价格信息')
        }

      } finally {
        // 清理请求标记
        this.activeRequests.delete(extCode)
      }

    } catch (error) {
      console.error('[BackgroundAmazonPriceService] 价格获取失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '价格获取失败'
      }
    }
  }
}

// 创建BackgroundAmazonPriceService实例
const backgroundAmazonPriceService = new BackgroundAmazonPriceService()

/**
 * 店小秘商品上传功能
 */
async function uploadDianxiaomiProduct(jsonData: string): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.info('[Background] 开始店小秘商品上传流程')

    // 1. 验证JSON数据
    let productData
    try {
      productData = JSON.parse(jsonData)
      console.info('[Background] JSON数据验证成功')
    } catch (parseError) {
      throw new Error('JSON数据格式错误: ' + (parseError instanceof Error ? parseError.message : '未知错误'))
    }

    // 2. 创建ZIP文件
    const zipResult = await createZipFile(jsonData)
    if (!zipResult.success || !zipResult.zipBlob) {
      throw new Error(zipResult.error || 'ZIP文件创建失败')
    }

    console.info('[Background] ZIP文件创建成功，大小:', zipResult.zipBlob.size, 'bytes')

    // 3. 上传ZIP文件到店小秘
    const uploadResult = await uploadZipToDianxiaomi(zipResult.zipBlob)

    if (uploadResult.success) {
      console.info('[Background] 店小秘商品上传成功:', uploadResult.data)
      return {
        success: true,
        data: uploadResult.data
      }
    } else {
      throw new Error(uploadResult.error || '上传失败')
    }

  } catch (error) {
    console.error('[Background] 店小秘商品上传失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '商品上传失败'
    }
  }
}

/**
 * 创建ZIP文件
 */
async function createZipFile(jsonData: string): Promise<{ success: boolean; zipBlob?: Blob; error?: string }> {
  try {
    console.info('[Background] 开始创建ZIP文件')

    // 使用JSZip创建ZIP文件
    const zip = new JSZip()

    // 添加choiceSave.txt文件
    zip.file('choiceSave.txt', jsonData)

    // 生成ZIP文件
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6
      }
    })

    console.info('[Background] ZIP文件创建成功，大小:', zipBlob.size, 'bytes')

    return {
      success: true,
      zipBlob: zipBlob
    }

  } catch (error) {
    console.error('[Background] ZIP文件创建失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'ZIP文件创建失败'
    }
  }
}

/**
 * 上传ZIP文件到店小秘
 */
async function uploadZipToDianxiaomi(zipBlob: Blob): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.info('[Background] 开始上传ZIP文件到店小秘')

    // 创建FormData
    const formData = new FormData()
    formData.append('file', zipBlob, 'choiceSave.zip')

    // 发送上传请求
    const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/uploadChoiceSave.json', {
      method: 'POST',
      body: formData,
      credentials: 'include', // 重要：包含cookies进行身份验证
      headers: {
        'Accept': '*/*',
        'X-Requested-With': 'XMLHttpRequest'
        // 不要设置Content-Type，让浏览器自动设置multipart/form-data
      }
    })

    console.info('[Background] 上传响应状态:', response.status, response.statusText)

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    console.info('[Background] 店小秘上传响应:', result)

    if (result.success) {
      return {
        success: true,
        data: result
      }
    } else {
      throw new Error(result.errorMsg || result.message || '上传失败')
    }

  } catch (error) {
    console.error('[Background] ZIP文件上传失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'ZIP文件上传失败'
    }
  }
}

/**
 * 下载图片并转换为base64
 */
async function downloadImageAsBase64(imageUrl: string): Promise<string> {
  try {
    console.info('[Background] 开始下载图片:', imageUrl)

    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
    }

    const blob = await response.blob()

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = reader.result as string
        console.info('[Background] 图片转换为base64成功，大小:', Math.round(base64.length / 1024), 'KB')
        resolve(base64)
      }
      reader.onerror = () => reject(new Error('图片转换为base64失败'))
      reader.readAsDataURL(blob)
    })
  } catch (error) {
    console.error('[Background] 下载图片失败:', error)
    throw new Error(`下载图片失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

/**
 * 提取和组装Amazon数据
 */
async function extractAndAssembleAmazonData(
  url: string,
  options: {
    processImages?: boolean
    maxImages?: number
    compressImages?: boolean
  } = {}
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.info('[Background] 开始提取和组装Amazon数据:', url)

    // 从URL中提取ASIN
    const asinMatch = url.match(/\/dp\/([A-Z0-9]{10})/)
    if (!asinMatch) {
      throw new Error('无法从URL中提取ASIN')
    }

    const asin = asinMatch[1]
    console.info('[Background] 提取的ASIN:', asin)

    // 获取Amazon页面HTML
    const html = await fetchAmazonPageHtml(url)

    // 使用amazonDataAssemblyService组装数据
    const result = await amazonDataAssemblyService.assembleAmazonDataWithImageProcessing(html, options)

    if (result.success) {
      console.info('[Background] Amazon数据提取和组装成功')
      return result
    } else {
      throw new Error(result.error || '数据组装失败')
    }

  } catch (error) {
    console.error('[Background] 提取和组装Amazon数据失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '提取和组装失败'
    }
  }
}

/**
 * 获取Amazon页面HTML
 */
async function fetchAmazonPageHtml(url: string): Promise<string> {
  try {
    console.info('[Background] 开始获取Amazon页面HTML:', url)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const html = await response.text()
    console.info('[Background] 成功获取Amazon页面HTML，长度:', html.length)

    return html
  } catch (error) {
    console.error('[Background] 获取Amazon页面HTML失败:', error)
    throw new Error(`获取Amazon页面HTML失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 处理页面导航
async function handlePageNavigation(request: any, sender: any, sendResponse: any) {
  try {
    console.info('[Background] 处理页面导航:', request.route)

    // 向side panel发送导航消息
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    if (tabs.length > 0) {
      // 通过content script转发消息到side panel
      chrome.tabs.sendMessage(tabs[0].id!, {
        action: 'SIDE_PANEL_NAVIGATE',
        route: request.route
      }).catch(error => {
        console.warn('[Background] 发送导航消息失败:', error)
      })
    }

    sendResponse({ success: true })
  } catch (error) {
    console.error('[Background] 页面导航失败:', error)
    sendResponse({ success: false, error: error instanceof Error ? error.message : '导航失败' })
  }
}

// 监听来自 side panel 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.info('[Background] 收到消息:', request)

  // 处理页面导航请求
  if (request.action === 'NAVIGATE_TO_PAGE') {
    handlePageNavigation(request, sender, sendResponse)
    return true
  }

  // 处理店小秘配置数据
  if (request.action === 'DXM_CONFIG_DATA_CAPTURED') {
    handleDXMConfigData(request, sender, sendResponse)
    return true // 保持消息通道开放
  }

  // 处理手动注入 MAIN world 脚本
  if (request.action === 'INJECT_MAIN_WORLD_SCRIPT_MANUAL') {
    handleInjectMainWorldScript(request, sender, sendResponse)
    return true // 保持消息通道开放
  }

  if (request.action === 'GET_TEMU_SHOP_INFO') {
    // 注意：temuService已被删除，请使用temuDataService或temu-api服务
    console.warn('[Background] GET_TEMU_SHOP_INFO已废弃，请使用新的Temu服务')
    sendResponse({
      success: false,
      error: 'temuService已被删除，请使用temuDataService或temu-api服务'
    })
    return true
  }

  if (request.action === 'SAVE_PRODUCT_CONFIG') {
    // 处理商品配置保存
    console.info('[Background] 收到商品配置保存请求:', request.config)

    configStorageService.saveProductConfig(request.config)
      .then(() => {
        sendResponse({
          success: true,
          message: '配置保存成功'
        })
      })
      .catch(error => {
        console.error('[Background] 配置保存失败:', error)
        sendResponse({
          success: false,
          error: error.message || '配置保存失败'
        })
      })

    return true
  }

  // 处理店小秘API调用请求
  if (request.action === 'CALL_DIANXIAOMI_API') {
    console.info('[Background] 收到店小秘API调用请求:', request.apiConfig)

    dianxiaomiApiService.callDianxiaomiAPI(request.apiConfig)
      .then(result => {
        console.info('[Background] 店小秘API调用结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 店小秘API调用失败:', error)
        sendResponse({
          success: false,
          error: error.message || '调用失败'
        })
      })

    return true
  }

  // 处理获取店小秘Token状态请求
  if (request.action === 'GET_DIANXIAOMI_TOKEN_STATUS') {
    console.info('[Background] 收到获取店小秘Token状态请求')

    dianxiaomiApiService.getDianxiaomiTokenStatus()
      .then(result => {
        console.info('[Background] 获取Token状态结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取Token状态失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取店铺列表请求
  if (request.action === 'GET_DIANXIAOMI_SHOP_LIST') {
    console.info('[Background] 收到获取店铺列表请求')

    dianxiaomiApiService.getShopList()
      .then(result => {
        console.info('[Background] 获取店铺列表结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取店铺列表失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理Amazon数据组装请求（仅组装）
  if (request.action === 'ASSEMBLE_AMAZON_DATA') {
    console.info('[Background] 收到Amazon数据组装请求')

    amazonDataAssemblyService.assembleAmazonDataWithImageProcessing(request.html, request.options)
      .then(result => {
        console.info('[Background] Amazon数据组装结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] Amazon数据组装失败:', error)
        sendResponse({
          success: false,
          error: error.message || '组装失败'
        })
      })

    return true
  }

  // 处理Amazon数据组装请求（新格式）
  if (request.action === 'ASSEMBLE_AMAZON_DATA_NEW_FORMAT') {
    console.info('[Background] 收到Amazon数据组装请求（新格式）')

    amazonDataAssemblyService.assembleAmazonDataNewFormat(request.html)
      .then(result => {
        console.info('[Background] Amazon数据组装结果（新格式）:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] Amazon数据组装失败（新格式）:', error)
        sendResponse({
          success: false,
          error: error.message || '组装失败'
        })
      })

    return true
  }

  // 处理图片上传到店小秘请求
  if (request.action === 'UPLOAD_IMAGE_TO_DIANXIAOMI') {
    console.info('[Background] 收到图片上传到店小秘请求')

    dianxiaomiImageUploadService.uploadImageToDianxiaomi(request.base64Data, request.fileName)
      .then(uploadedUrl => {
        console.info('[Background] 图片上传成功:', uploadedUrl)
        sendResponse({
          success: true,
          data: uploadedUrl
        })
      })
      .catch(error => {
        console.error('[Background] 图片上传失败:', error)
        sendResponse({
          success: false,
          error: error.message || '图片上传失败'
        })
      })

    return true
  }

  // 处理Amazon价格获取请求
  if (request.action === 'GET_AMAZON_PRICE' || request.type === 'FETCH_AMAZON_PRICE_V2') {
    console.info('[Background] 收到Amazon价格获取请求:', request)

    const { extCode, asin } = request.data || request
    const finalAsin = asin || (extCode ? extCode.split('_')[0] : '')

    if (!finalAsin) {
      sendResponse({
        success: false,
        error: '缺少ASIN参数'
      })
      return true
    }

    backgroundAmazonPriceService.getAmazonPrice(finalAsin, extCode || finalAsin)
      .then(result => {
        console.info('[Background] Amazon价格获取结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] Amazon价格获取失败:', error)
        sendResponse({
          success: false,
          error: error.message || 'Amazon价格获取失败'
        })
      })

    return true
  }

  // 处理Amazon数据提取和组装请求
  if (request.action === 'EXTRACT_AND_ASSEMBLE_AMAZON_DATA') {
    console.info('[Background] 收到Amazon数据提取和组装请求')

    extractAndAssembleAmazonData(request.url, request.options)
      .then(result => {
        console.info('[Background] Amazon数据提取和组装结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] Amazon数据提取和组装失败:', error)
        sendResponse({
          success: false,
          error: error.message || '数据提取和组装失败'
        })
      })

    return true
  }

  // 处理图片下载请求
  if (request.action === 'DOWNLOAD_IMAGE') {
    console.info('[Background] 收到图片下载请求:', request.imageUrl)

    downloadImageAsBase64(request.imageUrl)
      .then(base64Data => {
        console.info('[Background] 图片下载成功')
        sendResponse({
          success: true,
          data: base64Data
        })
      })
      .catch(error => {
        console.error('[Background] 图片下载失败:', error)
        sendResponse({
          success: false,
          error: error.message || '图片下载失败'
        })
      })

    return true
  }

  // 处理IndexedDB操作请求
  if (request.type === 'SAVE_TO_INDEXEDDB') {
    const indexDB = new IndexDBManager()
    indexDB.saveData(request.data.key, request.data.value)
      .then(() => {
        sendResponse({ success: true })
      })
      .catch(error => {
        console.error('[Background] IndexedDB保存失败:', error)
        sendResponse({
          success: false,
          error: error.message || 'IndexedDB保存失败'
        })
      })

    return true
  }

  if (request.type === 'GET_FROM_INDEXEDDB') {
    const indexDB = new IndexDBManager()
    indexDB.getData(request.data.key)
      .then(data => {
        sendResponse({
          success: true,
          data: data
        })
      })
      .catch(error => {
        console.error('[Background] IndexedDB获取失败:', error)
        sendResponse({
          success: false,
          error: error.message || 'IndexedDB获取失败'
        })
      })

    return true
  }

  if (request.type === 'DELETE_FROM_INDEXEDDB') {
    // 这里可以添加删除逻辑
    sendResponse({ success: true })
    return true
  }

  // 处理店小秘商品上传请求
  if (request.action === 'UPLOAD_DIANXIAOMI_PRODUCT') {
    console.info('[Background] 收到店小秘商品上传请求')

    uploadDianxiaomiProduct(request.jsonData)
      .then(result => {
        console.info('[Background] 店小秘商品上传结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 店小秘商品上传失败:', error)
        sendResponse({
          success: false,
          error: error.message || '商品上传失败'
        })
      })

    return true
  }

  // 处理获取运费模板请求
  if (request.action === 'GET_FREIGHT_TEMPLATES') {
    console.info('[Background] 收到获取运费模板请求:', request.shopId)

    dianxiaomiApiService.callDianxiaomiAPI({
      url: `https://www.dianxiaomi.com/api/freight/templates.json?shopId=${request.shopId}`,
      method: 'GET'
    })
      .then(result => {
        console.info('[Background] 获取运费模板结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取运费模板失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取运费模板失败'
        })
      })

    return true
  }

  // 处理获取商品分类请求
  if (request.action === 'GET_PRODUCT_CATEGORIES') {
    console.info('[Background] 收到获取商品分类请求:', request.shopId)

    dianxiaomiApiService.callDianxiaomiAPI({
      url: `https://www.dianxiaomi.com/api/product/categories.json?shopId=${request.shopId}`,
      method: 'GET'
    })
      .then(result => {
        console.info('[Background] 获取商品分类结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取商品分类失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取商品分类失败'
        })
      })

    return true
  }

  // 处理配置获取请求
  if (request.action === 'GET_CONFIG') {
    console.info('[Background] 收到配置获取请求')

    configStorageService.getFullConfig()
      .then(config => {
        console.info('[Background] 配置获取成功')
        sendResponse({
          success: true,
          data: config
        })
      })
      .catch(error => {
        console.error('[Background] 配置获取失败:', error)
        sendResponse({
          success: false,
          error: error.message || '配置获取失败'
        })
      })

    return true
  }

  // 处理配置完整性检查请求
  if (request.action === 'CHECK_CONFIG_COMPLETE') {
    console.info('[Background] 收到配置完整性检查请求')

    configStorageService.isConfigComplete()
      .then(result => {
        console.info('[Background] 配置完整性检查完成:', result)
        sendResponse({
          success: true,
          data: result
        })
      })
      .catch(error => {
        console.error('[Background] 配置完整性检查失败:', error)
        sendResponse({
          success: false,
          error: error.message || '配置检查失败'
        })
      })

    return true
  }

  // 处理数据映射请求
  if (request.action === 'MAP_AMAZON_TO_DIANXIAOMI') {
    console.info('[Background] 收到数据映射请求')

    const { spuData, skuDataList, config, processedImages } = request

    dataMapperService.mapAmazonToDianxiaomi(spuData, skuDataList, config, processedImages)
      .then(result => {
        console.info('[Background] 数据映射成功')
        sendResponse({
          success: true,
          data: result
        })
      })
      .catch(error => {
        console.error('[Background] 数据映射失败:', error)
        sendResponse({
          success: false,
          error: error.message || '数据映射失败'
        })
      })

    return true
  }

  // 处理打开配置页面请求
  if (request.action === 'OPEN_CONFIG_PAGE') {
    console.info('[Background] 收到打开配置页面请求')

    try {
      // 打开side panel
      chrome.sidePanel.open({ windowId: sender.tab?.windowId })
      console.info('[Background] 已打开side panel配置页面')
      sendResponse({ success: true })
    } catch (error) {
      console.error('[Background] 打开配置页面失败:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '打开配置页面失败'
      })
    }

    return true
  }

  // 处理店小秘配置数据保存通知
  if (request.action === 'DXM_CONFIG_DATA_SAVED') {
    console.info('[Background] 收到店小秘配置数据保存通知:', request.data)

    try {
      // 这里可以添加额外的处理逻辑
      // 比如通知其他组件、更新徽章等

      // 可选：更新扩展图标徽章
      if (request.data.categoryName) {
        chrome.action.setBadgeText({ text: '✓' })
        chrome.action.setBadgeBackgroundColor({ color: '#52c41a' })

        // 5秒后清除徽章
        setTimeout(() => {
          chrome.action.setBadgeText({ text: '' })
        }, 5000)
      }

      console.info('[Background] 店小秘配置数据处理完成')
      sendResponse({ success: true })
    } catch (error) {
      console.error('[Background] 处理店小秘配置数据失败:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '处理失败'
      })
    }

    return true
  }

  return false
})

export {}
