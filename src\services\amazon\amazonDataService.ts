import configStorageService from '../configStorageService'
import type { BasicConfig, ProductConfig } from '../configStorageService'
import imageProcessingService from '../imageProcessingService'

// Amazon产品数据接口
export interface AmazonProductData {
  asin: string
  title: string
  brand?: string
  price?: number
  currency?: string
  rating?: number
  reviewCount?: number
  mainImageUrl?: string
  imageUrls: string[]
  bulletPoints: string[]
  description?: string
  categoryPath?: string
  stockStatus?: string
  specifications?: Record<string, string>
  variations?: AmazonVariation[]
  sourceUrl: string
}

// Amazon商品价格信息接口
export interface AmazonPriceInfo {
  e: string // extCode
  p: number // 价格（美元）
  s: number // 库存
  img: string // 图片URL
}

// Amazon价格数据接口
export interface AmazonPriceData {
  usdPrice: string
  stock: string
  imageUrl?: string
  timestamp: number
}



// Amazon变体数据接口
export interface AmazonVariation {
  asin: string
  parentAsin: string
  price?: number
  imageUrl?: string
  stockStatus?: string
  attributes: Record<string, string>
}

// 店小秘格式的产品数据接口（根据验证通过的格式调整）
export interface DianxiaomiProductData {
  attributes: string
  categoryId: string
  shopId: string
  productSemiManagedReq: string
  sourceUrl: string
  productName: string
  productNameI18n: string
  outerGoodsUrl: string
  materialImgUrl: string
  productOrigin: string
  region2Id: string
  originFileUrl: string
  sensitiveAttr: string
  personalizationSwitch: number
  mainImage: string
  optionValue: string
  mainProductSkuSpecReqs: string
  goodsModel: string
  variationListStr: string
  productWarehouseRouteReq: string
  dxmPdfUrl: string
  qualifiedEn: string
  instructionsId: string
  instructionsName: string
  description: string
  instructionsTranslateId: string
  freightTemplateId: string
  shipmentLimitSecond: number
  op: number
  categoryType: number
  productId: string
  sizeTemplateIds: string
}

class AmazonDataService {
  /**
   * 获取Amazon商品价格（纯数据获取，不处理缓存）
   */
  async getAmazonPrice(extCode: string): Promise<AmazonPriceData | null> {
    try {
      console.info('[AmazonDataService] 获取Amazon价格:', extCode)

      // 检查是否是Amazon商品
      if (!this.isAmazonProduct(extCode)) {
        console.warn('[AmazonDataService] 不是Amazon商品:', extCode)
        return null
      }

      // 从API获取价格
      const apiPrice = await this.getAmazonPriceFromAPI(extCode)

      if (apiPrice) {
        console.info('[AmazonDataService] Amazon价格获取成功:', extCode)
        return apiPrice
      }

      console.warn('[AmazonDataService] 未能获取到Amazon价格:', extCode)
      return null
    } catch (error) {
      console.error('[AmazonDataService] 获取Amazon价格失败:', error)
      return null
    }
  }

  /**
   * 批量获取Amazon价格（纯数据获取，不处理缓存）
   */
  async batchGetAmazonPrices(
    extCodes: string[],
    onProgress?: (completed: number, total: number) => void
  ): Promise<Map<string, AmazonPriceData>> {
    const result = new Map<string, AmazonPriceData>()
    const total = extCodes.length
    let completed = 0

    console.info('[AmazonDataService] 批量获取Amazon价格:', total, '个商品')

    // 并发控制，最多同时处理3个请求
    const concurrency = 3
    const chunks = this.chunkArray(extCodes, concurrency)

    for (const chunk of chunks) {
      const promises = chunk.map(async (extCode) => {
        const priceData = await this.getAmazonPrice(extCode)
        if (priceData) {
          result.set(extCode, priceData)
        }
        completed++
        onProgress?.(completed, total)
      })

      await Promise.allSettled(promises)
    }

    console.info('[AmazonDataService] 批量获取完成，成功获取', result.size, '个Amazon价格')
    return result
  }

  /**
   * 验证是否为Amazon商品
   */
  isAmazonProduct(extCode: string): boolean {
    return extCode.includes('[am]') && this.extractASINFromExtCode(extCode) !== null
  }

  /**
   * 从extCode提取Amazon ASIN
   */
  extractASINFromExtCode(extCode: string): string | null {
    try {
      const match = extCode.match(/^([A-Z0-9]{10})\[am\]/)
      return match ? match[1] : null
    } catch (error) {
      console.error('[AmazonDataService] 提取ASIN失败:', error)
      return null
    }
  }

  /**
   * 构建Amazon商品URL
   */
  buildAmazonURL(extCode: string): string | null {
    const asin = this.extractASINFromExtCode(extCode)
    return asin ? `https://www.amazon.com/dp/${asin}` : null
  }

  // ==================== 静态方法：用于从HTML字符串中提取数据 ====================

  /**
   * 增强Amazon图片URL（静态方法）
   *
   * 将Amazon图片URL中的低分辨率参数替换为高分辨率参数
   * 这是一个纯函数，可以在任何地方调用，不依赖DOM环境
   *
   * @param imageUrl - 原始Amazon图片URL
   * @returns 增强后的高分辨率图片URL
   *
   * @example
   * AmazonDataService.enhanceImageUrl('https://m.media-amazon.com/images/I/511NGlAAE0L._AC_US100_.jpg')
   * // 返回: 'https://m.media-amazon.com/images/I/511NGlAAE0L._AC_US800_.jpg'
   */
  static enhanceImageUrl(imageUrl: string): string {
    if (!imageUrl || !imageUrl.includes('amazon.com')) {
      return imageUrl
    }

    // 将 _US100_ 替换为 _US800_ 以获取更高分辨率图片
    return imageUrl.replace(/_US100_/g, '_US800_')
  }

  /**
   * 从HTML字符串中提取主图URL（静态方法）
   *
   * 用于在background script等无DOM环境中从HTML字符串提取主图
   * 使用正则表达式匹配image-canvas-caption元素及其内部的第一张图片
   *
   * @param html - Amazon商品页面的HTML字符串
   * @returns 处理后的主图URL，如果未找到则返回null
   *
   * @example
   * const html = '<div id="image-canvas-caption" class="a-row"><img src="..."></div>'
   * const mainImageUrl = AmazonDataService.extractMainImageFromHtml(html)
   */
  static extractMainImageFromHtml(html: string): string | null {
    // 使用正则表达式匹配image-canvas-caption元素
    const canvasCaptionMatch = html.match(/<div[^>]*id="image-canvas-caption"[^>]*class="a-row"[^>]*>(.*?)<\/div>/s)
    if (canvasCaptionMatch) {
      // 提取第一张图片的src属性
      const imgMatch = canvasCaptionMatch[1].match(/<img[^>]*src="([^"]*)"[^>]*>/)
      if (imgMatch && imgMatch[1]) {
        console.info('[AmazonDataService] 从HTML中提取主图:', imgMatch[1])
        return this.enhanceImageUrl(imgMatch[1])
      }
    }

    console.warn('[AmazonDataService] HTML中未找到image-canvas-caption元素或其中的图片')
    return null
  }

  /**
   * 从HTML字符串中提取所有图片URL（静态方法）
   *
   * 用于在background script等无DOM环境中从HTML字符串提取所有商品图片
   * 使用正则表达式匹配image-canvas-caption元素及其内部的所有图片
   *
   * @param html - Amazon商品页面的HTML字符串
   * @returns JSON格式的图片URL数组字符串，最多包含10个元素
   *
   * @example
   * const html = '<div id="image-canvas-caption" class="a-row"><img src="..."><img src="..."></div>'
   * const imageUrlsJson = AmazonDataService.extractImageUrlsFromHtml(html)
   * const imageUrls = JSON.parse(imageUrlsJson) // 解析为数组
   */
  static extractImageUrlsFromHtml(html: string): string {
    const imageUrls: string[] = []

    // 使用正则表达式匹配image-canvas-caption元素
    const canvasCaptionMatch = html.match(/<div[^>]*id="image-canvas-caption"[^>]*class="a-row"[^>]*>(.*?)<\/div>/s)
    if (canvasCaptionMatch) {
      // 提取所有图片的src属性
      const imgPattern = /<img[^>]*src="([^"]*)"[^>]*>/g
      let imgMatch

      while ((imgMatch = imgPattern.exec(canvasCaptionMatch[1])) !== null) {
        const imgUrl = imgMatch[1]
        if (imgUrl && imgUrl.includes('amazon.com') && !imgUrl.includes('data:image')) {
          const enhancedUrl = this.enhanceImageUrl(imgUrl)
          if (!imageUrls.includes(enhancedUrl)) {
            imageUrls.push(enhancedUrl)
          }
        }
      }

      console.info('[AmazonDataService] 从HTML中提取到图片数量:', imageUrls.length)
    } else {
      console.warn('[AmazonDataService] HTML中未找到image-canvas-caption元素')
    }

    return JSON.stringify(imageUrls.slice(0, 10)) // 最多10张图片，返回JSON字符串
  }

  /**
   * 从Amazon页面提取产品数据
   */
  async extractProductDataFromPage(): Promise<AmazonProductData | null> {
    try {
      console.info('[AmazonDataService] 开始从当前页面提取Amazon产品数据...')
      
      // 检查是否为Amazon产品页面
      if (!this.isAmazonProductPage()) {
        throw new Error('当前页面不是Amazon产品页面')
      }

      const productData: AmazonProductData = {
        asin: this.extractAsin(),
        title: this.extractTitle(),
        brand: this.extractBrand(),
        price: this.extractPrice(),
        currency: 'USD',
        rating: this.extractRating(),
        reviewCount: this.extractReviewCount(),
        mainImageUrl: this.extractMainImage(),
        imageUrls: this.extractImageUrls(),
        bulletPoints: this.extractBulletPoints(),
        description: this.extractDescription(),
        categoryPath: this.extractCategoryPath(),
        stockStatus: this.extractStockStatus(),
        specifications: this.extractSpecifications(),
        variations: this.extractVariations(),
        sourceUrl: window.location.href
      }

      console.info('[AmazonDataService] Amazon产品数据提取完成:', productData)
      return productData
    } catch (error) {
      console.error('[AmazonDataService] 提取Amazon产品数据失败:', error)
      return null
    }
  }

  /**
   * 将Amazon数据转换为店小秘格式
   */
  async convertToDianxiaomiFormat(amazonData: AmazonProductData): Promise<DianxiaomiProductData> {
    try {
      console.info('[AmazonDataService] 开始转换Amazon数据为店小秘格式...')

      // 获取配置
      const { basic, product } = await configStorageService.getFullConfig()

      // 处理图片：转换为800x800格式并上传到店小秘
      console.info('[AmazonDataService] 开始处理Amazon图片...')
      let processedImageUrls: string[] = []

      try {
        const imageResults = await imageProcessingService.batchProcessImages(
          amazonData.imageUrls,
          (progress) => {
            console.info('[AmazonDataService] 图片处理进度:', progress)
          }
        )

        processedImageUrls = imageResults
          .filter(result => result.success && result.processedUrl)
          .map(result => result.processedUrl!)

        console.info('[AmazonDataService] 图片处理完成:', processedImageUrls.length, '张')
      } catch (imageError) {
        console.warn('[AmazonDataService] 图片处理失败，使用原始图片:', imageError)
        processedImageUrls = amazonData.imageUrls
      }

      // 构建description（包含处理后的图片）
      const description = await this.buildDescription(amazonData, processedImageUrls)

      // 构建店小秘格式数据
      const dianxiaomiData: DianxiaomiProductData = {
        attributes: this.buildAttributes(amazonData.specifications || {}),
        categoryId: basic.productCategory || "9938",
        shopId: basic.shopAccount || "",
        productSemiManagedReq: "100",
        sourceUrl: amazonData.sourceUrl,
        productName: this.buildProductName(amazonData.title, product.titlePrefix, product.titleSuffix),
        productNameI18n: JSON.stringify({ en: amazonData.title }),
        outerGoodsUrl: amazonData.sourceUrl,
        materialImgUrl: processedImageUrls[0] || amazonData.mainImageUrl || "",
        productOrigin: "CN",
        region2Id: basic.businessSite || "**************",
        originFileUrl: "",
        sensitiveAttr: "",
        personalizationSwitch: 0,
        mainImage: processedImageUrls.length > 0 ? processedImageUrls.join('|') : (amazonData.mainImageUrl || ""),
        optionValue: "[]",
        mainProductSkuSpecReqs: this.buildMainProductSkuSpecReqs(amazonData, processedImageUrls[0]),
        goodsModel: "",
        variationListStr: this.buildVariationListStr(amazonData, product, basic, processedImageUrls[0]),
        productWarehouseRouteReq: this.buildProductWarehouseRouteReq(basic.warehouse, basic.businessSite),
        dxmPdfUrl: "",
        qualifiedEn: "",
        instructionsId: "",
        instructionsName: "",
        description: description,
        instructionsTranslateId: "",
        freightTemplateId: basic.freightTemplate || "",
        shipmentLimitSecond: parseInt(basic.shippingTime) || 172800,
        op: 1,
        categoryType: 0,
        productId: "0",
        sizeTemplateIds: ""
      }

      console.info('[AmazonDataService] 数据转换完成:', dianxiaomiData)
      return dianxiaomiData
    } catch (error) {
      console.error('[AmazonDataService] 转换数据格式失败:', error)
      throw error
    }
  }

  /**
   * 模拟推送数据到店小秘
   */
  async simulatePushToDianxiaomi(dianxiaomiData: DianxiaomiProductData): Promise<void> {
    try {
      console.info('[AmazonDataService] 模拟推送数据到店小秘...')
      
      // 压缩数据
      const compressedData = JSON.stringify(dianxiaomiData)
      
      console.info('[AmazonDataService] 准备推送的数据大小:', compressedData.length, '字符')
      console.info('[AmazonDataService] 推送数据预览:', {
        productName: dianxiaomiData.productName,
        sourceUrl: dianxiaomiData.sourceUrl,
        shopId: dianxiaomiData.shopId,
        categoryId: dianxiaomiData.categoryId,
        mainImage: dianxiaomiData.mainImage?.substring(0, 100) + '...'
      })
      
      // 这里可以添加实际的API调用
      // const response = await fetch('店小秘API端点', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: compressedData
      // })
      
      console.info('[AmazonDataService] 数据推送模拟完成')
    } catch (error) {
      console.error('[AmazonDataService] 推送数据失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  private isAmazonProductPage(): boolean {
    return window.location.href.includes('amazon.com') && 
           (window.location.href.includes('/dp/') || window.location.href.includes('/gp/product/'))
  }

  private extractAsin(): string {
    const url = window.location.href
    const match = url.match(/\/dp\/([A-Z0-9]{10})/)
    return match ? match[1] : 'UNKNOWN'
  }

  private extractTitle(): string {
    const titleElement = document.querySelector('#productTitle')
    return titleElement?.textContent?.trim() || ''
  }

  private extractBrand(): string | undefined {
    const brandElement = document.querySelector('#bylineInfo')
    if (brandElement) {
      const brandText = brandElement.textContent?.trim() || ''
      const brandMatch = brandText.match(/Visit the (.+?) Store/)
      return brandMatch ? brandMatch[1] : brandText
    }
    return undefined
  }

  private extractPrice(): number | undefined {
    const priceElement = document.querySelector('.a-price .a-offscreen')
    if (priceElement) {
      const priceText = priceElement.textContent?.trim() || ''
      const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/)
      if (priceMatch) {
        return parseFloat(priceMatch[0].replace(',', ''))
      }
    }
    return undefined
  }

  private extractRating(): number | undefined {
    const ratingElement = document.querySelector('[data-hook="average-star-rating"] .a-icon-alt')
    if (ratingElement) {
      const ratingText = ratingElement.textContent?.trim() || ''
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/)
      return ratingMatch ? parseFloat(ratingMatch[1]) : undefined
    }
    return undefined
  }

  private extractReviewCount(): number | undefined {
    const reviewElement = document.querySelector('[data-hook="total-review-count"]')
    if (reviewElement) {
      const reviewText = reviewElement.textContent?.trim() || ''
      const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/)
      return reviewMatch ? parseInt(reviewMatch[1]) : undefined
    }
    return undefined
  }

  /**
   * 增强Amazon图片URL，将低分辨率替换为高分辨率
   *
   * Amazon图片URL通常包含尺寸参数，如_US100_表示100px宽度的缩略图
   * 通过将_US100_替换为_US800_可以获取800px宽度的高分辨率图片
   *
   * @param imageUrl - 原始Amazon图片URL
   * @returns 增强后的高分辨率图片URL
   *
   * @example
   * // 输入: https://m.media-amazon.com/images/I/511NGlAAE0L._AC_US100_.jpg
   * // 输出: https://m.media-amazon.com/images/I/511NGlAAE0L._AC_US800_.jpg
   */
  private enhanceAmazonImageUrl(imageUrl: string): string {
    if (!imageUrl || !imageUrl.includes('amazon.com')) {
      return imageUrl
    }

    // 将 _US100_ 替换为 _US800_ 以获取更高分辨率图片
    return imageUrl.replace(/_US100_/g, '_US800_')
  }

  /**
   * 提取Amazon商品主图URL
   *
   * 根据最新的Amazon页面结构，商品主图位于id为"image-canvas-caption"且class包含"a-row"的div元素中
   * 该元素包含商品的所有展示图片，第一张图片即为主图
   *
   * 注意：不再使用传统的#landingImage、#imgBlkFront等选择器，因为这些选择器在新版Amazon页面中不可靠
   *
   * @returns 处理后的主图URL，如果未找到则返回undefined
   */
  private extractMainImage(): string | undefined {
    const canvasCaptionElement = document.querySelector('#image-canvas-caption.a-row')
    if (canvasCaptionElement) {
      const firstImg = canvasCaptionElement.querySelector('img') as HTMLImageElement
      if (firstImg && firstImg.src) {
        console.info('[AmazonDataService] 从image-canvas-caption提取主图:', firstImg.src)
        return this.enhanceAmazonImageUrl(firstImg.src)
      }
    }

    console.warn('[AmazonDataService] 未找到image-canvas-caption元素或其中的图片')
    return undefined
  }

  /**
   * 提取Amazon商品所有图片URL列表
   *
   * 从id为"image-canvas-caption"且class包含"a-row"的div元素中提取所有商品图片
   * 该元素是Amazon商品页面中图片展示的标准容器
   *
   * 处理逻辑：
   * 1. 查找image-canvas-caption容器元素
   * 2. 提取容器内所有img标签的src属性
   * 3. 过滤掉非Amazon域名和data:image格式的图片
   * 4. 对每个图片URL进行高分辨率增强处理
   * 5. 去重并限制最多返回10张图片
   *
   * @returns 处理后的图片URL数组，最多包含10个元素
   */
  private extractImageUrls(): string[] {
    const imageUrls: string[] = []

    const canvasCaptionElement = document.querySelector('#image-canvas-caption.a-row')
    if (canvasCaptionElement) {
      const imageElements = canvasCaptionElement.querySelectorAll('img') as NodeListOf<HTMLImageElement>

      imageElements.forEach(img => {
        const imgUrl = img.src || img.getAttribute('data-src')
        if (imgUrl && imgUrl.includes('amazon.com') && !imgUrl.includes('data:image')) {
          const enhancedUrl = this.enhanceAmazonImageUrl(imgUrl)
          if (!imageUrls.includes(enhancedUrl)) {
            imageUrls.push(enhancedUrl)
          }
        }
      })

      console.info('[AmazonDataService] 从image-canvas-caption提取到图片数量:', imageUrls.length)
    } else {
      console.warn('[AmazonDataService] 未找到image-canvas-caption元素')
    }

    return imageUrls.slice(0, 10) // 限制最多10张图片
  }

  private extractBulletPoints(): string[] {
    const bulletPoints: string[] = []
    const bulletElements = document.querySelectorAll('#featurebullets_feature_div li span.a-list-item')

    bulletElements.forEach(bullet => {
      const text = bullet.textContent?.trim() || ''
      if (text && text.length > 10 && !text.includes('Make sure')) {
        bulletPoints.push(text)
      }
    })

    return bulletPoints.slice(0, 5) // 限制最多5个要点
  }

  private extractDescription(): string | undefined {
    const descElement = document.querySelector('#feature-bullets ul, #featurebullets_feature_div')
    return descElement?.textContent?.trim().substring(0, 1000) || undefined
  }

  private extractCategoryPath(): string | undefined {
    const breadcrumbElements = document.querySelectorAll('#wayfinding-breadcrumbs_feature_div a')
    const categoryPath: string[] = []

    breadcrumbElements.forEach(breadcrumb => {
      const text = breadcrumb.textContent?.trim() || ''
      if (text && text !== 'Home' && text !== 'Amazon.com') {
        categoryPath.push(text)
      }
    })

    return categoryPath.join(' > ') || undefined
  }

  private extractStockStatus(): string | undefined {
    const availabilityElement = document.querySelector('#availability span, #availabilityInsideBuyBox_feature_div span')
    return availabilityElement?.textContent?.trim() || 'In Stock'
  }

  private extractSpecifications(): Record<string, string> {
    const specs: Record<string, string> = {}
    const specTables = document.querySelectorAll('#productDetails_techSpec_section_1 tr, #productDetails_detailBullets_sections1 tr')

    specTables.forEach(row => {
      const cells = row.querySelectorAll('td')
      if (cells.length >= 2) {
        const key = cells[0].textContent?.trim() || ''
        const value = cells[1].textContent?.trim() || ''
        if (key && value) {
          specs[key] = value
        }
      }
    })

    return specs
  }

  private extractVariations(): AmazonVariation[] {
    // 这里可以实现变体提取逻辑
    // 暂时返回空数组
    return []
  }

  private buildAttributes(specifications: Record<string, string>): string {
    const attributes = Object.entries(specifications).map(([key, value], index) => ({
      propName: key,
      refPid: 4000 + index,
      pid: 1700 + index,
      templatePid: 1200000 + index,
      numberInputValue: "",
      valueUnit: "",
      vid: (67000 + index).toString(),
      propValue: value
    }))

    return JSON.stringify(attributes)
  }

  private buildProductName(title: string, prefix: string, suffix: string): string {
    let productName = title
    if (prefix) productName = prefix + ' ' + productName
    if (suffix) productName = productName + ' ' + suffix
    return productName.substring(0, 200) // 限制长度
  }

  private buildMainProductSkuSpecReqs(amazonData: AmazonProductData, processedImageUrl?: string): string {
    return JSON.stringify([{
      parentSpecId: 0,
      parentSpecName: "",
      specId: 0,
      specName: "",
      previewImgUrls: processedImageUrl || amazonData.mainImageUrl || "",
      extCode: `${amazonData.asin}[am]1`,
      productSkcId: ""
    }])
  }

  private buildVariationListStr(amazonData: AmazonProductData, productConfig: ProductConfig, basicConfig: BasicConfig, processedImageUrl?: string): string {
    // 使用正确的库存配置：优先使用fixedStock，否则使用minStock
    const stockQuantity = productConfig.fixedStock?.toString() || productConfig.minStock?.toString() || "200"

    // 按照验证通过的格式构建variation对象
    const variation = {
      productSkuId: 0,
      supplierPrice: Math.round((amazonData.price || 10) * productConfig.priceMultiplier * 100), // 转换为分
      extCode: `${amazonData.asin}[am]1`,
      length: productConfig.defaultSize.length,
      width: productConfig.defaultSize.width,
      height: productConfig.defaultSize.height,
      weight: productConfig.defaultSize.weight,
      codeType: "1",
      code: "",
      suggestedPrice: Math.round((amazonData.price || 10) * productConfig.priceMultiplier * 100),
      suggestedPriceCurrencyType: "CNY",
      numberOfPieces: 1,
      skuClassification: "1",
      pieceUnitCode: "1",
      individuallyPacked: null,
      productSkuAccessories: "[]", // 新增字段，按照验证格式
      thumbUrl: processedImageUrl || amazonData.mainImageUrl || "",
      productSkuSpecReqs: JSON.stringify([{
        specId: "0",
        specName: "Default",
        parentSpecId: 1001,
        parentSpecName: "颜色"
      }]),
      productSkuStockQuantityReq: JSON.stringify([{
        warehouseId: basicConfig.warehouse,
        targetStockAvailable: stockQuantity
      }]),
      sitePriceInfo: null
    }

    return JSON.stringify([variation])
  }

  private buildProductWarehouseRouteReq(warehouseId: string, siteId: string): string {
    return JSON.stringify([{
      warehouseId: warehouseId,
      warehouseName: "Amazon仓库",
      siteIdList: [siteId || "100"]
    }])
  }

  private async buildDescription(amazonData: AmazonProductData, processedImageUrls?: string[]): Promise<string> {
    interface DescriptionModule {
      lang: string
      type: string
      priority: string
      contentList: Array<{
        text?: string
        textModuleDetails?: {
          fontFamily: null
          fontColor: string
          backgroundColor: string
          fontSize: string
          align: string
        }
        imgUrl?: string
        height?: number
        width?: number
      }>
    }

    const description: DescriptionModule[] = []
    let priority = 0

    // 添加每个bulletPoint作为单独的文本模块
    if (amazonData.bulletPoints && amazonData.bulletPoints.length > 0) {
      amazonData.bulletPoints.forEach((bulletPoint) => {
        if (bulletPoint && bulletPoint.trim()) {
          description.push({
            lang: "zh",
            type: "text",
            priority: priority.toString(),
            contentList: [{
              text: bulletPoint.trim(),
              textModuleDetails: {
                fontFamily: null,
                fontColor: "#434649",
                backgroundColor: "#ffffff",
                fontSize: "12",
                align: "left"
              }
            }]
          })
          priority++
        }
      })
    }

    // 添加处理后的图片（800x800格式）
    const imageUrls = processedImageUrls || amazonData.imageUrls
    if (imageUrls && imageUrls.length > 0) {
      imageUrls.forEach((imageUrl) => {
        if (imageUrl && imageUrl.trim()) {
          description.push({
            lang: "zh",
            type: "image",
            priority: priority.toString(),
            contentList: [{
              imgUrl: imageUrl.trim(),
              height: 800,
              width: 800
            }]
          })
          priority++
        }
      })
    }

    return JSON.stringify(description)
  }

  // ==================== 价格相关私有方法 ====================

  /**
   * 从API获取Amazon价格（直接调用background的HTTP方法）
   */
  private async getAmazonPriceFromAPI(extCode: string): Promise<AmazonPriceData | null> {
    try {
      console.info('[AmazonDataService] 开始API获取Amazon价格:', extCode)

      // 从extCode中提取ASIN
      const asin = this.extractASINFromExtCode(extCode)
      if (!asin) {
        console.warn('[AmazonDataService] 无法从extCode提取ASIN:', extCode)
        return null
      }

      console.info('[AmazonDataService] 提取的ASIN:', asin)

      // 直接调用background的HTTP获取方法
      const response = await chrome.runtime.sendMessage({
        type: 'FETCH_AMAZON_PRICE_V2',
        data: { extCode, asin }
      })

      console.info('[AmazonDataService] Background HTTP响应:', response)

      if (response && response.success && response.data) {
        const priceInfo: AmazonPriceInfo = response.data
        const result = {
          usdPrice: priceInfo.p.toString(),
          stock: priceInfo.s.toString(),
          imageUrl: priceInfo.img || '',
          timestamp: Date.now()
        }
        console.info('[AmazonDataService] 转换后的价格数据:', result)
        return result
      } else {
        console.warn('[AmazonDataService] Background HTTP响应失败:', response?.error)
      }

      return null
    } catch (error) {
      console.error('[AmazonDataService] Background HTTP获取Amazon价格失败:', error)
      return null
    }
  }



  /**
   * 数组分块工具函数
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }
}

// 导出单例实例
export const amazonDataService = new AmazonDataService()
export default amazonDataService
