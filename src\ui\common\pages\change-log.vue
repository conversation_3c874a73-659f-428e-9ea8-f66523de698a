<script setup lang="ts">
import { marked } from "marked"

const version = __VERSION__
const changelog = __CHANGELOG__

// 版本更新记录
const versionHistory = [
  {
    version: "1.2.0",
    date: "2024-12-20",
    type: "feature",
    title: "重大功能更新",
    changes: [
      "新增智能价格监控功能",
      "支持多店铺统一管理",
      "优化商品批量上传性能",
      "增加数据分析报表功能"
    ]
  },
  {
    version: "1.1.5",
    date: "2024-12-15",
    type: "improvement",
    title: "性能优化",
    changes: [
      "提升页面加载速度",
      "优化内存使用",
      "修复已知bug",
      "改进用户界面体验"
    ]
  },
  {
    version: "1.1.0",
    date: "2024-12-10",
    type: "feature",
    title: "功能增强",
    changes: [
      "新增Amazon商品采集功能",
      "支持自定义商品模板",
      "增加库存管理功能",
      "优化商品信息组装逻辑"
    ]
  },
  {
    version: "1.0.5",
    date: "2024-12-05",
    type: "bugfix",
    title: "问题修复",
    changes: [
      "修复商品上传失败问题",
      "解决价格同步异常",
      "修复界面显示错误",
      "提升系统稳定性"
    ]
  },
  {
    version: "1.0.0",
    date: "2024-12-01",
    type: "release",
    title: "正式发布",
    changes: [
      "胡建大卖家正式上线",
      "支持Temu店铺管理",
      "提供基础商品管理功能",
      "完整的用户操作界面"
    ]
  }
]

const getTypeColor = (type: string) => {
  switch (type) {
    case 'feature': return 'bg-blue-100 text-blue-800'
    case 'improvement': return 'bg-green-100 text-green-800'
    case 'bugfix': return 'bg-red-100 text-red-800'
    case 'release': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'feature': return '🚀'
    case 'improvement': return '⚡'
    case 'bugfix': return '🐛'
    case 'release': return '🎉'
    default: return '📝'
  }
}
</script>

<template>
  <div class="p-6 max-w-4xl mx-auto">
    <RouterLinkUp />

    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">更新日志</h1>
      <p class="text-lg text-gray-600">胡建大卖家版本更新记录</p>
      <div class="mt-4">
        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
          当前版本: {{ version }}
        </span>
      </div>
    </div>

    <div class="space-y-6">
      <div
        v-for="release in versionHistory"
        :key="release.version"
        class="bg-white rounded-lg shadow-md p-6 border-l-4"
        :class="{
          'border-blue-500': release.type === 'feature',
          'border-green-500': release.type === 'improvement',
          'border-red-500': release.type === 'bugfix',
          'border-purple-500': release.type === 'release'
        }"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <span class="text-2xl mr-3">{{ getTypeIcon(release.type) }}</span>
            <div>
              <h3 class="text-xl font-semibold text-gray-800">
                {{ release.title }}
              </h3>
              <p class="text-sm text-gray-500">版本 {{ release.version }}</p>
            </div>
          </div>
          <div class="text-right">
            <span
              class="px-3 py-1 rounded-full text-xs font-medium"
              :class="getTypeColor(release.type)"
            >
              {{ release.type === 'feature' ? '新功能' :
                 release.type === 'improvement' ? '优化' :
                 release.type === 'bugfix' ? '修复' : '发布' }}
            </span>
            <p class="text-sm text-gray-500 mt-1">{{ release.date }}</p>
          </div>
        </div>

        <ul class="space-y-2">
          <li
            v-for="change in release.changes"
            :key="change"
            class="flex items-start"
          >
            <span class="text-green-500 mr-2 mt-1">•</span>
            <span class="text-gray-700">{{ change }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- 原始changelog -->
    <div v-if="changelog" class="mt-12 bg-gray-50 rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4 text-gray-800">详细更新记录</h2>
      <!-- eslint-disable vue/no-v-html -->
      <div
        class="prose prose-sm max-w-none changelog"
        v-html="marked(changelog)"
      />
      <!--eslint-enable-->
    </div>

    <div class="mt-8 text-center">
      <p class="text-gray-500 text-sm">
        我们会持续更新和改进产品，为您提供更好的使用体验。
      </p>
    </div>
  </div>
</template>

<style lang="css" scoped>
.changelog {
  color: #374151;
}

.changelog h1,
.changelog h2,
.changelog h3 {
  color: #1f2937;
  font-weight: 600;
}

.changelog ul {
  list-style-type: disc;
  list-style-position: inside;
}

.changelog li {
  margin-bottom: 0.25rem;
}
</style>
