/**
 * 数据映射服务
 * 负责将Amazon商品数据映射为店小秘API格式
 */

import type { AmazonProductData } from './amazon/amazonDataService'

export interface DianxiaomiProductData {
  attributes: string
  categoryId: string
  shopId: string
  productSemiManagedReq: string
  sourceUrl: string
  fullCid: string
  productName: string
  productNameI18n: string
  outerGoodsUrl: string
  materialImgUrl: string
  productOrigin: string
  region2Id: string
  originFileUrl: string
  sensitiveAttr: string
  personalizationSwitch: number
  mainImage: string
  dxmVideoId: string
  optionValue: string
  mainProductSkuSpecReqs: string
  goodsModel: string
  variationListStr: string
  productWarehouseRouteReq: string
  dxmPdfUrl: string
  qualifiedEn: string
  instructionsId: string
  instructionsName: string
  description: string
  instructionsTranslateId: string
  freightTemplateId: string
  shipmentLimitSecond: number
  op: number
  id: string
  categoryType: number
  dxmState: string
  productId: string
  sizeTemplateIds: string
}

export interface MappingConfig {
  basic: {
    categoryId: string
    shopId: string
    businessSite: string
    warehouse: string
    freightTemplate: string
    shippingTime: string
    publishStatus: string
    venue: string
    productCategory: string
    productAttributes: any
  }
  product: {
    titlePrefix: string
    titleSuffix: string
    priceMultiplier: number
    defaultSize: {
      length: number
      width: number
      height: number
      weight: number
    }
    fixedStock: number
    minStock: number
    collectDetails: string[]
    collectSku: boolean
  }
}

export class DataMapperService {
  private static instance: DataMapperService

  static getInstance(): DataMapperService {
    if (!DataMapperService.instance) {
      DataMapperService.instance = new DataMapperService()
    }
    return DataMapperService.instance
  }

  /**
   * 将Amazon数据映射为店小秘格式
   */
  async mapAmazonToDianxiaomi(
    spuData: any,
    skuDataList: any[],
    config: MappingConfig,
    processedImages?: string[]
  ): Promise<DianxiaomiProductData> {
    try {
      console.info('[DataMapperService] 开始映射Amazon数据到店小秘格式')

      // 生成唯一ID
      const uniqueId = this.generateUniqueId()

      // 构建属性数据
      const attributes = this.buildAttributes(spuData, config.basic.productAttributes)

      // 构建商品名称
      const productName = this.buildProductName(
        spuData.title || '',
        config.product.titlePrefix,
        config.product.titleSuffix
      )

      // 构建图片URLs
      const mainImage = this.buildMainImageString(processedImages || JSON.parse(spuData.imageUrls || '[]'))

      // 构建变体数据
      const variationListStr = this.buildVariationListStr(skuDataList, config)

      // 构建商品描述
      const description = this.buildDescription(spuData)

      // 构建仓库路由
      const productWarehouseRouteReq = this.buildWarehouseRoute(config.basic.warehouse, config.basic.businessSite)

      // 构建SKU规格
      const mainProductSkuSpecReqs = this.buildMainProductSkuSpecReqs(skuDataList)

      const dianxiaomiData: DianxiaomiProductData = {
        attributes: JSON.stringify(attributes),
        categoryId: config.basic.categoryId,
        shopId: config.basic.shopId,
        productSemiManagedReq: config.basic.businessSite,
        sourceUrl: spuData.sourceUrl || '',
        fullCid: "4547939-",
        productName: productName,
        productNameI18n: JSON.stringify({ en: spuData.title || '' }),
        outerGoodsUrl: spuData.sourceUrl || '',
        materialImgUrl: processedImages?.[0] || spuData.mainImageUrl || '',
        productOrigin: "CN",
        region2Id: config.basic.businessSite,
        originFileUrl: "",
        sensitiveAttr: "",
        personalizationSwitch: 0,
        mainImage: mainImage,
        dxmVideoId: "0",
        optionValue: "[]",
        mainProductSkuSpecReqs: mainProductSkuSpecReqs,
        goodsModel: "",
        variationListStr: variationListStr,
        productWarehouseRouteReq: productWarehouseRouteReq,
        dxmPdfUrl: "",
        qualifiedEn: "",
        instructionsId: "",
        instructionsName: "",
        description: description,
        instructionsTranslateId: "",
        freightTemplateId: config.basic.freightTemplate,
        shipmentLimitSecond: parseInt(config.basic.shippingTime) || 172800,
        op: 1,
        id: uniqueId,
        categoryType: 0,
        dxmState: config.basic.publishStatus === "2" ? "online" : "offline",
        productId: "0",
        sizeTemplateIds: ""
      }

      console.info('[DataMapperService] 数据映射完成')
      return dianxiaomiData

    } catch (error) {
      console.error('[DataMapperService] 数据映射失败:', error)
      throw new Error(`数据映射失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 构建属性数据
   */
  private buildAttributes(spuData: any, cachedAttributes: any): any[] {
    const attributes: any[] = []

    // 使用缓存的店小秘属性配置
    if (cachedAttributes && typeof cachedAttributes === 'object') {
      Object.keys(cachedAttributes).forEach(key => {
        const attr = cachedAttributes[key]
        if (attr && attr.propName) {
          attributes.push({
            propName: attr.propName,
            refPid: attr.refPid || 0,
            pid: attr.pid || 0,
            templatePid: attr.templatePid || 0,
            numberInputValue: "",
            valueUnit: "",
            vid: attr.vid || "",
            propValue: attr.propValue || attr.value || ""
          })
        }
      })
    }

    // 如果没有缓存属性，尝试从Amazon规格中映射
    if (attributes.length === 0 && spuData.specifications) {
      const specs = JSON.parse(spuData.specifications || '{}')
      Object.entries(specs).forEach(([key, value], index) => {
        attributes.push({
          propName: key,
          refPid: 1000 + index,
          pid: 500 + index,
          templatePid: 100000 + index,
          numberInputValue: "",
          valueUnit: "",
          vid: `${10000 + index}`,
          propValue: String(value)
        })
      })
    }

    return attributes
  }

  /**
   * 构建商品名称
   */
  private buildProductName(title: string, prefix: string, suffix: string): string {
    let productName = title.trim()

    if (prefix) {
      productName = `${prefix} ${productName}`
    }

    if (suffix) {
      productName = `${productName} ${suffix}`
    }

    // 限制长度
    if (productName.length > 200) {
      productName = productName.substring(0, 197) + '...'
    }

    return productName
  }

  /**
   * 构建主图片字符串
   */
  private buildMainImageString(imageUrls: string[]): string {
    return imageUrls.filter(url => url && url.trim()).join('|')
  }

  /**
   * 构建变体列表字符串
   */
  private buildVariationListStr(skuDataList: any[], config: MappingConfig): string {
    if (!skuDataList || skuDataList.length === 0) {
      // 单SKU商品
      return JSON.stringify([{
        id: this.generateUniqueId(),
        productSkuId: 0,
        supplierPrice: Math.round((parseFloat("10.00") || 10) * config.product.priceMultiplier * 100),
        extCode: "DEFAULT_SKU",
        length: config.product.defaultSize.length,
        width: config.product.defaultSize.width,
        height: config.product.defaultSize.height,
        weight: config.product.defaultSize.weight,
        codeType: "1",
        code: "",
        suggestedPrice: Math.round((parseFloat("10.00") || 10) * config.product.priceMultiplier * 100),
        suggestedPriceCurrencyType: "CNY",
        numberOfPieces: 1,
        skuClassification: "1",
        pieceUnitCode: "1",
        individuallyPacked: null,
        thumbUrl: "",
        productSkuSpecReqs: "[]",
        productSkuStockQuantityReq: JSON.stringify([{
          warehouseId: config.basic.warehouse,
          targetStockAvailable: config.product.fixedStock.toString()
        }]),
        sitePriceInfo: null
      }])
    }

    // 多SKU商品
    const variations = skuDataList.map(sku => {
      const price = parseFloat(sku.price) || 10
      const supplierPrice = Math.round(price * config.product.priceMultiplier * 100)

      return {
        id: this.generateUniqueId(),
        productSkuId: 0,
        supplierPrice: supplierPrice,
        extCode: sku.asin || 'UNKNOWN',
        length: config.product.defaultSize.length,
        width: config.product.defaultSize.width,
        height: config.product.defaultSize.height,
        weight: config.product.defaultSize.weight,
        codeType: "1",
        code: "",
        suggestedPrice: supplierPrice,
        suggestedPriceCurrencyType: "CNY",
        numberOfPieces: 1,
        skuClassification: "1",
        pieceUnitCode: "1",
        individuallyPacked: null,
        thumbUrl: sku.imageUrl || "",
        productSkuSpecReqs: sku.variationAttributes || "[]",
        productSkuStockQuantityReq: JSON.stringify([{
          warehouseId: config.basic.warehouse,
          targetStockAvailable: config.product.fixedStock.toString()
        }]),
        sitePriceInfo: null
      }
    })

    return JSON.stringify(variations)
  }

  /**
   * 构建商品描述
   */
  private buildDescription(spuData: any): string {
    const descriptionParts: any[] = []

    // 添加五点描述
    if (spuData.bulletPoints) {
      try {
        const bulletPoints = JSON.parse(spuData.bulletPoints)
        if (Array.isArray(bulletPoints) && bulletPoints.length > 0) {
          bulletPoints.forEach((point, index) => {
            descriptionParts.push({
              lang: "zh",
              type: "text",
              priority: index.toString(),
              contentList: [{
                text: point,
                textModuleDetails: {
                  fontFamily: null,
                  fontColor: "#000000",
                  backgroundColor: "#ffffff",
                  fontSize: "12",
                  align: "left"
                }
              }]
            })
          })
        }
      } catch (error) {
        console.warn('[DataMapperService] 解析bulletPoints失败:', error)
      }
    }

    return JSON.stringify(descriptionParts)
  }

  /**
   * 构建仓库路由
   */
  private buildWarehouseRoute(warehouseId: string, businessSite: string): string {
    return JSON.stringify([{
      warehouseId: warehouseId,
      warehouseName: "亚马逊",
      siteIdList: [businessSite]
    }])
  }

  /**
   * 构建主商品SKU规格
   */
  private buildMainProductSkuSpecReqs(skuDataList: any[]): string {
    if (!skuDataList || skuDataList.length === 0) {
      return JSON.stringify([{
        parentSpecId: 0,
        parentSpecName: "",
        specId: 0,
        specName: "",
        previewImgUrls: "",
        extCode: "DEFAULT_SKU",
        productSkcId: ""
      }])
    }

    const mainSku = skuDataList[0]
    return JSON.stringify([{
      parentSpecId: 0,
      parentSpecName: "",
      specId: 0,
      specName: "",
      previewImgUrls: mainSku.imageUrl || "",
      extCode: mainSku.asin || "UNKNOWN",
      productSkcId: ""
    }])
  }

  /**
   * 生成唯一ID
   */
  private generateUniqueId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }
}

export default DataMapperService.getInstance()
