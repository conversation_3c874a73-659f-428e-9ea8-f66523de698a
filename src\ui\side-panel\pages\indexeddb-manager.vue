<template>
  <div class="indexeddb-manager">
    <!-- 统计信息 -->
    <a-card title="📊 数据统计" class="stats-card" :loading="loading">
      <div class="stats-row">
        <a-space size="large" wrap>
          <a-statistic title="总记录数" :value="stats.count" class="inline-stat" />
          <a-statistic title="实际数据" :value="stats.totalRecords" class="inline-stat" />
          <a-statistic title="最早记录" :value="oldestRecordText" class="inline-stat" />
          <a-statistic title="最新记录" :value="newestRecordText" class="inline-stat" />
        </a-space>
      </div>
    </a-card>

    <!-- 数据列表 -->
    <a-card title="📝 数据列表" class="data-list-card">
      <!-- 搜索、排序和操作控制 -->
      <div class="search-controls" style="margin-bottom: 16px;">
        <a-row :gutter="12" align="middle">
          <a-col :span="6">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="按主键搜索..."
              @search="handleSearch"
              @change="handleSearch"
              allowClear
              size="small"
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="sortOrder"
              placeholder="排序方式"
              style="width: 100%"
              @change="handleSortChange"
              size="small"
            >
              <a-select-option value="desc">时间倒序</a-select-option>
              <a-select-option value="asc">时间正序</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="14">
            <div class="flex justify-between items-center">
              <a-space size="small">
                <a-button size="small" type="primary" @click="refreshStats" :loading="loading">
                  <template #icon><ReloadOutlined /></template>
                  刷新统计
                </a-button>
                <a-button size="small" @click="loadAllData" :loading="loadingData">
                  <template #icon><ReloadOutlined /></template>
                  刷新数据
                </a-button>
                <a-button size="small" @click="cleanExpiredData" :loading="cleaningData">
                  <template #icon><DeleteOutlined /></template>
                  清理过期
                </a-button>
                <a-button size="small" @click="exportData">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
                <a-button size="small" @click="showDetailedAnalysis">
                  <template #icon><BarChartOutlined /></template>
                  分析
                </a-button>
              </a-space>
              <a-tag color="blue">共 {{ filteredData.length }} 条记录</a-tag>
            </div>
          </a-col>
        </a-row>
      </div>     

      <!-- 表格容器 -->
      <div class="table-container">
        <a-table
          :dataSource="paginatedData"
          :columns="dataColumns"
          :pagination="false"
          size="small"
          :loading="loadingData"
          bordered
          :scroll="{ x: 1200, y: 'calc(100vh - 520px)' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'timestamp'">
              {{ formatTimestamp(record.t) }}
            </template>
            <template v-else-if="column.key === 'price'">
              <a-tag v-if="record.parsedData?.price" color="green">
                ${{ record.parsedData.price }}
              </a-tag>
              <span v-else class="text-gray-400">-</span>
            </template>
            <template v-else-if="column.key === 'stock'">
              <a-tag v-if="record.parsedData?.stock" color="blue">
                {{ record.parsedData.stock }}
              </a-tag>
              <span v-else class="text-gray-400">-</span>
            </template>
            <template v-else-if="column.key === 'image'">
              <div v-if="record.parsedData?.imageUrl" class="image-cell">
                <a-image
                  :src="record.parsedData.imageUrl"
                  :width="40"
                  :height="40"
                  :preview="true"
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              </div>
              <span v-else class="text-gray-400">无图片</span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button size="small" @click="viewRecord(record)">详情</a-button>
                <a-button size="small" type="primary" @click="copyToClipboard(record.id)">
                  复制ID
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 自定义分页控件 -->
      <div class="custom-pagination">
        <a-pagination
          :current="currentPage"
          :page-size="pageSize"
          :total="filteredData.length || 0"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          :page-size-options="['20', '50', '100', '200', '500', '1000']"
          size="small"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </a-card>

    <!-- 详细分析模态框 -->
    <a-modal
      v-model:open="analysisModalVisible"
      title="📊 详细数据分析"
      width="80%"
      :footer="null"
    >
      <div v-if="analysisData">
        <a-descriptions title="数据概览" bordered>
          <a-descriptions-item label="总记录数">{{ analysisData.totalCount }}</a-descriptions-item>
          <a-descriptions-item label="时间跨度">{{ getTimeSpan() }}</a-descriptions-item>
          <a-descriptions-item label="数据完整性">{{ getDataIntegrity() }}</a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <a-row :gutter="16">
          <a-col :span="12">
            <h4>键值模式分布</h4>
            <a-table 
              :dataSource="keyPatternsData" 
              :columns="[
                { title: '模式', dataIndex: 'pattern', key: 'pattern' },
                { title: '数量', dataIndex: 'count', key: 'count' }
              ]"
              :pagination="false"
              size="small"
            />
          </a-col>
          <a-col :span="12">
            <h4>数据结构类型</h4>
            <a-table 
              :dataSource="dataTypesData" 
              :columns="[
                { title: '类型', dataIndex: 'type', key: 'type' },
                { title: '数量', dataIndex: 'count', key: 'count' }
              ]"
              :pagination="false"
              size="small"
            />
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 记录详情模态框 -->
    <a-modal
      v-model:open="recordModalVisible"
      title="📄 记录详情"
      width="60%"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions title="基本信息" bordered>
          <a-descriptions-item label="ID">{{ selectedRecord.id }}</a-descriptions-item>
          <a-descriptions-item label="时间戳">{{ formatTimestamp(selectedRecord.t) }}</a-descriptions-item>
          <a-descriptions-item label="数据大小">{{ getDataSize(selectedRecord) }} 字节</a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <h4>数据内容</h4>
        <pre class="json-display">{{ formatJSON(selectedRecord.d) }}</pre>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, DeleteOutlined, DownloadOutlined, BarChartOutlined } from '@ant-design/icons-vue'
import { IndexedDBDebugManager } from '@/utils/indexedDBManager'

// 响应式数据
const loading = ref(false)
const cleaningData = ref(false)
const loadingData = ref(false)
const stats = ref({
  count: 0,
  totalRecords: 0,
  sampleData: [],
  oldestRecord: null,
  newestRecord: null
})

const analysisModalVisible = ref(false)
const recordModalVisible = ref(false)
const analysisData = ref(null)
const selectedRecord = ref(null)

// 数据列表相关
const allData = ref([])
const searchKeyword = ref('')
const sortOrder = ref('desc') // 'asc' | 'desc'

// 创建IndexedDB管理器实例
const dbManager = new IndexedDBDebugManager()

// 数据列表表格列定义
const dataColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }) => {
      return (currentPage.value - 1) * pageSize.value + index + 1
    }
  },
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 200,
    ellipsis: true,
    sorter: (a, b) => a.id.localeCompare(b.id)
  },
  {
    title: '时间',
    key: 'timestamp',
    width: 180,
    sorter: (a, b) => (a.t || 0) - (b.t || 0)
  },
  {
    title: '价格',
    key: 'price',
    width: 100,
    sorter: (a, b) => (a.parsedData?.price || 0) - (b.parsedData?.price || 0)
  },
  {
    title: '库存',
    key: 'stock',
    width: 80,
    sorter: (a, b) => (a.parsedData?.stock || 0) - (b.parsedData?.stock || 0)
  },
  {
    title: '图片',
    key: 'image',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 分页状态
const currentPage = ref(1)
const pageSize = ref(100)
const jumpToPage = ref(1)

// 分页数据
const paginatedData = computed(() => {
  if (!filteredData.value || filteredData.value.length === 0) {
    return []
  }
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 分页配置
const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: filteredData.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
  size: 'small',
  onChange: (page: number, size: number) => {
    currentPage.value = page
    pageSize.value = size
  },
  onShowSizeChange: (current: number, size: number) => {
    currentPage.value = 1 // 改变页面大小时重置到第一页
    pageSize.value = size
  }
}))

// 计算属性
const oldestRecordText = computed(() => {
  if (stats.value.oldestRecord?.t) {
    return new Date(stats.value.oldestRecord.t).toLocaleString()
  }
  return '-'
})

const newestRecordText = computed(() => {
  if (stats.value.newestRecord?.t) {
    return new Date(stats.value.newestRecord.t).toLocaleString()
  }
  return '-'
})

// 过滤和排序后的数据
const filteredData = computed(() => {
  let data = [...allData.value]

  // 搜索过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    data = data.filter(item =>
      item.id?.toLowerCase().includes(keyword)
    )
  }

  // 时间排序
  data.sort((a, b) => {
    const timeA = a.t || 0
    const timeB = b.t || 0
    return sortOrder.value === 'desc' ? timeB - timeA : timeA - timeB
  })

  return data
})

const keyPatternsData = computed(() => {
  if (!analysisData.value?.keyPatterns) return []
  return Object.entries(analysisData.value.keyPatterns).map(([pattern, count]) => ({
    pattern,
    count
  }))
})

const dataTypesData = computed(() => {
  if (!analysisData.value?.dataTypes) return []
  return Object.entries(analysisData.value.dataTypes).map(([type, count]) => ({
    type,
    count
  }))
})

// 数据解析函数
const parseRecordData = (record) => {
  const parsed = {
    price: null,
    stock: null,
    imageUrl: null
  }

  try {
    if (record.d && typeof record.d === 'string') {
      // 解析JSON字符串格式的数据
      const data = JSON.parse(record.d)
      if (Array.isArray(data) && data[0]) {
        parsed.price = data[0].p || data[0].price
        parsed.stock = data[0].s || data[0].stock
        parsed.imageUrl = data[0].img || data[0].imageUrl
      }
    } else if (record.data) {
      // 直接数据格式
      parsed.price = record.data.usdPrice || record.data.price
      parsed.stock = record.data.stock
      parsed.imageUrl = record.data.imageUrl
    } else {
      // 直接属性格式
      parsed.price = record.p || record.price
      parsed.stock = record.s || record.stock
      parsed.imageUrl = record.img || record.imageUrl
    }
  } catch (error) {
    console.warn('解析记录数据失败:', error, record)
  }

  return parsed
}

// 方法
const refreshStats = async () => {
  loading.value = true
  try {
    const result = await dbManager.getStats()
    stats.value = result
    message.success('统计信息已刷新')
  } catch (error) {
    console.error('刷新统计失败:', error)
    message.error('刷新统计失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载所有数据
const loadAllData = async () => {
  loadingData.value = true
  try {
    const data = await dbManager.getAllDataDirect()
    // 为每条记录添加解析后的数据
    allData.value = data.map(record => ({
      ...record,
      parsedData: parseRecordData(record)
    }))
    message.success(`已加载 ${data.length} 条记录`)
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败: ' + error.message)
  } finally {
    loadingData.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 搜索时重置到第一页
}

// 排序变化处理
const handleSortChange = () => {
  currentPage.value = 1 // 排序时重置到第一页
}

// 分页处理方法
const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  if (size) {
    pageSize.value = size
  }
}

const handlePageSizeChange = (current: number, size: number) => {
  currentPage.value = 1 // 改变页面大小时重置到第一页
  pageSize.value = size
  jumpToPage.value = 1
}

// 从下拉选择器改变页面大小
const handlePageSizeChangeFromSelect = (size: number) => {
  currentPage.value = 1 // 改变页面大小时重置到第一页
  pageSize.value = size
  jumpToPage.value = 1
}

const handleJumpToPage = () => {
  const maxPage = Math.ceil((filteredData.value?.length || 0) / pageSize.value) || 1
  if (jumpToPage.value >= 1 && jumpToPage.value <= maxPage) {
    currentPage.value = jumpToPage.value
  } else {
    message.warning(`页码范围：1-${maxPage}`)
    jumpToPage.value = currentPage.value
  }
}

const cleanExpiredData = async () => {
  cleaningData.value = true
  try {
    const deletedCount = await dbManager.cleanExpiredData()
    message.success(`已清理 ${deletedCount} 条过期数据`)
    await refreshStats() // 刷新统计
  } catch (error) {
    console.error('清理数据失败:', error)
    message.error('清理数据失败: ' + error.message)
  } finally {
    cleaningData.value = false
  }
}

const exportData = async () => {
  try {
    const allData = await dbManager.getAllDataDirect()
    const dataStr = JSON.stringify(allData, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `indexeddb-export-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    message.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    message.error('导出数据失败: ' + error.message)
  }
}

const showDetailedAnalysis = async () => {
  try {
    analysisData.value = await dbManager.analyzeData()
    analysisModalVisible.value = true
  } catch (error) {
    console.error('分析数据失败:', error)
    message.error('分析数据失败: ' + error.message)
  }
}

const viewRecord = (record: any) => {
  selectedRecord.value = record
  recordModalVisible.value = true
}

const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

const formatJSON = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

const getDataSize = (record: any) => {
  return JSON.stringify(record).length
}

const getTimeSpan = () => {
  if (!analysisData.value?.timeRange?.oldest || !analysisData.value?.timeRange?.newest) {
    return '-'
  }
  const days = Math.round(
    (analysisData.value.timeRange.newest.getTime() - analysisData.value.timeRange.oldest.getTime()) / 
    (1000 * 60 * 60 * 24)
  )
  return `${days} 天`
}

const getDataIntegrity = () => {
  if (!analysisData.value) return '-'
  const hasValidStructure = Object.keys(analysisData.value.dataTypes).length > 0
  return hasValidStructure ? '✅ 良好' : '❌ 异常'
}

// 生命周期
onMounted(async () => {
  await refreshStats()
  await loadAllData()
})
</script>

<style scoped>
.indexeddb-manager {
  padding: 16px;
  height: calc(100vh - 32px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.description {
  color: #666;
  margin: 0;
}

.stats-card,
.operations-card,
.sample-data-card {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.data-list-card {
  margin-bottom: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-list-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding-bottom: 80px; /* 为分页器留出空间 */
}

.table-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.table-container .ant-table-wrapper {
  height: 100%;
}

.table-container .ant-table {
  height: 100%;
}

.table-container .ant-table-container {
  height: calc(100vh - 520px);
  overflow: auto;
}

.pagination-controls {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.control-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.pagination-info {
  text-align: right;
}

.custom-pagination {
  margin-top: 16px;
  text-align: center;
  padding: 16px 0 24px 0;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
  background: white;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.json-display {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.image-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-cell .ant-image {
  border-radius: 4px;
  overflow: hidden;
}

.search-controls {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.data-list-card .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.text-gray-400 {
  color: #9ca3af;
}

.inline-stat {
  text-align: center;
  min-width: 120px;
}

.inline-stat .ant-statistic-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.inline-stat .ant-statistic-content {
  font-size: 16px;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

/* 确保滚动条可见 */
:deep(.ant-table-body) {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

:deep(.ant-table-container) {
  overflow: auto !important;
}

/* 分页器样式优化 */
:deep(.ant-pagination) {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

:deep(.ant-pagination-options) {
  margin-left: 16px;
}

/* 确保分页器不被遮盖 */
.data-list-card {
  position: relative;
}

.custom-pagination {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 为分页器预留空间 */
.table-container {
  margin-bottom: 80px;
}
</style>
