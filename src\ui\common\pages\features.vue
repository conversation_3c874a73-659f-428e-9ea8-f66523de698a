<script setup lang="ts">
const features = ref([
  {
    title: "商品批量采集",
    description: "支持从Amazon、Temu等平台批量采集商品信息，包括标题、价格、图片、描述等详细数据，大幅提升上架效率。",
    icon: "🛍️"
  },
  {
    title: "智能价格监控",
    description: "实时监控竞品价格变化，自动调整商品定价策略，确保价格竞争力，提升销售转化率。",
    icon: "💰"
  },
  {
    title: "多店铺管理",
    description: "统一管理多个Temu店铺账号，支持批量操作，简化多店铺运营流程，提高管理效率。",
    icon: "🏪"
  },
  {
    title: "自动化上传",
    description: "智能商品信息组装和批量上传功能，支持自定义模板，减少人工操作，降低出错率。",
    icon: "⚡"
  },
  {
    title: "数据分析报表",
    description: "提供详细的销售数据分析和可视化报表，帮助您了解店铺运营状况，制定优化策略。",
    icon: "📊"
  },
  {
    title: "库存管理",
    description: "实时同步库存信息，支持多仓库管理，避免超卖和缺货情况，优化供应链管理。",
    icon: "📦"
  },
  {
    title: "订单处理",
    description: "自动化订单处理流程，支持批量发货、物流跟踪，提升订单处理效率和客户满意度。",
    icon: "📋"
  },
  {
    title: "客户服务",
    description: "集成客户消息管理，快速响应客户咨询，提供专业的售后服务支持。",
    icon: "💬"
  },
  {
    title: "合规检查",
    description: "自动检查商品信息合规性，避免违规操作，确保店铺安全运营。",
    icon: "✅"
  },
  {
    title: "运营优化",
    description: "基于数据分析提供运营建议，优化商品标题、描述、关键词等，提升搜索排名。",
    icon: "🚀"
  },
  {
    title: "API集成",
    description: "支持与第三方ERP系统、物流平台等集成，构建完整的电商生态系统。",
    icon: "🔗"
  },
  {
    title: "安全保障",
    description: "采用企业级安全标准，保护用户数据和账号安全，确保稳定可靠的服务。",
    icon: "🔒"
  }
])

const additionalFeatures = [
  {
    title: "智能翻译",
    description: "内置多语言翻译功能，自动翻译商品标题和描述，支持全球化销售。"
  },
  {
    title: "图片处理",
    description: "自动优化商品图片，支持批量水印添加、尺寸调整等功能。"
  },
  {
    title: "关键词优化",
    description: "基于搜索热度和竞争分析，智能推荐最佳关键词组合。"
  },
  {
    title: "定时任务",
    description: "支持定时执行各种操作，如价格更新、库存同步等自动化任务。"
  },
  {
    title: "数据备份",
    description: "自动备份重要数据，确保数据安全，支持一键恢复功能。"
  },
  {
    title: "团队协作",
    description: "支持多用户协作，权限管理，适合团队化运营管理。"
  }
]
</script>

<template>
  <div class="p-6 max-w-6xl mx-auto">
    <RouterLinkUp />

    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">功能特色</h1>
      <p class="text-lg text-gray-600">胡建大卖家为您提供全方位的Temu店铺管理解决方案</p>
    </div>

    <a-row :gutter="[24, 24]" class="mb-12">
      <a-col
        v-for="{ title, description, icon } in features"
        :key="title"
        :xs="24" :sm="12" :lg="8"
      >
        <a-card
          class="h-full hover:shadow-lg transition-shadow"
          :bordered="false"
        >
          <a-space direction="vertical" :size="16" class="w-full">
            <a-space align="center" :size="12">
              <span class="text-3xl">{{ icon }}</span>
              <h3 class="text-xl font-semibold text-gray-800">{{ title }}</h3>
            </a-space>
            <p class="text-gray-600 leading-relaxed">{{ description }}</p>
          </a-space>
        </a-card>
      </a-col>
    </a-row>

    <a-card class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg mb-8" :bordered="false">
      <h2 class="text-2xl font-bold text-center mb-6 text-gray-800">更多强大功能</h2>
      <a-row :gutter="[16, 16]">
        <a-col
          v-for="{ title, description } in additionalFeatures"
          :key="title"
          :xs="24" :sm="12" :lg="8"
        >
          <a-card class="h-full" :bordered="false">
            <h4 class="font-semibold text-gray-800 mb-2">{{ title }}</h4>
            <p class="text-sm text-gray-600">{{ description }}</p>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <div class="text-center bg-gray-50 rounded-lg p-8">
      <h2 class="text-2xl font-bold mb-4 text-gray-800">持续更新，不断完善</h2>
      <p class="text-gray-600 mb-6 max-w-3xl mx-auto">
        我们致力于为用户提供最优质的服务体验。基于用户反馈和市场需求，
        我们会持续优化现有功能，并不断推出新的实用工具，
        确保您始终拥有最先进的店铺管理解决方案。
      </p>
      <div class="flex flex-wrap gap-4 justify-center">
        <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
          定期功能更新
        </div>
        <div class="bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
          7×24小时技术支持
        </div>
        <div class="bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium">
          用户反馈驱动
        </div>
        <div class="bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-medium">
          免费功能升级
        </div>
      </div>
    </div>
  </div>
</template>
