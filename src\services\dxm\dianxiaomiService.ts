// 店小秘服务
// 处理与店小秘平台的 API 交互

import { getDianxiaomiApiUrl, config } from '../../config'

// 店小秘账号凭据
interface DianxiaomiCredentials {
  username: string
  password: string
  apiKey?: string
  secretKey?: string
}

// 店小秘绑定信息
interface DianxiaomiBindingInfo {
  userId: string
  credentials: DianxiaomiCredentials
  shopInfo?: {
    shopId: string
    shopName: string
    platform: string
  }
}

// 验证结果
interface VerifyResult {
  success: boolean
  message: string
  data?: {
    accountId: string
    accountName: string
    permissions: string[]
    shopList?: Array<{
      shopId: string
      shopName: string
      platform: string
      status: string
    }>
  }
  error?: {
    code: number
    message: string
  }
}

// 绑定结果
interface BindingResult {
  success: boolean
  message: string
  data?: {
    bindingId: string
    accountId: string
    bindTime: string
    status: string
  }
  error?: {
    code: number
    message: string
  }
}

// 绑定状态
interface BindingStatus {
  success: boolean
  data?: {
    isBound: boolean
    bindingInfo?: {
      bindingId: string
      accountId: string
      accountName: string
      bindTime: string
      status: string
      shopList: Array<{
        shopId: string
        shopName: string
        platform: string
        status: string
      }>
    }
    lastCheckTime: string
  }
  error?: {
    code: number
    message: string
  }
}

// 解绑结果
interface UnbindResult {
  success: boolean
  message: string
  error?: {
    code: number
    message: string
  }
}

class DianxiaomiService {
  private userId: string | null = null

  constructor() {
    this.initializeUserId()
  }

  // 初始化用户ID
  private async initializeUserId() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['userId', 'userInfo'])
        this.userId = result.userId || result.userInfo?.userId || null
      } else {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const parsed = JSON.parse(userInfo)
          this.userId = parsed.userId || null
        }
      }
    } catch (error) {
      console.error('[DianxiaomiService] 初始化用户ID失败:', error)
    }
  }

  // 设置用户ID
  public setUserId(userId: string) {
    this.userId = userId
  }

  // 获取请求头
  private getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': navigator.userAgent,
      // 在实际项目中，这里应该添加认证token
      // 'Authorization': `Bearer ${this.getAuthToken()}`
    }
  }

  // 验证店小秘账号
  public async verifyAccount(credentials: DianxiaomiCredentials): Promise<VerifyResult> {
    try {
      console.log('[DianxiaomiService] 验证店小秘账号...')

      const response = await fetch(getDianxiaomiApiUrl('/account/verify'), {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          credentials,
          timeout: config.dianxiaomi.timeout
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('[DianxiaomiService] 账号验证成功:', result)
        return result
      } else {
        console.error('[DianxiaomiService] 账号验证失败:', result)
        return {
          success: false,
          message: result.message || '账号验证失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[DianxiaomiService] 验证请求失败:', error)
      return {
        success: false,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 绑定店小秘账号
  public async bindAccount(bindingInfo: DianxiaomiBindingInfo): Promise<BindingResult> {
    try {
      if (!this.userId) {
        return {
          success: false,
          message: '用户未登录，请先登录',
          error: { code: 4001, message: 'User not authenticated' }
        }
      }

      console.log('[DianxiaomiService] 绑定店小秘账号...')

      const response = await fetch(getDianxiaomiApiUrl('/account/bind'), {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({
          userId: this.userId,
          ...bindingInfo
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('[DianxiaomiService] 账号绑定成功:', result)
        return result
      } else {
        console.error('[DianxiaomiService] 账号绑定失败:', result)
        return {
          success: false,
          message: result.message || '绑定失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[DianxiaomiService] 绑定请求失败:', error)
      return {
        success: false,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 检查绑定状态
  public async checkBindingStatus(): Promise<BindingStatus> {
    try {
      if (!this.userId) {
        return {
          success: false,
          error: { code: 4001, message: 'User not authenticated' }
        }
      }

      console.log('[DianxiaomiService] 检查绑定状态...')

      const response = await fetch(getDianxiaomiApiUrl(`/account/binding-status?userId=${this.userId}`), {
        method: 'GET',
        headers: this.getHeaders()
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('[DianxiaomiService] 绑定状态检查成功:', result)
        return result
      } else {
        console.error('[DianxiaomiService] 绑定状态检查失败:', result)
        return {
          success: false,
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[DianxiaomiService] 状态检查请求失败:', error)
      return {
        success: false,
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 解绑账号
  public async unbindAccount(): Promise<UnbindResult> {
    try {
      if (!this.userId) {
        return {
          success: false,
          message: '用户未登录',
          error: { code: 4001, message: 'User not authenticated' }
        }
      }

      console.log('[DianxiaomiService] 解绑店小秘账号...')

      const response = await fetch(getDianxiaomiApiUrl('/account/unbind'), {
        method: 'DELETE',
        headers: this.getHeaders(),
        body: JSON.stringify({
          userId: this.userId
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('[DianxiaomiService] 账号解绑成功:', result)
        return result
      } else {
        console.error('[DianxiaomiService] 账号解绑失败:', result)
        return {
          success: false,
          message: result.message || '解绑失败',
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[DianxiaomiService] 解绑请求失败:', error)
      return {
        success: false,
        message: '网络错误，请检查网络连接',
        error: { code: 5000, message: 'Network error' }
      }
    }
  }

  // 获取店铺列表
  public async getShopList(): Promise<{ success: boolean; data?: any[]; error?: any }> {
    try {
      if (!this.userId) {
        return {
          success: false,
          error: { code: 4001, message: 'User not authenticated' }
        }
      }

      console.log('[DianxiaomiService] 获取店铺列表...')

      const response = await fetch(getDianxiaomiApiUrl(`/shops?userId=${this.userId}`), {
        method: 'GET',
        headers: this.getHeaders()
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('[DianxiaomiService] 获取店铺列表成功:', result)
        return {
          success: true,
          data: result.data || []
        }
      } else {
        console.error('[DianxiaomiService] 获取店铺列表失败:', result)
        return {
          success: false,
          error: result.error || { code: response.status, message: response.statusText }
        }
      }
    } catch (error) {
      console.error('[DianxiaomiService] 获取店铺列表请求失败:', error)
      return {
        success: false,
        error: { code: 5000, message: 'Network error' }
      }
    }
  }
}

// 创建单例实例
export const dianxiaomiService = new DianxiaomiService()
export default dianxiaomiService

// 导出类型
export type {
  DianxiaomiCredentials,
  DianxiaomiBindingInfo,
  VerifyResult,
  BindingResult,
  BindingStatus,
  UnbindResult
}
