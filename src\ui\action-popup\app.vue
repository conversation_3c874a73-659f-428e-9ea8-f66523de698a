<script setup lang="ts">
// 移除暗黑主题，使用明亮主题
</script>

<template>
  <div class="popup-app">
    <RouterView />
  </div>
</template>

<style>
/* Chrome Extension Popup 根容器样式 */
.popup-app {
  width: 420px;
  height: 600px;
  max-height: 600px;
  background-color: white;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

/* 确保弹出窗口有合适的尺寸 */
:deep(.ant-card) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.ant-btn) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 确保内容可见 */
* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
}
</style>
