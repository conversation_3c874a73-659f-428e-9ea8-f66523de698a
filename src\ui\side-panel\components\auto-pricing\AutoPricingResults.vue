<template>
  <div class="results-container">
    <!-- 结果统计 -->
    <a-card title="核价结果统计" size="small" class="summary-card">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic 
            title="核价成功数据" 
            :value="approvedResults.length" 
            :value-style="{ color: '#52c41a' }"
          />
          <div class="rate-text">
            首次申报通过率：{{ stats.firstTimeApprovalRate }}%
          </div>
        </a-col>
        <a-col :span="8">
          <a-statistic 
            title="重新报价数据" 
            :value="repricedResults.length" 
            :value-style="{ color: '#faad14' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic 
            title="拒绝核价数据" 
            :value="rejectedResults.length" 
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-col>
      </a-row>
      <div class="overall-rate">
        当前通过率：{{ stats.approvalRate.toFixed(2) }}%
      </div>
    </a-card>

    <!-- 结果表格 -->
    <a-tabs v-model:activeKey="activeTab" class="results-tabs">
      <!-- 核价成功 -->
      <a-tab-pane key="approved" :tab="`核价成功 (${approvedResults.length})`">
        <a-table 
          :dataSource="approvedResults" 
          :columns="approvedColumns"
          :pagination="{ pageSize: 20 }"
          size="small"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'profitRate'">
              <span :class="getProfitRateClass(record.profitRate)">
                {{ record.profitRate.toFixed(2) }}%
              </span>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag color="success">{{ record.remark }}</a-tag>
            </template>
          </template>
        </a-table>
      </a-tab-pane>

      <!-- 重新报价 -->
      <a-tab-pane key="repriced" :tab="`重新报价 (${repricedResults.length})`">
        <a-table 
          :dataSource="repricedResults" 
          :columns="repricedColumns"
          :pagination="{ pageSize: 20 }"
          size="small"
          :scroll="{ x: 1400 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'originalProfitRate'">
              <span :class="getProfitRateClass(record.originalProfitRate)">
                {{ record.originalProfitRate.toFixed(2) }}%
              </span>
            </template>
            <template v-else-if="column.key === 'newProfitRate'">
              <span :class="getProfitRateClass(record.newProfitRate)">
                {{ record.newProfitRate.toFixed(2) }}%
              </span>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag color="warning">{{ record.remark }}</a-tag>
            </template>
          </template>
        </a-table>
      </a-tab-pane>

      <!-- 拒绝核价 -->
      <a-tab-pane key="rejected" :tab="`拒绝核价 (${rejectedResults.length})`">
        <a-table 
          :dataSource="rejectedResults" 
          :columns="rejectedColumns"
          :pagination="{ pageSize: 20 }"
          size="small"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'profitRate'">
              <span :class="getProfitRateClass(record.profitRate)">
                {{ record.profitRate.toFixed(2) }}%
              </span>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag color="error">{{ record.remark }}</a-tag>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface PricingResult {
  spu: string
  skc: string
  sku: string
  originalPrice: number
  suggestedPrice: number
  reviewTimes: number
  finalPrice: number
  extCode: string
  stock: number
  costPrice: number
  profitRate: number
  status: 'approved' | 'repriced' | 'rejected'
  remark: string
  // 重新报价特有字段
  originalProfitRate?: number
  newProfitRate?: number
}

interface Props {
  approvedResults: PricingResult[]
  repricedResults: PricingResult[]
  rejectedResults: PricingResult[]
  stats: {
    total: number
    processed: number
    approved: number
    repriced: number
    rejected: number
    successRate: number
    approvalRate: number
    firstTimeApprovalRate: number
  }
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('approved')

// 表格列定义
const approvedColumns = [
  { title: 'SPU', dataIndex: 'spu', key: 'spu', width: 120 },
  { title: 'SKC', dataIndex: 'skc', key: 'skc', width: 120 },
  { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 120 },
  { title: '原申报价', dataIndex: 'originalPrice', key: 'originalPrice', width: 100 },
  { title: '官方建议报价', dataIndex: 'suggestedPrice', key: 'suggestedPrice', width: 120 },
  { title: '核价次数', dataIndex: 'reviewTimes', key: 'reviewTimes', width: 80 },
  { title: '通过价格', dataIndex: 'finalPrice', key: 'finalPrice', width: 100 },
  { title: '货号', dataIndex: 'extCode', key: 'extCode', width: 150 },
  { title: '货盘库存', dataIndex: 'stock', key: 'stock', width: 80 },
  { title: '成本价格', dataIndex: 'costPrice', key: 'costPrice', width: 100 },
  { title: '利润率', dataIndex: 'profitRate', key: 'profitRate', width: 80 },
  { title: '其他', dataIndex: 'remark', key: 'status', width: 120 }
]

const repricedColumns = [
  { title: 'SPU', dataIndex: 'spu', key: 'spu', width: 120 },
  { title: 'SKC', dataIndex: 'skc', key: 'skc', width: 120 },
  { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 120 },
  { title: '原申报价', dataIndex: 'originalPrice', key: 'originalPrice', width: 100 },
  { title: '官方建议报价', dataIndex: 'suggestedPrice', key: 'suggestedPrice', width: 120 },
  { title: '核价次数', dataIndex: 'reviewTimes', key: 'reviewTimes', width: 80 },
  { title: '重报价格', dataIndex: 'finalPrice', key: 'finalPrice', width: 100 },
  { title: '货号', dataIndex: 'extCode', key: 'extCode', width: 150 },
  { title: '货盘库存', dataIndex: 'stock', key: 'stock', width: 80 },
  { title: '成本价格', dataIndex: 'costPrice', key: 'costPrice', width: 100 },
  { title: '利润率(官方建议价)', dataIndex: 'originalProfitRate', key: 'originalProfitRate', width: 140 },
  { title: '重报利润率', dataIndex: 'newProfitRate', key: 'newProfitRate', width: 100 },
  { title: '其他', dataIndex: 'remark', key: 'status', width: 120 }
]

const rejectedColumns = [
  { title: 'SPU', dataIndex: 'spu', key: 'spu', width: 120 },
  { title: 'SKC', dataIndex: 'skc', key: 'skc', width: 120 },
  { title: 'SKU', dataIndex: 'sku', key: 'sku', width: 120 },
  { title: '原申报价', dataIndex: 'originalPrice', key: 'originalPrice', width: 100 },
  { title: '官方建议报价', dataIndex: 'suggestedPrice', key: 'suggestedPrice', width: 120 },
  { title: '核价次数', dataIndex: 'reviewTimes', key: 'reviewTimes', width: 80 },
  { title: '货号', dataIndex: 'extCode', key: 'extCode', width: 150 },
  { title: '货盘库存', dataIndex: 'stock', key: 'stock', width: 80 },
  { title: '成本价格', dataIndex: 'costPrice', key: 'costPrice', width: 100 },
  { title: '利润率', dataIndex: 'profitRate', key: 'profitRate', width: 80 },
  { title: '其他', dataIndex: 'remark', key: 'status', width: 120 }
]

// 工具函数
const getProfitRateColor = (rate: number) => {
  if (rate >= 40) return '#52c41a'
  if (rate >= 20) return '#faad14'
  return '#ff4d4f'
}

// 获取利润率CSS类名
const getProfitRateClass = (rate: number) => {
  if (rate >= 40) return 'profit-rate-high'
  if (rate >= 20) return 'profit-rate-medium'
  return 'profit-rate-low'
}
</script>

<style scoped>
.results-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.summary-card {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.rate-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.overall-rate {
  text-align: center;
  margin-top: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

.results-tabs {
  flex: 1;
  overflow: hidden;
}

:deep(.ant-tabs-content-holder) {
  overflow: auto;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px;
}

:deep(.ant-table-thead > tr > th) {
  padding: 8px;
  font-size: 12px;
}

/* 利润率颜色类 */
.profit-rate-high {
  color: #52c41a;
}

.profit-rate-medium {
  color: #faad14;
}

.profit-rate-low {
  color: #ff4d4f;
}
</style>
