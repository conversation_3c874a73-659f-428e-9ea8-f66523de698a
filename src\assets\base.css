/* ========================================
   Chrome Extension V3 专用设计系统
   基于 Ant Design Vue + CSS变量
======================================== */

/* Chrome Extension 环境基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  overflow: hidden;
  background-color: #fafafa;
}

#app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* ========================================
   设计Token系统 - 统一的设计变量
======================================== */
:root {
  /* 品牌色彩系统 - 基于Temu业务特色 */
  --brand-primary: #1677ff;
  --brand-primary-light: #4096ff;
  --brand-primary-dark: #0958d9;
  --brand-secondary: #722ed1;
  --brand-accent: #13c2c2;

  /* 功能色彩 */
  --color-success: #52c41a;
  --color-success-light: #73d13d;
  --color-warning: #faad14;
  --color-warning-light: #ffc53d;
  --color-error: #ff4d4f;
  --color-error-light: #ff7875;
  --color-info: #1677ff;
  --color-info-light: #4096ff;

  /* 中性色彩系统 */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #f0f0f0;
  --gray-300: #d9d9d9;
  --gray-400: #bfbfbf;
  --gray-500: #8c8c8c;
  --gray-600: #595959;
  --gray-700: #434343;
  --gray-800: #262626;
  --gray-900: #1f1f1f;

  /* 语义化颜色 */
  --text-primary: var(--gray-800);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-500);
  --text-quaternary: var(--gray-400);
  --text-disabled: var(--gray-300);

  /* 背景色系统 */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-container: #ffffff;
  --bg-elevated: #ffffff;

  /* 边框色系统 */
  --border-primary: var(--gray-300);
  --border-secondary: var(--gray-200);
  --border-light: var(--gray-100);

  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  --space-3xl: 32px;

  /* 字体大小系统 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;

  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* 字重系统 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}

/* ========================================
   Ant Design Vue 主题定制
======================================== */

/* 将设计Token映射到Ant Design Vue变量 */
:root {
  /* 主色调映射 */
  --ant-primary-color: var(--brand-primary);
  --ant-primary-color-hover: var(--brand-primary-light);
  --ant-primary-color-active: var(--brand-primary-dark);

  /* 功能色映射 */
  --ant-success-color: var(--color-success);
  --ant-warning-color: var(--color-warning);
  --ant-error-color: var(--color-error);
  --ant-info-color: var(--color-info);

  /* 文本色映射 */
  --ant-text-color: var(--text-primary);
  --ant-text-color-secondary: var(--text-secondary);
  --ant-text-color-tertiary: var(--text-tertiary);
  --ant-disabled-color: var(--text-disabled);

  /* 背景色映射 */
  --ant-component-background: var(--bg-container);
  --ant-body-background: var(--bg-secondary);
  --ant-layout-body-background: var(--bg-secondary);

  /* 边框色映射 */
  --ant-border-color-base: var(--border-primary);
  --ant-border-color-split: var(--border-secondary);

  /* 圆角映射 */
  --ant-border-radius-base: var(--radius-md);
  --ant-border-radius-sm: var(--radius-sm);
  --ant-border-radius-lg: var(--radius-lg);

  /* 阴影映射 */
  --ant-shadow-1-up: var(--shadow-sm);
  --ant-shadow-1-down: var(--shadow-sm);
  --ant-shadow-2: var(--shadow-md);

  /* 字体大小映射 */
  --ant-font-size-base: var(--text-sm);
  --ant-font-size-lg: var(--text-base);
  --ant-font-size-sm: var(--text-xs);

  /* 行高映射 */
  --ant-line-height-base: var(--leading-normal);
}

/* ========================================
   Chrome Extension 专用组件样式
======================================== */

/* Layout 组件定制 */
.ant-layout {
  background: var(--bg-secondary);
}

.ant-layout-header {
  background: var(--bg-container);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  padding: 0;
  height: 64px;
  line-height: 64px;
}

/* 按钮组件增强 */
.ant-btn {
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.ant-btn-primary {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-dark) 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, var(--brand-primary-light) 0%, var(--brand-primary) 100%);
}

/* 卡片组件增强 */
.ant-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  background: var(--bg-container);
}

.ant-card-head {
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-tertiary);
}

/* Avatar 组件定制 */
.ant-avatar {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  border: 2px solid var(--bg-container);
  box-shadow: var(--shadow-sm);
}

/* Tag 组件定制 */
.ant-tag {
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
  border: none;
  box-shadow: var(--shadow-xs);
}

/* Space 组件优化 */
.ant-space {
  display: flex;
  align-items: center;
}

/* 表格组件增强 */
.ant-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.ant-table-thead > tr > th {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.ant-table-tbody > tr:hover > td {
  background: var(--bg-secondary);
}

/* 输入框组件增强 */
.ant-input,
.ant-select-selector {
  border-radius: var(--radius-md);
  border-color: var(--border-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

/* Dropdown 组件定制 */
.ant-dropdown {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

.ant-dropdown-menu {
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
}

.ant-dropdown-menu-item {
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-xs);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ant-dropdown-menu-item:hover {
  background: var(--bg-secondary);
  transform: translateX(2px);
}

/* ========================================
   Chrome Extension 专用工具类
======================================== */

/* 布局工具类 */
.extension-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: var(--bg-secondary);
}

.extension-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.extension-header {
  background: var(--bg-container);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
}

/* 品牌元素 */
.brand-gradient {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-shadow {
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.15);
}

/* 交互效果 */
.hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.hover-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* 状态指示器 */
.status-online {
  color: var(--color-success);
}

.status-offline {
  color: var(--text-tertiary);
}

.status-error {
  color: var(--color-error);
}

.status-warning {
  color: var(--color-warning);
}

/* 响应式工具类 - 针对Extension环境 */
.extension-responsive {
  min-width: 320px;
  max-width: 100%;
}

@media (max-width: 768px) {
  .extension-responsive {
    padding: var(--space-sm);
  }
}

/* 滚动条美化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 加载状态 */
.loading-shimmer {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 文本工具类 */
.text-gradient {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ========================================
   替代Tailwind的工具类系统
   基于Ant Design Vue设计规范
======================================== */

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* Grid 布局 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.gap-2 {
  gap: var(--space-sm);
}

.gap-4 {
  gap: var(--space-lg);
}

.gap-6 {
  gap: var(--space-xl);
}

/* 间距工具类 */
.space-x-1 > * + * {
  margin-left: var(--space-xs);
}

.space-x-2 > * + * {
  margin-left: var(--space-sm);
}

.space-x-3 > * + * {
  margin-left: var(--space-md);
}

.space-x-4 > * + * {
  margin-left: var(--space-lg);
}

.space-x-6 > * + * {
  margin-left: var(--space-xl);
}

.space-y-1 > * + * {
  margin-top: var(--space-xs);
}

.space-y-2 > * + * {
  margin-top: var(--space-sm);
}

.space-y-3 > * + * {
  margin-top: var(--space-md);
}

.space-y-4 > * + * {
  margin-top: var(--space-lg);
}

/* 内边距 */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-3 { padding: var(--space-md); }
.p-4 { padding: var(--space-lg); }
.p-6 { padding: var(--space-xl); }

.px-2 { padding-left: var(--space-sm); padding-right: var(--space-sm); }
.px-3 { padding-left: var(--space-md); padding-right: var(--space-md); }
.px-4 { padding-left: var(--space-lg); padding-right: var(--space-lg); }
.px-6 { padding-left: var(--space-xl); padding-right: var(--space-xl); }

.py-2 { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-3 { padding-top: var(--space-md); padding-bottom: var(--space-md); }
.py-4 { padding-top: var(--space-lg); padding-bottom: var(--space-lg); }

/* 外边距 */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-xs); }
.m-2 { margin: var(--space-sm); }
.m-4 { margin: var(--space-lg); }

.mb-1 { margin-bottom: var(--space-xs); }
.mb-2 { margin-bottom: var(--space-sm); }
.mb-3 { margin-bottom: var(--space-md); }
.mb-4 { margin-bottom: var(--space-lg); }
.mb-6 { margin-bottom: var(--space-xl); }

.mt-2 { margin-top: var(--space-sm); }
.mt-4 { margin-top: var(--space-lg); }

/* 宽度和高度 */
.w-auto { width: auto; }
.w-full { width: 100%; }
.h-8 { height: 32px; }
.h-12 { height: 48px; }
.h-auto { height: auto; }
.h-full { height: 100%; }

.min-h-screen { min-height: 100vh; }

/* 文本样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-xs { font-size: 12px; }
.text-sm { font-size: 14px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-2xl { font-size: 24px; }

/* 文本颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-white { color: #ffffff; }

.text-blue-600 { color: #1677ff; }
.text-red-600 { color: #ff4d4f; }
.text-green-600 { color: #52c41a; }
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }

/* 背景颜色 */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-neutral { background-color: var(--bg-secondary); }

/* 边框 */
.border { border: 1px solid var(--border-primary); }
.border-l-4 { border-left: 4px solid; }
.border-l-blue-500 { border-left-color: #1677ff; }
.border-l-red-500 { border-left-color: #ff4d4f; }
.border-l-green-500 { border-left-color: #52c41a; }
.border-l-gray-400 { border-left-color: var(--gray-400); }

.rounded { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

/* 阴影 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }

/* 其他工具类 */
.overflow-hidden { overflow: hidden; }
.flex-1 { flex: 1; }

/* 渐变背景 */
.bg-gradient-to-r {
  background: linear-gradient(to right, var(--from-color, #1677ff), var(--to-color, #722ed1));
}

.from-blue-400 { --from-color: #60a5fa; }
.to-purple-500 { --to-color: #a855f7; }
.from-yellow-400 { --from-color: #facc15; }
.to-orange-500 { --to-color: #f97316; }

/* 响应式网格 */
@media (max-width: 1200px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, 1fr);
  }

  .space-x-2 > * + * {
    margin-left: var(--space-xs);
  }

  .space-x-4 > * + * {
    margin-left: var(--space-sm);
  }
}
