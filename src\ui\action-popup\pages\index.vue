<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  AppstoreOutlined,
  SettingOutlined,
  ShopOutlined,
  UserOutlined,
  CheckCircleOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  RocketOutlined,
  <PERSON>boltOutlined,
  CrownOutlined,
  GiftOutlined,
  StarOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 状态数据
const stats = ref({
  shopStatus: '正常',
  credits: 30,
  todayOrders: 12,
  totalProducts: 156,
  notifications: 3
})

// 快速操作入口
const openSidePanel = async () => {
  try {
    // 获取当前窗口ID
    const currentWindow = await chrome.windows.getCurrent()
    await chrome.sidePanel.open({ windowId: currentWindow.id })
    window.close() // 关闭弹出窗口
  } catch (error) {
    console.error('打开侧边栏失败:', error)
    message.error('打开侧边栏失败')
  }
}

const openOptionsPage = () => {
  chrome.runtime.openOptionsPage()
  window.close()
}

// 快速功能 - 基于实际存在的功能
const quickActions = [
  {
    title: '工作台',
    icon: BarChartOutlined,
    colorClass: 'action-blue',
    route: '/side-panel/dashboard',
    description: '商品数据和核价管理'
  },
  {
    title: '上品中心',
    icon: DatabaseOutlined,
    colorClass: 'action-green',
    route: '/side-panel/product-center',
    description: '商品配置和店小秘集成'
  },
  {
    title: '自动核价',
    icon: ThunderboltOutlined,
    colorClass: 'action-orange',
    route: '/side-panel/auto-pricing',
    description: '批量价格审核处理'
  },
  {
    title: '子账号',
    icon: UserOutlined,
    colorClass: 'action-purple',
    route: '/side-panel/sub-account',
    description: '多账号管理'
  }
]

// 打开指定页面 - 通过storage通信
const openSidePanelPage = async (route: string) => {
  try {
    console.log('准备打开侧边栏页面:', route)

    // 将导航信息存储到chrome.storage，侧边栏会监听这个变化
    await chrome.storage.local.set({
      'side_panel_navigation': {
        route: route,
        timestamp: Date.now()
      }
    })

    // 然后打开侧边栏
    const currentWindow = await chrome.windows.getCurrent()
    await chrome.sidePanel.open({ windowId: currentWindow.id })

    console.log('导航信息已存储，侧边栏已打开:', route)
    window.close() // 关闭弹出窗口
  } catch (error) {
    console.error('打开页面失败:', error)
    message.error('打开页面失败')
  }
}

// 加载数据
const loadData = async () => {
  try {
    // 这里可以从chrome.storage或API加载实际数据
    const result = await chrome.storage.local.get(['hjdmj_stats'])
    if (result.hjdmj_stats) {
      Object.assign(stats.value, result.hjdmj_stats)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="popup-container">
    <!-- 头部信息 -->
    <div class="bg-gray-50 border-b p-4">
      <a-space class="w-full" align="center" :size="16">
        <a-space align="center" :size="12">
          <a-avatar :size="40" class="bg-blue-500">
            <template #icon>
              <img src="@assets/logo.png" alt="Logo" class="w-6 h-6" />
            </template>
          </a-avatar>
          <div>
            <h1 class="text-lg font-semibold text-gray-800">胡建大卖家</h1>
            <p class="text-sm text-gray-500">Temu店铺管理工具</p>
          </div>
        </a-space>
        <div class="flex-1"></div>
        <a-badge :count="stats.notifications" size="small">
          <BellOutlined class="text-lg text-gray-600 cursor-pointer hover:text-blue-500" />
        </a-badge>
      </a-space>

      <!-- 简化的统计信息 -->
      <a-row :gutter="12" class="mt-4">
        <a-col :span="8">
          <a-card class="text-center" size="small" :bordered="true">
            <div class="text-lg font-semibold text-gray-800">{{ stats.totalProducts }}</div>
            <div class="text-xs text-gray-500">商品总数</div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="text-center" size="small" :bordered="true">
            <div class="text-lg font-semibold text-gray-800">{{ stats.todayOrders }}</div>
            <div class="text-xs text-gray-500">今日订单</div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card class="text-center" size="small" :bordered="true">
            <div class="text-lg font-semibold text-gray-800">{{ stats.credits }}</div>
            <div class="text-xs text-gray-500">积分余额</div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要功能入口 -->
    <div class="p-4 space-y-4">
      <!-- 快速启动按钮 -->
      <a-button
        type="primary"
        size="large"
        block
        @click="openSidePanel"
        class="h-12 font-medium"
      >
        <template #icon>
          <AppstoreOutlined />
        </template>
        打开管理面板
      </a-button>

      <!-- 功能导航 -->
      <a-space direction="vertical" :size="8" class="w-full">
        <div class="text-sm font-medium text-gray-600">快速导航</div>
        <a-card
          v-for="action in quickActions"
          :key="action.title"
          @click="openSidePanelPage(action.route)"
          class="cursor-pointer hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
          size="small"
          :bordered="true"
        >
          <a-space align="center" :size="12" class="w-full">
            <a-avatar :size="32" :class="action.colorClass">
              <template #icon>
                <component :is="action.icon" class="text-base" />
              </template>
            </a-avatar>
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-800">{{ action.title }}</div>
              <div class="text-xs text-gray-500">{{ action.description }}</div>
            </div>
          </a-space>
        </a-card>
      </a-space>

      <!-- 状态信息 -->
      <div class="bg-gray-50 rounded-lg p-3 border">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <CheckCircleOutlined class="text-green-500" />
            <span class="text-sm text-gray-700">店铺状态</span>
          </div>
          <span class="text-sm font-medium text-green-600">{{ stats.shopStatus }}</span>
        </div>
      </div>

      <!-- 快速链接 -->
      <a-row :gutter="8">
        <a-col :span="12">
          <a-button
            @click="openOptionsPage"
            class="h-9 text-sm w-full"
            ghost
          >
            <template #icon>
              <SettingOutlined />
            </template>
            扩展设置
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            @click="openSidePanelPage('/side-panel/help')"
            class="h-9 text-sm w-full"
            ghost
          >
            <template #icon>
              <QuestionCircleOutlined />
            </template>
            帮助中心
          </a-button>
        </a-col>
      </a-row>

      <!-- 底部信息 -->
      <div class="text-center text-xs text-gray-400 pt-2 border-t">
        <p>胡建大卖家 v1.2.0</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Chrome Extension Popup 容器样式 */
.popup-container {
  width: 384px; /* w-96 = 384px */
  min-height: 500px;
  background-color: white;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  overflow: hidden;
}

/* 快速操作按钮颜色类 */
.action-blue {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.action-green {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.action-orange {
  background-color: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.action-purple {
  background-color: rgba(114, 46, 209, 0.1);
  color: #722ed1;
}

/* 确保所有Tailwind类都有fallback */
.bg-gray-50 {
  background-color: #f9fafb;
}

.border-b {
  border-bottom: 1px solid #e5e7eb;
}

.p-4 {
  padding: 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.w-10 {
  width: 2.5rem;
}

.h-10 {
  height: 2.5rem;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-800 {
  color: #1f2937;
}

.text-sm {
  font-size: 0.875rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.cursor-pointer {
  cursor: pointer;
}

.hover\:text-blue-500:hover {
  color: #3b82f6;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.hover\:bg-blue-50:hover {
  background-color: #eff6ff;
}

.hover\:border-blue-200:hover {
  border-color: #bfdbfe;
}

.transition-all {
  transition: all 0.15s ease-in-out;
}

.duration-200 {
  transition-duration: 200ms;
}

.border {
  border: 1px solid #e5e7eb;
}

.border-transparent {
  border-color: transparent;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.flex-1 {
  flex: 1;
}

.text-base {
  font-size: 1rem;
}

.font-medium {
  font-weight: 500;
}

.text-xs {
  font-size: 0.75rem;
}

.text-center {
  text-align: center;
}

.text-gray-400 {
  color: #9ca3af;
}

.pt-2 {
  padding-top: 0.5rem;
}

.border-t {
  border-top: 1px solid #e5e7eb;
}
</style>
