<script setup lang="ts">
import { h } from 'vue'
import { QuestionCircleOutlined, SettingOutlined } from '@ant-design/icons-vue'
import ThemeSwitch from '@/components/ThemeSwitch.vue'
</script>

<template>
  <a-layout-header class="flex justify-between p-2 bg-neutral">
    <RouterLink to="/">
      <a-space :size="8" align="center">
        <img
          src="@assets/logo.png"
          alt="logo"
          class="h-8 w-auto"
        />
        <div class="font-semibold text-primary">
          Vite Vue 3 Chrome Extension
        </div>
      </a-space>
    </RouterLink>
    <a-space :size="8">
      <RouterLink to="/common/about">
        <a-button type="text" :icon="h(QuestionCircleOutlined)" />
      </RouterLink>
      <RouterLink to="/options-page">
        <a-button type="text" :icon="h(SettingOutlined)" />
      </RouterLink>
      <ThemeSwitch />
    </a-space>
  </a-layout-header>
</template>

<style scoped></style>
