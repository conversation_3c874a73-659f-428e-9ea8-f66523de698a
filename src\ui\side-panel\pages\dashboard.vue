<script setup lang="ts">
import { ref, computed, onMounted, h, nextTick } from "vue"
import { ReloadOutlined } from '@ant-design/icons-vue'
import AutoPricingModal from "../../../components/AutoPricingModal.vue"
import PriceCheckModal from "../components/PriceCheckModal.vue"
import TemuNotFound from "../../../components/TemuNotFound.vue"
import AmazonPriceInfo from "../components/AmazonPriceInfo.vue"
import { useDashboard } from "../../../composables/useDashboard"
import { useShopBinding } from "../../../composables/useShopBinding"
import { useAppRouter, AppPageState } from "../../../composables/useAppRouter"
import { getSiteOptions } from '../../../config/temuSites'

// 使用应用路由管理 - 仅用于工作台页面的 Temu 绑定检查
const {
  currentPageState,
  isLoading: appLoading,
  temuLoginStatus,
  backendBindingStatus,
  checkAppState,
  handleBindingSuccess,
  handleLoginSuccess,
  getStateDescription
} = useAppRouter()

// 使用 Dashboard 数据管理
const {
  isLoading,
  todoData,
  products,
  tabs,
  activeTab,
  pageNum,
  pageSize,
  total,
  searchForm,
  currentShopInfo,
  mallId,
  refreshData,
  fetchProducts,
  searchProducts,
  switchTab,
  handlePageChange,
  jumpToSource,

  // 商品选择相关
  selectedProductIds,
  isAllSelected,
  isIndeterminate,
  handleSelectAll,
  handleSelectProduct,
  isProductSelected,
  getSelectedProducts,
  clearSelection,
  batchPriceConfirm,

  // Amazon价格方法
  fetchAmazonPrice,
  batchFetchAmazonPrices,
  forceRefreshProducts,

  // 工具方法
  parseMultipleInput,
  parseMultipleExtCode,
  getSiteId,
  buildQueryParams
} = useDashboard()

// 处理绑定成功事件
const onBindingSuccess = () => {
  handleBindingSuccess()
}

// 处理登录成功事件
const onLoginSuccess = () => {
  handleLoginSuccess()
}

// 组件挂载时检测应用状态
onMounted(() => {
  checkAppState()
})

// 使用店铺绑定状态
const { shopBinding } = useShopBinding()

// 数据源切换
const dataSource = ref(true) // true: 本地24小时内数据, false: 货盘

// 站点选项 - 使用统一的站点配置
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.code.toLowerCase(),
  label: `${site.label}站`
})))

// 商品ID类型选项
const idTypeOptions = [
  { value: "SKC", label: "SKC" },
  { value: "SPU", label: "SPU" },
  { value: "SKU", label: "SKU" },
]

// 当前店铺信息（从绑定状态获取）
const currentShop = computed(() => {
  if (currentShopInfo.value) {
    return {
      label: currentShopInfo.value.shopName || currentShopInfo.value.mallName || "Unknown Shop",
      avatar: currentShopInfo.value.logo || "",
      shopId: currentShopInfo.value.mallId?.toString() || currentShopInfo.value.shopId?.toString() || "",
      memberType: "会员",
      site: "美国站",
      url: `https://www.temu.com/mall.html?mall_id=${currentShopInfo.value.mallId || currentShopInfo.value.shopId}`,
    }
  }
  return {
    label: "未绑定店铺",
    avatar: "",
    shopId: "",
    memberType: "",
    site: "",
    url: "",
  }
})

// 功能函数
const openMerchantBackend = () => {
  if (currentShop.value?.url) {
    window.open(currentShop.value.url, "_blank")
  }
}

const autoSyncStock = () => {
  console.info("自动同步库存...")
}

const autoFollowPrice = () => {
  console.info("自动跟价管理...")
}

const autoActivityRegistration = () => {
  console.info("自动活动报名...")
}

// 显示自动核价弹窗
const showAutoPricingModal = ref(false)

const openAutoPricingModal = () => {
  console.info('[Dashboard] 打开自动核价弹窗')
  showAutoPricingModal.value = true
}

const closeAutoPricingModal = () => {
  console.info('[Dashboard] 关闭自动核价弹窗')
  showAutoPricingModal.value = false
}

// 测试查询参数构造
const testQueryParams = () => {
  console.info('[Dashboard] 测试查询参数构造')
  console.info('当前搜索表单:', searchForm.value)

  // 测试解析多选输入
  if (searchForm.value.productId) {
    const productIds = parseMultipleInput(searchForm.value.productId)
    console.info('解析商品ID:', productIds)
  }

  if (searchForm.value.extCode) {
    const extCodes = parseMultipleExtCode(searchForm.value.extCode)
    console.info('解析货号:', extCodes)
  }

  // 测试构造查询参数
  const params = buildQueryParams(activeTab.value, 1, 20, true)
  console.info('构造的查询参数:', params)

  // 显示JSON格式 - 使用安全的JSON序列化
  try {
    const jsonStr = JSON.stringify(params, (key, value) => {
      // 过滤掉可能包含控制字符的值
      if (typeof value === 'string') {
        return value.replace(/[\x00-\x1F\x7F-\x9F]/g, '')
      }
      return value
    }, 2)
    console.info('JSON格式:', jsonStr)
  } catch (error) {
    console.warn('JSON序列化失败:', error)
    console.info('参数对象:', params)
  }
}

// 强制刷新界面
const forceRefreshUI = () => {
  console.info('[Dashboard] 强制刷新界面')
  // 触发组件重新渲染 - 使用更可靠的方式
  const currentProducts = [...products.value]
  products.value = []
  nextTick(() => {
    products.value = currentProducts
    console.info('[Dashboard] 界面刷新完成，当前商品数量:', products.value.length)
  })
}

// 测试Amazon价格获取
const testAmazonPrices = async () => {
  console.info('[Dashboard] 测试Amazon价格获取')
  console.info('当前商品列表:', products.value)

  // 提取Amazon商品
  const amazonProducts = products.value.filter(product => {
    const extCode = product.sku?.itemNo || product.extCode || ''
    return extCode.includes('[am]')
  })

  console.info('找到Amazon商品:', amazonProducts.length, '个')
  amazonProducts.forEach(product => {
    console.info('Amazon商品详情:', {
      id: product.id,
      title: product.title,
      extCode: product.sku?.itemNo || product.extCode,
      currentPriceInfo: product.priceInfo,
      skuInfo: product.sku
    })
  })

  if (amazonProducts.length > 0) {
    console.info('开始强制获取Amazon价格...')

    // 获取价格前记录当前状态
    const beforeUpdate = products.value.map(p => ({
      id: p.id,
      priceInfo: p.priceInfo ? { ...p.priceInfo } : null
    }))

    await batchFetchAmazonPrices(true)

    // 强制刷新商品数据显示
    forceRefreshProducts()

    // 等待一下确保数据更新完成
    await nextTick()

    // 获取完成后检查更新结果
    console.info('价格获取完成，检查更新结果:')
    products.value.forEach(product => {
      const before = beforeUpdate.find(b => b.id === product.id)
      if (before && product.priceInfo) {
        // 安全地记录价格更新对比
        try {
          console.info('商品价格更新对比:', {
            id: product.id,
            extCode: (product.sku?.itemNo || product.extCode || '').replace(/[\x00-\x1F\x7F-\x9F]/g, ''),
            beforePrice: before.priceInfo?.usdPrice,
            afterPrice: product.priceInfo.usdPrice,
            beforeStock: before.priceInfo?.stock,
            afterStock: product.priceInfo.stock,
            updated: before.priceInfo?.usdPrice !== product.priceInfo.usdPrice
          })
        } catch (logError) {
          console.info('商品价格更新对比 (简化):', product.id)
        }
      }
    })

    console.info('[Dashboard] Amazon价格测试完成')
  } else {
    console.warn('没有找到Amazon商品')
  }
}

// 显示价格确认弹窗
const showPriceCheckModal = ref(false)
const selectedProduct = ref(null)

const openPriceCheckModal = (product: any) => {
  selectedProduct.value = product
  showPriceCheckModal.value = true
}

const closePriceCheckModal = () => {
  showPriceCheckModal.value = false
  selectedProduct.value = null
}

// 处理跳转货源（从价格确认弹窗触发）
const handleJumpToSource = (product: any) => {
  jumpToSource(product)
}

// 获取货源链接
const getSourceUrl = (extCode: string) => {
  if (!extCode) return ''

  const match = extCode.match(/^([A-Z0-9]{10})\[([a-z]+)\](\d+)$/)
  if (!match) return ''

  const [, asin, platform] = match

  switch (platform.toLowerCase()) {
    case 'am':
    case 'amazon':
      return `https://www.amazon.com/dp/${asin}`
    case 'eb':
    case 'ebay':
      return `https://www.ebay.com/itm/${asin}`
    default:
      return ''
  }
}

// 处理Amazon价格更新
const handleAmazonPriceUpdate = (product: any, amazonData: { usdPrice: string; stock: number; updateTime: string }) => {
  // 安全地记录Amazon价格更新
  try {
    console.info('[Dashboard] Amazon价格更新:', {
      productId: product.id,
      extCode: (product.sku?.itemNo || '').replace(/[\x00-\x1F\x7F-\x9F]/g, ''),
      amazonData: {
        usdPrice: amazonData.usdPrice,
        stock: amazonData.stock,
        updateTime: amazonData.updateTime
      }
    })
  } catch (logError) {
    console.info('[Dashboard] Amazon价格更新 (简化):', product.id)
  }

  // 更新商品的priceInfo
  if (product.priceInfo) {
    product.priceInfo.usdPrice = amazonData.usdPrice
    product.priceInfo.stock = amazonData.stock
    product.priceInfo.updateTime = amazonData.updateTime
  } else {
    product.priceInfo = {
      supplierPriceValue: 0,
      supplierPriceText: '',
      usdPrice: amazonData.usdPrice,
      exchangeRate: 7.2,
      reviewTimes: 0,
      priceReviewStatus: 0,
      officialDeclaredPrice: 0, // 默认官方申报价
      stock: amazonData.stock,
      updateTime: amazonData.updateTime
    }
  }

  // 计算并更新毛利相关字段
  const cost = parseFloat(amazonData.usdPrice) * (product.priceInfo.exchangeRate || 7.2)
  const officialPrice = product.priceInfo.officialDeclaredPrice || 84.07
  const profitAmount = officialPrice - cost
  const profitRate = officialPrice > 0 ? (profitAmount / officialPrice * 100) : 0

  // 添加计算字段到商品对象，用于过滤
  product.calculatedCost = cost
  product.calculatedProfitAmount = profitAmount
  product.calculatedProfitRate = profitRate

  console.info('[Dashboard] 计算毛利信息:', {
    productId: product.id,
    cost: cost.toFixed(2),
    officialPrice: officialPrice.toFixed(2),
    profitAmount: profitAmount.toFixed(2),
    profitRate: profitRate.toFixed(2) + '%'
  })

  // 强制触发响应式更新
  forceRefreshProducts()
}



// 基础表格列定义
const baseTableColumns = [
  {
    title: '📦 产品信息',
    key: 'product',
    width: 500,
    fixed: 'left'
  },
  {
    title: '💰 原申报信息',
    key: 'declared',
    width: 320
  },
  {
    title: '🎨 SKU属性',
    key: 'sku',
    width: 320
  },
  {
    title: '📅 时间信息',
    key: 'time',
    width: 180,
    align: 'center'
  }
]

// 价格待确认标签页的表格列定义
const priceConfirmingColumns = [
  {
    title: '产品信息',
    key: 'product',
    width: 500,
    fixed: 'left'
  },
  {
    title: '价格信息',
    key: 'price-info',
    width: 300
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center'
  }
]

// 动态表格列定义
const tableColumns = computed(() => {
  return activeTab.value === 'price-confirming' ? priceConfirmingColumns : baseTableColumns
})

// 定价操作状态管理
const pricingOperations = ref(new Map())

// 获取商品的定价操作状态
const getPricingOperation = (productId: number) => {
  if (!pricingOperations.value.has(productId)) {
    pricingOperations.value.set(productId, {
      supplierResult: 1, // 默认选择同意
      customPrice: undefined,
      submitting: false
    })
  }
  return pricingOperations.value.get(productId)
}

// 操作类型变化处理
const onOperationChange = (productId: number) => {
  const operation = getPricingOperation(productId)
  if (operation.supplierResult !== 2) {
    operation.customPrice = undefined
  }
}

// 检查是否可以提交
const canSubmitPricing = (productId: number) => {
  const operation = getPricingOperation(productId)
  if (operation.submitting) return false
  if (operation.supplierResult === 2) {
    return operation.customPrice && operation.customPrice > 0
  }
  return true
}

// 格式化价格显示
const formatPrice = (price: number) => {
  if (!price) return '0.00'
  return (price / 100).toFixed(2)
}

// 提交定价操作
const submitPricingOperation = async (record: any) => {
  const operation = getPricingOperation(record.id)
  if (!canSubmitPricing(record.id)) return

  operation.submitting = true

  try {
    // 导入priceReviewService
    const { priceReviewService } = await import('../../../services/priceReviewService')

    // 获取必要的参数
    // priceOrderId通常在sku.priceReviewInfo.priceOrderId或sku.priceOrderId中
    const priceOrderId = record.sku?.priceReviewInfo?.priceOrderId || record.sku?.priceOrderId || record.priceInfo?.priceOrderId

    // productSkuId可能在多个位置，尝试不同的字段
    const productSkuId = record.sku?.priceReviewInfo?.productSkuId ||
                        record.sku?.skuId ||
                        record.sku?.productSkuId ||
                        record.id

    console.info('[Dashboard] 定价操作 - 商品数据分析:', {
      recordId: record.id,
      recordTitle: record.title,
      priceOrderId,
      productSkuId,
      skuData: record.sku,
      priceReviewInfo: record.sku?.priceReviewInfo,
      priceInfo: record.priceInfo
    })

    if (!priceOrderId) {
      console.error('[Dashboard] 缺少priceOrderId，商品数据:', record)
      throw new Error(`缺少priceOrderId信息，商品: ${record.title}`)
    }

    if (!productSkuId) {
      console.error('[Dashboard] 缺少productSkuId，商品数据:', record)
      throw new Error(`缺少productSkuId信息，商品: ${record.title}`)
    }

    let actionText = ''

    switch (operation.supplierResult) {
      case 1: { // 同意
        // 获取官方申报价，检查多个可能的位置
        const officialDeclaredPrice = record.priceInfo?.officialDeclaredPrice ||
                                     record.sku?.officialDeclaredPrice ||
                                     record.priceInfo?.supplierPriceValue ||
                                     record.sku?.siteSupplierPrice?.supplierPriceValue

        console.info('[Dashboard] 查找官方申报价:', {
          'priceInfo.officialDeclaredPrice': record.priceInfo?.officialDeclaredPrice,
          'sku.officialDeclaredPrice': record.sku?.officialDeclaredPrice,
          'priceInfo.supplierPriceValue': record.priceInfo?.supplierPriceValue,
          'sku.siteSupplierPrice.supplierPriceValue': record.sku?.siteSupplierPrice?.supplierPriceValue,
          'finalPrice': officialDeclaredPrice
        })

        if (!officialDeclaredPrice || officialDeclaredPrice <= 0) {
          throw new Error(`缺少官方申报价信息，无法同意申报价。商品: ${record.title}`)
        }

        const acceptPrice = Math.round(officialDeclaredPrice)
        await priceReviewService.singleAcceptPrice(priceOrderId, productSkuId, acceptPrice)
        actionText = '同意申报价'
        break
      }
      case 2: { // 重报
        if (!operation.customPrice) {
          throw new Error('请输入重报价格')
        }
        const repriceCents = Math.round(operation.customPrice * 100)
        await priceReviewService.singleRepricing(priceOrderId, productSkuId, repriceCents)
        actionText = '重新报价'
        break
      }
      case 3: { // 放弃
        await priceReviewService.singleRejectPricing(priceOrderId, productSkuId)
        actionText = '放弃上新'
        break
      }
    }

    // 操作成功
    const { message } = await import('ant-design-vue')
    message.success(`${actionText}成功`)

    // 重置操作状态
    pricingOperations.value.delete(record.id)

    // 刷新数据，移除已处理的商品
    await refreshData()

  } catch (error) {
    console.error('定价操作失败:', error)
    const { message } = await import('ant-design-vue')

    // 提取具体的错误信息
    const errorMessage = (error as Error).message

    // 如果是锁定失败，给出更友好的提示
    if (errorMessage.includes('lock') && errorMessage.includes('acquire failed')) {
      message.error('操作失败：该商品正在被其他操作处理中，请稍后重试')
    } else {
      message.error('操作失败: ' + errorMessage)
    }
  } finally {
    operation.submitting = false
  }
}
</script>

<template>
  <div class="extension-container custom-scrollbar">
    <!-- 加载状态 -->
    <div v-if="currentPageState === AppPageState.LOADING" class="h-full flex items-center justify-center">
      <div class="text-center">
        <a-spin size="large" />
        <div class="mt-4">
          <h3 class="text-lg font-medium text-primary mb-2">{{ getStateDescription() }}</h3>
          <p class="text-secondary">正在检测 Temu 登录状态和后台绑定状态...</p>
        </div>
      </div>
    </div>

    <!-- Temu 未登录状态 -->
    <div v-else-if="currentPageState === AppPageState.TEMU_NOT_FOUND" class="h-full">
      <TemuNotFound
        @login-success="onLoginSuccess"
      />
    </div>

    <!-- 需要绑定状态 -->
    <div v-else-if="currentPageState === AppPageState.BINDING_REQUIRED" class="h-full">
      <TemuNotFound
        :show-binding-mode="true"
        :temu-user-info="temuLoginStatus.userInfo"
        :temu-shop-info="temuLoginStatus.shopInfo"
        @binding-success="onBindingSuccess"
      />
    </div>

    <!-- 工作台主内容 - 只有在 DASHBOARD 状态下才显示 -->
    <div v-else-if="currentPageState === AppPageState.DASHBOARD" class="extension-content">
      <div class="dashboard-container">
        <!-- 欢迎横幅 -->
        <a-card class="welcome-banner brand-shadow hover-lift">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1 class="welcome-title">
                <span class="welcome-emoji">🎉</span>
                <span class="text-gradient">欢迎使用胡建大卖家</span>
              </h1>
              <p class="welcome-subtitle">您的专业Temu店铺管理助手，让销售更简单高效</p>
            </div>
            <div class="welcome-stats">
              <a-statistic
                :value="products.length"
                title="商品总数"
                :value-style="{ color: 'var(--brand-primary)', fontSize: '2rem', fontWeight: 'bold' }"
              />
            </div>
          </div>
        </a-card>

        <!-- 店铺信息卡片 -->
        <a-card class="shop-info-card hover-lift">
          <div class="shop-info-content">
            <!-- 左侧：店铺信息 -->
            <div class="shop-info-left">
              <div class="shop-brand">
                <a-avatar :size="48" class="shop-avatar">
                  <template #icon>
                    <span class="shop-icon">T</span>
                  </template>
                </a-avatar>
                <div class="shop-details">
                  <h2 class="shop-title">Temu 店铺</h2>
                  <p class="shop-subtitle">跨境电商平台</p>
                </div>
              </div>

              <!-- 店铺选择器 -->
              <div class="shop-selector">
                <span class="selector-label">当前店铺：</span>
                <a-select
                  v-model:value="mallId"
                  style="min-width: 200px"
                  placeholder="选择店铺"
                  class="shop-select"
                >
                  <a-select-option :value="currentShop.shopId">
                    <div class="flex items-center space-x-2">
                      <a-avatar
                        v-if="currentShop.avatar"
                        :src="currentShop.avatar"
                        :size="24"
                      />
                      <a-avatar
                        v-else
                        :size="24"
                        class="bg-gradient-to-r from-blue-400 to-purple-500"
                      >
                        <span class="text-xs text-white">店</span>
                      </a-avatar>
                      <span class="font-medium">{{ currentShop.label }}</span>
                    </div>
                  </a-select-option>
                </a-select>
              </div>

              <!-- 访问店铺链接 -->
              <a-button
                v-if="currentShop.url"
                type="primary"
                ghost
                size="small"
                @click="window.open(currentShop.url, '_blank')"
              >
                🔗 访问店铺
              </a-button>
            </div>

            <!-- 右侧：会员状态和操作 -->
            <div class="flex items-center space-x-6">
              <!-- 会员状态卡片 -->
              <div class="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-4 text-white">
                <div class="flex items-center space-x-2">
                  <span class="text-lg">👑</span>
                  <div>
                    <div class="font-bold">星耀会员</div>
                    <div class="text-sm opacity-90">剩余 3 天</div>
                  </div>
                </div>
              </div>

              <!-- 数据源切换 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <div class="text-sm text-gray-600 mb-2">数据来源</div>
                <a-switch
                  v-model:checked="dataSource"
                  checked-children="本地数据"
                  un-checked-children="货盘数据"
                  class="bg-blue-500"
                />
              </div>

              <!-- 刷新按钮 -->
              <a-button
                type="primary"
                :icon="h(ReloadOutlined)"
                :loading="isLoading"
                @click="refreshData"
                class="h-12 px-6"
              >
                刷新数据
              </a-button>
            </div>
          </div>
        </a-card>

        <!-- 商品管理区域 -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
          <!-- 快捷操作工具栏 -->
          <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-lg font-semibold text-gray-800">📊 商品管理</span>
                <a-tag color="blue">{{ total }} 个商品</a-tag>
              </div>
              <div class="flex items-center space-x-3">
                <a-button
                  type="primary"
                  ghost
                  size="small"
                  @click="autoSyncStock"
                  class="flex items-center space-x-1"
                >
                  <span>🔄</span>
                  <span>同步库存</span>
                </a-button>
                <a-button
                  type="primary"
                  ghost
                  size="small"
                  @click="openAutoPricingModal"
                  class="flex items-center space-x-1"
                >
                  <span>🤖</span>
                  <span>自动核价</span>
                </a-button>
                <a-button
                  type="primary"
                  ghost
                  size="small"
                  @click="autoFollowPrice"
                  class="flex items-center space-x-1"
                >
                  <span>📈</span>
                  <span>跟价管理</span>
                </a-button>
                <a-button
                  type="primary"
                  ghost
                  size="small"
                  @click="autoActivityRegistration"
                  class="flex items-center space-x-1"
                >
                  <span>🎯</span>
                  <span>活动报名</span>
                </a-button>
              </div>
            </div>
          </div>

          <!-- 标签页 -->
          <a-tabs
            v-model:active-key="activeTab"
            class="dashboard-tabs px-6"
            @change="switchTab"
          >

            <a-tab-pane
              v-for="tab in tabs"
              :key="tab.key"
              :tab="`${tab.label} ${tab.count > 0 ? tab.count : ''}`"
            >
              <!-- 搜索表单 -->
              <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="flex items-center mb-3">
                  <span class="text-sm font-medium text-gray-700">🔍 筛选条件</span>
                </div>
                <a-form
                  :model="searchForm"
                  layout="inline"
                  @finish="searchProducts"
                  class="search-form"
                >
                  <a-form-item label="站点">
                    <a-select
                      v-model:value="searchForm.site"
                      placeholder="选择站点"
                      style="width: 120px"
                      allow-clear
                    >
                      <a-select-option
                        v-for="site in siteOptions"
                        :key="site.value"
                        :value="site.value"
                      >
                        {{ site.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>

                  <a-form-item label="商品ID">
                    <a-input-group compact>
                      <a-select
                        v-model:value="searchForm.productIdType"
                        style="width: 80px"
                      >
                        <a-select-option
                          v-for="type in idTypeOptions"
                          :key="type.value"
                          :value="type.value"
                        >
                          {{ type.label }}
                        </a-select-option>
                      </a-select>
                      <a-input
                        v-model:value="searchForm.productId"
                        placeholder="多个ID用空格分隔"
                        style="width: 220px"
                        allow-clear
                      />
                    </a-input-group>
                  </a-form-item>

                  <a-form-item label="货号">
                    <a-input-group compact>
                      <a-select
                        v-model:value="searchForm.extCodeType"
                        style="width: 80px"
                      >
                        <a-select-option value="SKC">SKC</a-select-option>
                        <a-select-option value="SKU">SKU</a-select-option>
                      </a-select>
                      <a-input
                        v-model:value="searchForm.extCode"
                        placeholder="多个货号用空格分隔"
                        style="width: 220px"
                        allow-clear
                      />
                    </a-input-group>
                  </a-form-item>

                  <a-form-item label="库存">
                    <a-input-group compact style="width: 120px">
                      <a-input
                        addon-before="≤"
                        v-model:value="searchForm.stockFilter"
                        placeholder="库存数量"
                        type="number"
                        :min="0"
                        style="width: 100%"
                      />
                    </a-input-group>
                  </a-form-item>

                  <a-form-item label="商品分类">
                    <a-select
                      v-model:value="searchForm.category"
                      placeholder="选择分类"
                      style="width: 160px"
                      allow-clear
                      show-search
                      :filter-option="(input, option) =>
                        option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      "
                    >
                      <a-select-option value="">全部分类</a-select-option>
                      <a-select-option value="服装">服装</a-select-option>
                      <a-select-option value="家居">家居</a-select-option>
                      <a-select-option value="电子">电子</a-select-option>
                      <a-select-option value="美妆">美妆</a-select-option>
                      <a-select-option value="运动">运动</a-select-option>
                      <a-select-option value="其他">其他</a-select-option>
                    </a-select>
                  </a-form-item>

                  <a-form-item label="毛利率">
                    <a-input-group compact style="width: 160px">
                      <a-select
                        v-model:value="searchForm.profitRateOperator"
                        style="width: 70px"
                      >
                        <a-select-option value="gte">≥%</a-select-option>
                        <a-select-option value="lte">≤%</a-select-option>
                      </a-select>
                      <a-input
                        v-model:value="searchForm.profitRateFilter"
                        placeholder="毛利率"
                        type="number"
                        :min="0"
                        :max="100"
                        style="width: calc(100% - 70px)"
                      />
                    </a-input-group>
                  </a-form-item>

                  <a-form-item>
                    <a-space>
                      <a-button
                        type="primary"
                        html-type="submit"
                        class="search-btn"
                      >
                        <span>🔍</span>
                        <span>查询</span>
                      </a-button>
                      <a-button
                        type="default"
                        @click="testQueryParams"
                        class="test-btn"
                      >
                        <span>🧪</span>
                        <span>测试</span>
                      </a-button>
                      <a-button
                        type="default"
                        @click="testAmazonPrices"
                        :loading="isLoading"
                        class="amazon-btn"
                      >
                        <span>💰</span>
                        <span>测试Amazon价格</span>
                      </a-button>
                    </a-space>
                  </a-form-item>
                </a-form>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>

        <!-- 商品列表 -->
        <div class="mt-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">📦 商品列表</h3>
            <div class="text-sm text-gray-500">
              共 {{ total }} 件商品
            </div>
          </div>



          <a-spin
            :spinning="isLoading"
            tip="正在加载商品数据..."
          >
            <!-- 商品表格 -->
            <a-table
              v-if="products.length > 0"
              :columns="tableColumns"
              :data-source="products"
              :pagination="{
                current: pageNum,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total: number, range: number[]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: handlePageChange,
                onShowSizeChange: (current: number, size: number) => {
                  // 更新页面大小并重新加载数据
                  const params = buildQueryParams(activeTab, current, size)
                  fetchProducts(params)
                },
                size: 'small'
              }"
              :scroll="{ x: 1300 }"
              row-key="id"
              size="small"
              bordered
              class="custom-table"
            >
              <!-- 表格列模板 -->
              <template #bodyCell="{ column, record }">
                <!-- 产品信息列 - 通用 -->
                <template v-if="column.key === 'product'">
                  <div class="product-info-cell">
                    <div class="product-image">
                      <img
                        :src="record.image"
                        :alt="record.title"
                        width="60"
                        height="60"
                      />
                    </div>
                    <div class="product-details">
                      <div class="product-title" :title="record.title">
                        {{ record.title }}
                      </div>
                      <div class="product-meta">
                        <span>SPU: {{ record.spu }}</span>
                        <span>SKC: {{ record.skc }}</span>
                        <span>站点: {{ record.site }}</span>
                      </div>
                      <div class="product-category">
                        {{ record.category }}
                      </div>
                    </div>
                  </div>
                </template>

                <!-- 价格信息列 - 仅在价格待确认标签页显示 -->
                <template v-else-if="column.key === 'price-info'">
                  <div class="price-info-cell">
                    <!-- 多SKU商品显示 -->
                    <div v-if="record.isMultiSku" class="multi-sku-info">
                      <div class="multi-sku-header">
                        <span class="multi-sku-badge">多SKU ({{ record.totalSkuCount }}个)</span>
                        <span class="price-range" v-if="record.priceRange">
                          {{ record.priceRange.min }} - {{ record.priceRange.max }}
                        </span>
                      </div>

                      <!-- 显示所有SKU -->
                      <div class="sku-list">
                        <div
                          v-for="(skuInfo, index) in record.skuList"
                          :key="skuInfo.skuId"
                          class="sku-item"
                        >
                          <img
                            :src="skuInfo.image"
                            width="20"
                            height="20"
                            class="sku-image-small"
                          />
                          <div class="sku-item-details">
                            <div class="sku-properties">{{ skuInfo.properties }}</div>
                            <div class="sku-code-small">{{ skuInfo.extCode }}</div>
                          </div>
                          <div class="sku-price">{{ skuInfo.price }}</div>
                        </div>
                      </div>

                      <!-- Amazon价格信息 - 显示所有Amazon SKU的价格 -->
                      <div v-for="(skuInfo, index) in record.skuList.filter(sku => sku.extCode && sku.extCode.includes('[am]'))"
                           :key="skuInfo.skuId"
                           class="amazon-sku-price">
                        <div v-if="index > 0" class="amazon-sku-separator">---</div>
                        <AmazonPriceInfo
                          :ext-code="skuInfo.extCode"
                          :exchange-rate="record.priceInfo?.exchangeRate || 7.2"
                          :declared-price="record.declaredPrice"
                          :official-declared-price="record.sku?.officialDeclaredPrice || record.priceInfo?.officialDeclaredPrice || 84.07"
                          :price-order-id="record.sku?.priceOrderId"
                          @price-updated="(data) => handleAmazonPriceUpdate(record, data)"
                        />
                      </div>
                    </div>

                    <!-- 单SKU商品显示 -->
                    <div v-else class="single-sku-info">
                      <div class="sku-info">
                        <img
                          :src="record.sku.image"
                          width="24"
                          height="24"
                          class="sku-image"
                        />
                        <div class="sku-details">
                          <div class="sku-color">颜色: {{ record.sku.color }}</div>
                          <div class="sku-code">货号: {{ record.sku.itemNo }}</div>
                        </div>
                      </div>

                      <!-- Amazon实时价格信息组件 -->
                      <AmazonPriceInfo
                        v-if="record.sku?.itemNo && record.sku.itemNo.includes('[am]')"
                        :ext-code="record.sku.itemNo"
                        :exchange-rate="record.priceInfo?.exchangeRate || 7.2"
                        :declared-price="record.declaredPrice"
                        :official-declared-price="record.sku?.officialDeclaredPrice || record.priceInfo?.officialDeclaredPrice || 84.07"
                        :price-order-id="record.sku?.priceOrderId"
                        @price-updated="(data) => handleAmazonPriceUpdate(record, data)"
                      />
                    </div>
                  </div>
                </template>

                <!-- 申报信息列 - 其他标签页显示 -->
                <template v-else-if="column.key === 'declared'">
                  <div class="p-3 bg-gray-50 rounded-lg">
                    <div class="space-y-3">
                      <!-- 申报价格 -->
                      <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-500">💰 申报价格:</span>
                        <a-tag
                          color="red"
                          size="default"
                          class="font-semibold"
                        >
                          {{ record.declaredPrice }}
                        </a-tag>
                      </div>
                      <!-- 创建时间 -->
                      <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-500">📅 创建时间:</span>
                        <span class="text-sm text-gray-700 font-medium">
                          {{ record.createTime }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>

                <template v-else-if="column.key === 'sku'">
                  <div class="flex items-start space-x-2 p-2">
                    <div>
                      <a-image
                        :src="record.sku.image"
                        :alt="record.sku.color"
                        :width="40"
                        :height="40"
                        :preview="true"
                        class="rounded border"
                      />
                    </div>
                    <div class="flex-1">
                      <div class="flex items-center space-x-1 mb-1">
                        <span class="text-xs text-gray-500">🎨</span>
                        <a-tag color="purple" size="small">{{ record.sku.color }}</a-tag>
                         <a-button
                            type="link"
                            size="small"
                            class="p-0 h-auto text-xs text-blue-600"
                            @click="jumpToSource(record)"
                          >
                            跳转货源
                          </a-button>
                      </div>

                      <div class="space-y-1">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center space-x-1">
                            <span class="text-xs text-gray-400">货号:</span>
                            <span class="text-xs text-gray-700">{{ record.sku.itemNo }}</span>
                          </div>
                         
                        </div>

                        <div class="flex items-center space-x-1">
                          <span class="text-xs text-gray-400">状态:</span>
                          <a-tag
                            :color="record.sku.status === '价格申报中' ? 'processing' : 'default'"
                            size="small"
                          >
                            {{ record.sku.status }}
                          </a-tag>
                        </div>

                        <div class="flex items-center space-x-1">
                          <span class="text-xs text-gray-400">价格:</span>
                          <span class="text-sm font-bold text-red-600">{{ record.sku.price }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>

                <template v-else-if="column.key === 'time'">
                  <div class="flex items-center space-x-2 p-2">
                    <span class="text-xs text-gray-500">📅 创建时间:</span>
                    <span class="text-sm text-gray-700 font-medium">
                      {{ record.createTime }}
                    </span>
                  </div>
                </template>

                <!-- 操作列 - 仅在价格待确认标签页显示 -->
                <template v-else-if="column.key === 'actions'">
                  <div class="pricing-action-container" style="padding: 8px; min-width: 200px;">
                    <!-- 改价次数和申报状态 -->
                    <div class="info-section" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span style="font-size: 12px; color: #666;">改价次数:</span>
                        <span style="font-size: 12px; font-weight: 500;">{{ record.priceInfo?.reviewTimes || 1 }}</span>
                      </div>
                      <div style="display: flex; justify-content: space-between;">
                        <span style="font-size: 12px; color: #666;">申报状态:</span>
                        <span style="font-size: 12px; color: #faad14;">价格申报中</span>
                      </div>
                    </div>

                    <!-- 操作选择 -->
                    <div class="operation-section" style="margin-bottom: 12px;">
                      <a-radio-group
                        v-model:value="getPricingOperation(record.id).supplierResult"
                        size="small"
                        @change="onOperationChange(record.id)"
                      >
                        <a-radio :value="1" style="display: block; margin-bottom: 4px;">
                          <span style="color: #52c41a; font-size: 12px;">同意</span>
                        </a-radio>
                        <a-radio :value="2" style="display: block; margin-bottom: 4px;">
                          <span style="color: #faad14; font-size: 12px;">重报</span>
                        </a-radio>
                        <a-radio :value="3" style="display: block;">
                          <span style="color: #ff4d4f; font-size: 12px;">放弃</span>
                        </a-radio>
                      </a-radio-group>
                    </div>

                    <!-- 重报价格输入 -->
                    <div
                      v-if="getPricingOperation(record.id).supplierResult === 2"
                      class="price-input-section"
                      style="margin-bottom: 12px;"
                    >
                      <a-input-number
                        v-model:value="getPricingOperation(record.id).customPrice"
                        :min="0.01"
                        :step="0.01"
                        :precision="2"
                        placeholder="输入重报价格"
                        size="small"
                        style="width: 100%;"
                        addon-before="¥"
                      />
                      <div style="font-size: 10px; color: #999; margin-top: 2px;">
                        建议价: ¥{{ formatPrice(record.priceInfo?.officialDeclaredPrice || 0) }}
                      </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="submit-section">
                      <a-button
                        type="primary"
                        size="small"
                        style="width: 100%;"
                        :loading="getPricingOperation(record.id).submitting"
                        :disabled="!canSubmitPricing(record.id)"
                        @click="submitPricingOperation(record)"
                      >
                        确认提交
                      </a-button>
                    </div>
                  </div>
                </template>
              </template>
            </a-table>

            <!-- 空数据状态 -->
            <a-empty
              v-else
              description="暂无商品数据"
            >
              <a-button
                type="primary"
                @click="refreshData"
              >
                刷新数据
              </a-button>
            </a-empty>
          </a-spin>
        </div>
      </div>

      <!-- 数据统计卡片 -->
      <a-row :gutter="16" class="mt-4">
        <!-- 商品总数 -->
        <a-col :span="6">
          <a-card
            class="text-center shadow-sm border-l-4 border-l-blue-500"
            :bordered="false"
          >
            <div class="text-2xl font-bold text-blue-600 mb-1">{{ total }}</div>
            <div class="text-sm text-gray-600">商品总数</div>
          </a-card>
        </a-col>

        <!-- 待办事项 -->
        <a-col :span="6">
          <a-card
            class="text-center shadow-sm border-l-4 border-l-red-500"
            :bordered="false"
          >
            <div class="text-2xl font-bold text-red-600 mb-1">{{ todoData.total || 0 }}</div>
            <div class="text-sm text-gray-600">待办事项</div>
          </a-card>
        </a-col>

        <!-- 当前标签页 -->
        <a-col :span="6">
          <a-card
            class="text-center shadow-sm border-l-4 border-l-green-500"
            :bordered="false"
          >
            <div class="text-lg font-semibold text-green-600 mb-1">
              {{ tabs.find(tab => tab.key === activeTab)?.label || '全部' }}
            </div>
            <div class="text-sm text-gray-600">当前分类</div>
          </a-card>
        </a-col>

        <!-- 连接状态 -->
        <a-col :span="6">
          <a-card
            class="text-center shadow-sm border-l-4"
            :class="currentShop.shopId ? 'border-l-green-500' : 'border-l-gray-400'"
            :bordered="false"
          >
            <div class="text-lg font-semibold mb-1"
                 :class="currentShop.shopId ? 'text-green-600' : 'text-gray-500'">
              {{ currentShop.shopId ? '已连接' : '未连接' }}
            </div>
            <div class="text-sm text-gray-600">店铺状态</div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>

  <!-- 自动核价弹窗 -->
  <AutoPricingModal
    v-if="showAutoPricingModal"
    @close="closeAutoPricingModal"
  />

  <!-- 价格确认弹窗 -->
  <PriceCheckModal
    v-model:open="showPriceCheckModal"
    :product-data="selectedProduct"
    @jump-to-source="handleJumpToSource"
  />
</template>

<style scoped>
/* ========================================
   Dashboard 页面专用样式 - 基于新设计系统
======================================== */

/* 主容器 */
.dashboard-container {
  padding: var(--space-lg);
  max-width: 1200px;
  margin: 0 auto;
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  border: none;
  margin-bottom: var(--space-xl);
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin: 0 0 var(--space-sm) 0;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.welcome-emoji {
  font-size: var(--text-3xl);
}

.welcome-subtitle {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-size: var(--text-base);
}

.welcome-stats {
  text-align: right;
}

/* 店铺信息卡片 */
.shop-info-card {
  margin-bottom: var(--space-xl);
}

.shop-info-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shop-info-left {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

.shop-brand {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.shop-avatar {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.shop-icon {
  font-weight: var(--font-bold);
  color: white;
  font-size: var(--text-lg);
}

.shop-details {
  display: flex;
  flex-direction: column;
}

.shop-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
}

.shop-subtitle {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin: 0;
}

.shop-selector {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.selector-label {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
}

.shop-select {
  border-radius: var(--radius-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--space-md);
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-lg);
  }

  .shop-info-content {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .shop-info-left {
    flex-direction: column;
    gap: var(--space-md);
  }
}

/* 全局字体大小调整 */
:deep(.ant-typography) {
  font-size: 14px !important;
}

:deep(.ant-btn) {
  font-size: 14px !important;
  height: auto !important;
  padding: 6px 15px !important;
}

:deep(.ant-form-item-label > label) {
  font-size: 14px !important;
}

:deep(.ant-input) {
  font-size: 14px !important;
}

:deep(.ant-select) {
  font-size: 14px !important;
}

/* 自定义表格样式 - 简化版 */
.custom-table :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  border: 1px solid #e8e8e8;
  font-weight: 500;
  color: #333;
  font-size: 13px !important;
  padding: 8px 12px !important;
  text-align: left;
}

.custom-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f9f9f9;
}

.custom-table :deep(.ant-table-tbody > tr > td) {
  border: 1px solid #e8e8e8;
  padding: 8px !important;
  font-size: 12px !important;
  vertical-align: top;
}

.custom-table :deep(.ant-table-container) {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.custom-table :deep(.ant-table) {
  font-size: 12px !important;
}

/* 产品信息单元格样式 */
.product-info-cell {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px;
}

.product-image img {
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
}

.product-meta span {
  font-size: 11px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 2px;
}

.product-category {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  margin-top: var(--space-xs);
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

/* 价格信息单元格样式 */
.price-info-cell {
  padding: 8px;
}

.sku-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.sku-image {
  border-radius: 2px;
  border: 1px solid #e8e8e8;
}

.sku-details {
  flex: 1;
}

.sku-color,
.sku-code {
  font-size: 11px;
  color: #666;
  line-height: 1.3;
}

/* 多SKU商品样式 */
.multi-sku-info {
  width: 100%;
}

.multi-sku-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.multi-sku-badge {
  background: #e6f7ff;
  color: #1677ff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  border: 1px solid #91d5ff;
}

.price-range {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.sku-list {
  margin-bottom: 8px;
}

.sku-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 0;
  border-bottom: 1px solid #f5f5f5;
}

.sku-item:last-child {
  border-bottom: none;
}

.sku-image-small {
  border-radius: 2px;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.sku-item-details {
  flex: 1;
  min-width: 0;
}

.sku-properties {
  font-size: 10px;
  color: #333;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sku-code-small {
  font-size: 9px;
  color: #999;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sku-price {
  font-size: 10px;
  color: #ff4d4f;
  font-weight: 500;
  flex-shrink: 0;
}

.single-sku-info {
  width: 100%;
}

/* Amazon SKU价格样式 */
.amazon-sku-price {
  margin-bottom: 6px;
}

.amazon-sku-price:last-child {
  margin-bottom: 0;
}

.amazon-sku-separator {
  text-align: center;
  color: #ccc;
  font-size: 10px;
  margin: 4px 0;
  border-top: 1px solid #f0f0f0;
  padding-top: 4px;
}

/* 产品卡片样式 */
.product-card {
  transition: all 0.2s ease;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 标签样式增强 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
}

/* 图片样式 */
:deep(.ant-image) {
  transition: all 0.2s ease;
}

:deep(.ant-image:hover) {
  transform: scale(1.05);
}

/* 卡片阴影效果 */
.shadow-sm {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 响应式网格 */
@media (max-width: 1200px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* 按钮悬停效果 */
:deep(.ant-btn:hover) {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 标签页按钮样式 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  font-size: 14px !important;
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 统计卡片字体 */
.text-2xl {
  font-size: 1.75rem !important;
}

.text-lg {
  font-size: 1.125rem !important;
}

.text-sm {
  font-size: 0.875rem !important;
}

/* 标题字体 */
h2, h3, h4 {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
}

/* 产品标题样式优化 */
.product-title {
  word-break: break-word;
  word-wrap: break-word;
  line-height: 1.4 !important;
  max-height: none !important;
  overflow: visible !important;
  white-space: normal !important;
  display: block !important;
}

/* Dashboard 标签页样式 */
.dashboard-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 0;
}

.dashboard-tabs :deep(.ant-tabs-tab) {
  font-weight: 500;
}

.dashboard-tabs :deep(.ant-tabs-tab-active) {
  font-weight: 600;
}

/* 顶部区域样式 */
.dashboard-header {
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .dashboard-container {
    min-width: auto;
  }
}

/* 参考页面样式 */
.display-align-items {
  display: flex;
  align-items: flex-start;
}

.m-r-20 {
  margin-right: 20px;
}

.font-size-12 {
  font-size: 12px !important;
}

.font-size-14 {
  font-size: 14px !important;
}

.font-size-16 {
  font-size: 16px !important;
}

.font-weight-bold {
  font-weight: bold !important;
}

.color-danger {
  color: #ff4d4f !important;
}

/* Ant Design 样式覆盖 */
:deep(.ant-space) {
  display: flex;
}

:deep(.ant-space-vertical) {
  flex-direction: column;
}

:deep(.ant-space-horizontal) {
  flex-direction: row;
}

:deep(.ant-space-align-start) {
  align-items: flex-start;
}

:deep(.ant-space-align-center) {
  align-items: center;
}

:deep(.ant-space-item) {
  display: flex;
}

:deep(.ant-typography-secondary) {
  color: rgba(0, 0, 0, 0.45) !important;
}

:deep(.ant-input-number-group-wrapper) {
  display: inline-block;
  width: 100%;
  text-align: start;
  vertical-align: top;
}

:deep(.ant-input-number-group) {
  position: relative;
  display: table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

:deep(.ant-input-number-group-addon) {
  position: relative;
  padding: 0 11px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
  display: table-cell;
  width: 1px;
  white-space: nowrap;
  vertical-align: middle;
}

:deep(.ant-input-number-affix-wrapper) {
  position: relative;
  display: inline-block;
  width: 100%;
  min-width: 0;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 1.5714285714285714;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.2s;
}

:deep(.ant-input-number-prefix) {
  display: flex;
  flex: none;
  align-items: center;
  margin-inline-end: 4px;
}

:deep(.ant-checkbox-wrapper) {
  display: inline-flex;
  align-items: baseline;
  line-height: unset;
  cursor: pointer;
}

:deep(.ant-checkbox) {
  position: relative;
  top: 0.2em;
  line-height: 1;
  white-space: nowrap;
  outline: none;
  cursor: pointer;
}

:deep(.ant-checkbox-inner) {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  width: 16px;
  height: 16px;
  direction: ltr;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  border-collapse: separate;
  transition: all 0.3s;
}
</style>
